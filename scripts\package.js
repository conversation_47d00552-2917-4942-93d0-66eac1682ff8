const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

async function createPackage() {
  try {
    console.log('📦 开始打包 AI Coach Advanced...');

    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const version = packageJson.version;
    const packageName = `ai-coach-advanced-v${version}.zip`;
    
    // 确保dist目录存在
    if (!fs.existsSync('dist')) {
      console.error('❌ dist目录不存在，请先运行构建命令');
      process.exit(1);
    }

    // 创建releases目录
    const releasesDir = 'releases';
    if (!fs.existsSync(releasesDir)) {
      fs.mkdirSync(releasesDir);
    }

    // 创建zip文件
    const output = fs.createWriteStream(path.join(releasesDir, packageName));
    const archive = archiver('zip', {
      zlib: { level: 9 } // 最高压缩级别
    });

    output.on('close', () => {
      const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
      console.log('✅ 打包完成!');
      console.log(`📦 文件: ${packageName}`);
      console.log(`📏 大小: ${sizeInMB} MB`);
      console.log(`📁 位置: ${path.join(releasesDir, packageName)}`);
    });

    archive.on('error', (err) => {
      throw err;
    });

    archive.pipe(output);

    // 添加dist目录中的所有文件
    archive.directory('dist/', false);

    // 添加额外的文档文件
    const docsToInclude = [
      'docs/README.md',
      'docs/INSTALLATION.md',
      'docs/USER_GUIDE.md',
      'docs/CHANGELOG.md',
      'docs/PERFORMANCE_OPTIMIZATION.md',
      'docs/SECURITY_CHECKLIST.md',
      'docs/TOOL_CALLING_GUIDE.md',
      'docs/DEBUG_TOOL_CALLING.md',
      'docs/FILE_OPERATIONS_GUIDE.md',
      'docs/SIDEBAR_MEMORY_GUIDE.md',
      'docs/BUGFIX_GUIDE.md',
      'docs/FINAL_FIX_SUMMARY.md',
      'docs/PROJECT_COMPLETION_SUMMARY.md'
    ];

    for (const doc of docsToInclude) {
      if (fs.existsSync(doc)) {
        archive.file(doc, { name: path.basename(doc) });
        console.log(`📄 添加文档: ${doc}`);
      }
    }

    // 生成发布说明
    const releaseNotes = generateReleaseNotes(version);
    archive.append(releaseNotes, { name: 'RELEASE_NOTES.md' });

    await archive.finalize();

  } catch (error) {
    console.error('❌ 打包失败:', error);
    process.exit(1);
  }
}

function generateReleaseNotes(version) {
  return `# AI Coach Advanced v${version} 发布说明

## 🎉 新功能

### 核心功能
- ✅ 多LLM提供商支持（OpenAI、Google Gemini、DeepSeek等）
- ✅ 智能任务规划（ReAct模式）
- ✅ 强大的工具调用框架
- ✅ 安全的JavaScript执行环境
- ✅ Vault语义搜索
- ✅ 网络搜索集成
- ✅ 插件管理工具
- ✅ 记忆管理系统

### 用户界面
- ✅ 直观的对话界面
- ✅ 实时状态反馈
- ✅ 完整的设置页面
- ✅ 命令面板集成

### 安全性
- ✅ 沙箱执行环境
- ✅ 权限管理系统
- ✅ 输入验证和过滤
- ✅ 安全的API调用

## 📋 安装说明

1. 下载 \`ai-coach-advanced-v${version}.zip\`
2. 解压到 Obsidian 插件目录
3. 在 Obsidian 设置中启用插件
4. 配置 LLM API 密钥
5. 开始使用！

## 🔧 配置要求

- Obsidian 版本: 1.0.0+
- Node.js 版本: 16.0.0+（开发环境）
- 支持的 LLM 提供商:
  - OpenAI (GPT-3.5, GPT-4)
  - Google Gemini
  - DeepSeek
  - 其他 OpenAI 兼容接口

## 📚 文档

- \`README.md\` - 基本介绍
- \`INSTALLATION.md\` - 详细安装指南
- \`USER_GUIDE.md\` - 用户使用指南
- \`PERFORMANCE_OPTIMIZATION.md\` - 性能优化指南
- \`SECURITY_CHECKLIST.md\` - 安全检查清单

## 🐛 已知问题

- 某些复杂的JavaScript代码可能需要多次尝试
- 大型Vault的初始索引可能需要较长时间
- 网络搜索结果的质量取决于搜索引擎API

## 🔄 更新日志

### v${version}
- 初始发布版本
- 实现所有核心功能
- 完成用户界面
- 添加安全保护机制

## 💬 支持

如果遇到问题或有建议，请：
1. 查看文档
2. 检查设置配置
3. 提交 Issue 或反馈

---

感谢使用 AI Coach Advanced！🚀
`;
}

createPackage();
