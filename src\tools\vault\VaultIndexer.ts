import { App, TFile, Vault } from 'obsidian';
import { VaultFile, VaultIndex, VaultIndexMetadata, VaultIndexer as IVaultIndexer, VaultIndexStats } from '@/types/vault';

/**
 * Vault索引器
 * 负责文件内容的分块、索引和存储
 */
export class VaultIndexer implements IVaultIndexer {
  private app: App;
  private vault: Vault;
  private indices: Map<string, VaultIndex[]> = new Map();
  private chunkSize: number = 1000;
  private overlapSize: number = 200;

  constructor(app: App, chunkSize: number = 1000) {
    this.app = app;
    this.vault = app.vault;
    this.chunkSize = chunkSize;
    this.overlapSize = Math.min(200, Math.floor(chunkSize * 0.2));
  }

  /**
   * 索引单个文件
   */
  async indexFile(file: VaultFile): Promise<VaultIndex[]> {
    try {
      const content = file.content || await this.readFileContent(file.path);
      if (!content) {
        return [];
      }

      // 提取元数据
      const metadata = this.extractMetadata(content, file);
      
      // 分块处理
      const chunks = this.chunkText(content);
      const indices: VaultIndex[] = [];

      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        const index: VaultIndex = {
          id: this.generateIndexId(file.path, i),
          filePath: file.path,
          content: chunk,
          embedding: undefined, // 将在后续步骤中生成
          metadata: {
            ...metadata,
            chunkIndex: i,
            totalChunks: chunks.length,
            wordCount: this.countWords(chunk)
          },
          createdAt: new Date(),
          updatedAt: new Date()
        };

        indices.push(index);
      }

      // 存储索引
      this.indices.set(file.path, indices);
      
      return indices;
    } catch (error) {
      console.error(`Failed to index file ${file.path}:`, error);
      return [];
    }
  }

  /**
   * 更新文件索引
   */
  async updateIndex(filePath: string): Promise<void> {
    try {
      const file = this.vault.getAbstractFileByPath(filePath);
      if (!(file instanceof TFile)) {
        throw new Error(`File not found: ${filePath}`);
      }

      const vaultFile: VaultFile = {
        path: filePath,
        name: file.name,
        extension: file.extension,
        size: file.stat.size,
        mtime: file.stat.mtime
      };

      await this.indexFile(vaultFile);
    } catch (error) {
      console.error(`Failed to update index for ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * 删除文件索引
   */
  async deleteIndex(filePath: string): Promise<void> {
    this.indices.delete(filePath);
  }

  /**
   * 搜索索引（基础文本搜索）
   */
  async search(options: any): Promise<any[]> {
    const { query, maxResults = 10, threshold = 0.5 } = options;
    const results: any[] = [];

    for (const [filePath, fileIndices] of this.indices.entries()) {
      for (const index of fileIndices) {
        const score = this.calculateTextSimilarity(query, index.content);
        if (score >= threshold) {
          results.push({
            file: { path: filePath },
            content: index.content,
            score,
            highlights: this.extractHighlights(query, index.content),
            metadata: index.metadata
          });
        }
      }
    }

    // 按分数排序并限制结果数量
    results.sort((a, b) => b.score - a.score);
    return results.slice(0, maxResults);
  }

  /**
   * 获取索引统计信息
   */
  async getStats(): Promise<VaultIndexStats> {
    const totalFiles = this.indices.size;
    let totalChunks = 0;
    let indexSize = 0;

    for (const fileIndices of this.indices.values()) {
      totalChunks += fileIndices.length;
      indexSize += fileIndices.reduce((sum, index) => sum + index.content.length, 0);
    }

    return {
      totalFiles,
      indexedFiles: totalFiles,
      totalChunks,
      lastUpdated: new Date(),
      indexSize
    };
  }

  /**
   * 读取文件内容
   */
  private async readFileContent(filePath: string): Promise<string> {
    try {
      const file = this.vault.getAbstractFileByPath(filePath);
      if (!(file instanceof TFile)) {
        throw new Error(`File not found: ${filePath}`);
      }
      return await this.vault.read(file);
    } catch (error) {
      console.error(`Failed to read file ${filePath}:`, error);
      return '';
    }
  }

  /**
   * 提取文件元数据
   */
  private extractMetadata(content: string, file: VaultFile): Omit<VaultIndexMetadata, 'chunkIndex' | 'totalChunks' | 'wordCount'> {
    const metadata: Omit<VaultIndexMetadata, 'chunkIndex' | 'totalChunks' | 'wordCount'> = {
      title: file.name.replace(/\.[^/.]+$/, ''), // 移除扩展名
      tags: [],
      links: [],
      headings: []
    };

    // 提取标题（从frontmatter或第一个H1）
    const titleMatch = content.match(/^#\s+(.+)$/m);
    if (titleMatch) {
      metadata.title = titleMatch[1].trim();
    }

    // 提取标签
    const tagMatches = content.match(/#[\w\-_]+/g);
    if (tagMatches) {
      metadata.tags = [...new Set(tagMatches.map(tag => tag.slice(1)))];
    }

    // 提取链接
    const linkMatches = content.match(/\[\[([^\]]+)\]\]/g);
    if (linkMatches) {
      metadata.links = [...new Set(linkMatches.map(link => link.slice(2, -2)))];
    }

    // 提取标题
    const headingMatches = content.match(/^#{1,6}\s+(.+)$/gm);
    if (headingMatches) {
      metadata.headings = headingMatches.map(heading => {
        const level = heading.match(/^#+/)?.[0].length || 1;
        const text = heading.replace(/^#+\s+/, '');
        return `${'#'.repeat(level)} ${text}`;
      });
    }

    return metadata;
  }

  /**
   * 文本分块
   */
  private chunkText(text: string): string[] {
    const chunks: string[] = [];
    const sentences = this.splitIntoSentences(text);
    
    let currentChunk = '';
    let currentLength = 0;

    for (const sentence of sentences) {
      const sentenceLength = sentence.length;
      
      // 如果当前块加上新句子超过了块大小
      if (currentLength + sentenceLength > this.chunkSize && currentChunk) {
        chunks.push(currentChunk.trim());
        
        // 开始新块，包含重叠内容
        const overlapText = this.getOverlapText(currentChunk);
        currentChunk = overlapText + sentence;
        currentLength = currentChunk.length;
      } else {
        currentChunk += sentence;
        currentLength += sentenceLength;
      }
    }

    // 添加最后一个块
    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }

    return chunks.filter(chunk => chunk.length > 50); // 过滤太短的块
  }

  /**
   * 将文本分割为句子
   */
  private splitIntoSentences(text: string): string[] {
    // 简单的句子分割，可以根据需要改进
    return text
      .split(/[.!?]+/)
      .map(sentence => sentence.trim())
      .filter(sentence => sentence.length > 0)
      .map(sentence => sentence + '. ');
  }

  /**
   * 获取重叠文本
   */
  private getOverlapText(text: string): string {
    if (text.length <= this.overlapSize) {
      return text;
    }
    
    const overlap = text.slice(-this.overlapSize);
    // 尝试在单词边界处截断
    const lastSpaceIndex = overlap.lastIndexOf(' ');
    if (lastSpaceIndex > this.overlapSize * 0.5) {
      return overlap.slice(lastSpaceIndex + 1);
    }
    
    return overlap;
  }

  /**
   * 计算文本相似度（简单的关键词匹配）
   */
  private calculateTextSimilarity(query: string, text: string): number {
    const queryWords = query.toLowerCase().split(/\s+/).filter(word => word.length > 2);
    const textWords = text.toLowerCase().split(/\s+/);
    
    if (queryWords.length === 0) return 0;
    
    let matches = 0;
    for (const queryWord of queryWords) {
      if (textWords.some(textWord => textWord.includes(queryWord))) {
        matches++;
      }
    }
    
    return matches / queryWords.length;
  }

  /**
   * 提取高亮片段
   */
  private extractHighlights(query: string, text: string, maxHighlights: number = 3): string[] {
    const queryWords = query.toLowerCase().split(/\s+/).filter(word => word.length > 2);
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const highlights: string[] = [];
    
    for (const sentence of sentences) {
      const sentenceLower = sentence.toLowerCase();
      const hasMatch = queryWords.some(word => sentenceLower.includes(word));
      
      if (hasMatch && highlights.length < maxHighlights) {
        highlights.push(sentence.trim() + '...');
      }
    }
    
    return highlights;
  }

  /**
   * 统计单词数
   */
  private countWords(text: string): number {
    return text.split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * 生成索引ID
   */
  private generateIndexId(filePath: string, chunkIndex: number): string {
    return `${filePath}#${chunkIndex}`;
  }

  /**
   * 获取所有索引
   */
  getAllIndices(): Map<string, VaultIndex[]> {
    return new Map(this.indices);
  }

  /**
   * 清空所有索引
   */
  clearAllIndices(): void {
    this.indices.clear();
  }

  /**
   * 批量索引文件
   */
  async indexFiles(files: VaultFile[]): Promise<void> {
    const batchSize = 10;
    for (let i = 0; i < files.length; i += batchSize) {
      const batch = files.slice(i, i + batchSize);
      await Promise.all(batch.map(file => this.indexFile(file)));
    }
  }
}
