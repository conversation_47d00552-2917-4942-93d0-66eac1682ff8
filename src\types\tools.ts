// 工具相关类型定义

export interface Tool {
  name: string;
  description: string;
  category: ToolCategory;
  version: string;
  parameters: ToolParameters;
  permissions: ToolPermissions;
  metadata: ToolMetadata;
  execute(args: Record<string, any>, context?: ToolExecutionContext): Promise<ToolResult>;
  validate?(args: Record<string, any>): Promise<ToolValidationResult>;
  initialize?(): Promise<void>;
  cleanup?(): Promise<void>;
}

export interface ToolParameters {
  type: 'object';
  properties: Record<string, ToolParameter>;
  required?: string[];
}

export interface ToolParameter {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description: string;
  enum?: any[];
  items?: ToolParameter;
  properties?: Record<string, ToolParameter>;
  default?: any;
  minimum?: number;
  maximum?: number;
  pattern?: string;
}

export interface ToolResult {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: ToolResultMetadata;
}

export interface ToolResultMetadata {
  executionTime?: number;
  tokensUsed?: number;
  cacheHit?: boolean;
  source?: string;
  confidence?: number;
  [key: string]: any;
}

export interface ToolExecutionContext {
  userId?: string;
  sessionId?: string;
  requestId?: string;
  timestamp: Date;
  permissions: string[];
  config: Record<string, any>;
}

export interface ToolValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ToolPermissions {
  required: string[];
  optional: string[];
  dangerous: boolean;
  requiresConfirmation: boolean;
}

export interface ToolMetadata {
  author: string;
  tags: string[];
  documentation?: string;
  examples?: ToolExample[];
  dependencies?: string[];
  supportedPlatforms?: string[];
}

export interface ToolExample {
  name: string;
  description: string;
  input: Record<string, any>;
  expectedOutput: any;
}

export type ToolCategory =
  | 'vault'
  | 'web'
  | 'plugin'
  | 'javascript'
  | 'memory'
  | 'utility'
  | 'ai'
  | 'custom';

export interface ToolRegistry {
  register(tool: Tool): Promise<void>;
  unregister(name: string): Promise<void>;
  get(name: string): Tool | undefined;
  list(category?: ToolCategory): Tool[];
  execute(name: string, args: Record<string, any>, context?: ToolExecutionContext): Promise<ToolResult>;
  validate(name: string, args: Record<string, any>): Promise<ToolValidationResult>;
  getToolDefinition(name: string): ToolDefinition | undefined;
  getToolDefinitions(category?: ToolCategory): ToolDefinition[];
}

export interface ToolDefinition {
  name: string;
  description: string;
  category: ToolCategory;
  parameters: ToolParameters;
  permissions: ToolPermissions;
  metadata: ToolMetadata;
}

// 具体工具类型
export interface VaultQueryResult {
  content: string;
  file: string;
  score: number;
  metadata?: Record<string, any>;
}

export interface WebSearchResult {
  title: string;
  url: string;
  snippet: string;
  score?: number;
}

export interface PluginInfo {
  id: string;
  name: string;
  version: string;
  enabled: boolean;
  description?: string;
  author?: string;
  authorUrl?: string;
  minAppVersion?: string;
  commands?: PluginCommand[];
}

export interface PluginCommand {
  id: string;
  name: string;
  description?: string;
  callback?: string;
  hotkeys?: PluginHotkey[];
}

export interface PluginHotkey {
  modifiers: string[];
  key: string;
}

export interface WebSearchResult {
  title: string;
  url: string;
  snippet: string;
  score: number;
  metadata?: {
    publishDate?: string;
    author?: string;
    domain?: string;
  };
}

export interface VaultQueryResult {
  content: string;
  file: string;
  score: number;
  metadata: {
    highlights: string[];
    [key: string]: any;
  };
}

export interface JSExecutionResult {
  output: any;
  logs: string[];
  errors: string[];
  executionTime: number;
}
