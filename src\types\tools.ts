// 工具相关类型定义

export interface Tool {
  name: string;
  description: string;
  parameters: ToolParameters;
  execute(args: Record<string, any>): Promise<ToolResult>;
}

export interface ToolParameters {
  type: 'object';
  properties: Record<string, ToolParameter>;
  required?: string[];
}

export interface ToolParameter {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description: string;
  enum?: any[];
  items?: ToolParameter;
  properties?: Record<string, ToolParameter>;
}

export interface ToolResult {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: Record<string, any>;
}

export interface ToolRegistry {
  register(tool: Tool): void;
  unregister(name: string): void;
  get(name: string): Tool | undefined;
  list(): Tool[];
  execute(name: string, args: Record<string, any>): Promise<ToolResult>;
}

// 具体工具类型
export interface VaultQueryResult {
  content: string;
  file: string;
  score: number;
  metadata?: Record<string, any>;
}

export interface WebSearchResult {
  title: string;
  url: string;
  snippet: string;
  score?: number;
}

export interface PluginInfo {
  id: string;
  name: string;
  version: string;
  enabled: boolean;
  commands?: PluginCommand[];
}

export interface PluginCommand {
  id: string;
  name: string;
  description?: string;
  callback?: string;
}

export interface JSExecutionResult {
  output: any;
  logs: string[];
  errors: string[];
  executionTime: number;
}
