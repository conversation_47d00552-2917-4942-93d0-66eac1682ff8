import { LLMInterface } from '@/core/llm/LLMInterface';
import { PromptManager } from '@/utils/prompts/PromptManager';
import { ToolRegistry } from '@/core/tools/ToolRegistry';
import { Tool, ToolResult } from '@/types/tools';
import { LLMMessage, LLMResponse } from '@/types/llm';

/**
 * 任务规划器
 * 实现ReAct（Reasoning and Acting）模式的任务规划和执行
 */
export class TaskPlanner {
  private llm: LLMInterface;
  private promptManager: PromptManager;
  private toolRegistry: ToolRegistry;
  private maxIterations: number = 10;
  private maxThinkingSteps: number = 3;

  constructor(
    llm: LLMInterface,
    promptManager: PromptManager,
    toolRegistry: ToolRegistry
  ) {
    this.llm = llm;
    this.promptManager = promptManager;
    this.toolRegistry = toolRegistry;
  }

  /**
   * 执行任务规划和执行
   */
  async planAndExecute(userInput: string, context?: PlanningContext): Promise<TaskExecutionResult> {
    const startTime = Date.now();
    const executionLog: ExecutionStep[] = [];
    
    try {
      // 1. 初始任务分析
      const taskAnalysis = await this.analyzeTask(userInput, context);
      executionLog.push({
        type: 'analysis',
        content: taskAnalysis.analysis,
        timestamp: new Date()
      });

      if (!taskAnalysis.executable) {
        return {
          success: false,
          result: taskAnalysis.analysis,
          executionLog,
          executionTime: Date.now() - startTime,
          error: '任务无法执行'
        };
      }

      // 2. ReAct循环执行
      const reactResult = await this.executeReActLoop(userInput, taskAnalysis, executionLog);
      
      return {
        success: reactResult.success,
        result: reactResult.finalAnswer,
        executionLog,
        executionTime: Date.now() - startTime,
        toolsUsed: reactResult.toolsUsed,
        iterations: reactResult.iterations
      };

    } catch (error) {
      executionLog.push({
        type: 'error',
        content: `执行错误: ${error.message}`,
        timestamp: new Date()
      });

      return {
        success: false,
        result: '任务执行过程中发生错误',
        executionLog,
        executionTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * 分析任务
   */
  private async analyzeTask(userInput: string, context?: PlanningContext): Promise<TaskAnalysis> {
    const analysisPrompt = this.promptManager.generateTaskAnalysisPrompt(
      userInput,
      context?.conversationHistory
    );

    const messages: LLMMessage[] = [
      { role: 'system', content: analysisPrompt },
      { role: 'user', content: userInput }
    ];

    const response = await this.llm.generateText(messages);
    
    return this.parseTaskAnalysis(response.content);
  }

  /**
   * 执行ReAct循环
   */
  private async executeReActLoop(
    userInput: string,
    taskAnalysis: TaskAnalysis,
    executionLog: ExecutionStep[]
  ): Promise<ReActResult> {
    const availableTools = this.toolRegistry.list();
    const toolsUsed: string[] = [];
    let currentContext = userInput;
    let finalAnswer = '';
    let iterations = 0;

    for (let i = 0; i < this.maxIterations; i++) {
      iterations++;
      
      // Thought: 思考下一步行动
      const thought = await this.generateThought(currentContext, availableTools, executionLog);
      executionLog.push({
        type: 'thought',
        content: thought,
        timestamp: new Date()
      });

      // Action: 决定采取的行动
      const action = await this.generateAction(thought, availableTools);
      executionLog.push({
        type: 'action',
        content: `计划执行: ${action.type} - ${action.description}`,
        timestamp: new Date()
      });

      // 执行行动
      if (action.type === 'tool_call') {
        // 调用工具
        const toolResult = await this.executeTool(action.toolName!, action.parameters!);
        executionLog.push({
          type: 'tool_result',
          content: `工具 ${action.toolName} 执行结果: ${JSON.stringify(toolResult.data)}`,
          timestamp: new Date(),
          toolName: action.toolName,
          toolResult
        });

        if (!toolsUsed.includes(action.toolName!)) {
          toolsUsed.push(action.toolName!);
        }

        // 更新上下文
        currentContext += `\n\n工具执行结果:\n${JSON.stringify(toolResult.data)}`;

      } else if (action.type === 'final_answer') {
        // 生成最终答案
        finalAnswer = action.answer!;
        executionLog.push({
          type: 'final_answer',
          content: finalAnswer,
          timestamp: new Date()
        });
        break;

      } else if (action.type === 'need_more_info') {
        // 需要更多信息
        finalAnswer = action.question!;
        executionLog.push({
          type: 'clarification',
          content: finalAnswer,
          timestamp: new Date()
        });
        break;
      }

      // 检查是否应该继续
      if (await this.shouldStop(currentContext, executionLog)) {
        finalAnswer = await this.generateFinalAnswer(currentContext, executionLog);
        break;
      }
    }

    if (iterations >= this.maxIterations && !finalAnswer) {
      finalAnswer = '任务执行超过最大迭代次数，请简化任务或提供更明确的指令。';
    }

    return {
      success: !!finalAnswer,
      finalAnswer,
      toolsUsed,
      iterations
    };
  }

  /**
   * 生成思考内容
   */
  private async generateThought(
    context: string,
    availableTools: Tool[],
    executionLog: ExecutionStep[]
  ): Promise<string> {
    const systemPrompt = this.promptManager.generateSystemPrompt();
    const toolDescriptions = availableTools.map(tool => 
      `${tool.name}: ${tool.description}`
    ).join('\n');

    const recentSteps = executionLog.slice(-5).map(step => 
      `${step.type}: ${step.content}`
    ).join('\n');

    const prompt = `
基于当前情况，思考下一步应该采取什么行动：

当前上下文：
${context}

可用工具：
${toolDescriptions}

最近的执行步骤：
${recentSteps}

请分析当前情况并思考下一步的最佳行动方案。`;

    const messages: LLMMessage[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: prompt }
    ];

    const response = await this.llm.generateText(messages);
    return response.content;
  }

  /**
   * 生成行动计划
   */
  private async generateAction(thought: string, availableTools: Tool[]): Promise<ActionPlan> {
    const toolDescriptions = availableTools.map(tool => ({
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters
    }));

    const prompt = `
基于以下思考内容，决定下一步的具体行动：

思考内容：
${thought}

可用工具：
${JSON.stringify(toolDescriptions, null, 2)}

请选择以下行动类型之一：
1. tool_call - 调用特定工具
2. final_answer - 提供最终答案
3. need_more_info - 需要用户提供更多信息

请以JSON格式返回行动计划：
{
  "type": "tool_call|final_answer|need_more_info",
  "description": "行动描述",
  "toolName": "工具名称（仅tool_call时需要）",
  "parameters": "工具参数（仅tool_call时需要）",
  "answer": "最终答案（仅final_answer时需要）",
  "question": "需要询问的问题（仅need_more_info时需要）"
}`;

    const messages: LLMMessage[] = [
      { role: 'user', content: prompt }
    ];

    const response = await this.llm.generateText(messages);
    
    try {
      return JSON.parse(response.content);
    } catch (error) {
      // 如果解析失败，返回默认的最终答案行动
      return {
        type: 'final_answer',
        description: '解析行动计划失败，提供基于思考的答案',
        answer: thought
      };
    }
  }

  /**
   * 执行工具
   */
  private async executeTool(toolName: string, parameters: any): Promise<ToolResult> {
    try {
      return await this.toolRegistry.execute(toolName, parameters);
    } catch (error) {
      return {
        success: false,
        error: `工具执行失败: ${error.message}`
      };
    }
  }

  /**
   * 判断是否应该停止
   */
  private async shouldStop(context: string, executionLog: ExecutionStep[]): Promise<boolean> {
    // 简单的停止条件
    const recentSteps = executionLog.slice(-3);
    
    // 如果最近有最终答案，停止
    if (recentSteps.some(step => step.type === 'final_answer')) {
      return true;
    }

    // 如果最近的工具调用都失败了，停止
    const recentToolCalls = recentSteps.filter(step => step.type === 'tool_result');
    if (recentToolCalls.length >= 2 && 
        recentToolCalls.every(step => !step.toolResult?.success)) {
      return true;
    }

    return false;
  }

  /**
   * 生成最终答案
   */
  private async generateFinalAnswer(context: string, executionLog: ExecutionStep[]): Promise<string> {
    const prompt = this.promptManager.generateContentSummaryPrompt(
      context,
      executionLog.map(step => ({
        source: step.type,
        content: step.content,
        score: 1.0
      }))
    );

    const messages: LLMMessage[] = [
      { role: 'user', content: prompt }
    ];

    const response = await this.llm.generateText(messages);
    return response.content;
  }

  /**
   * 解析任务分析结果
   */
  private parseTaskAnalysis(content: string): TaskAnalysis {
    // 简单的解析逻辑，实际应该更复杂
    const executable = !content.toLowerCase().includes('无法') && 
                     !content.toLowerCase().includes('不能') &&
                     !content.toLowerCase().includes('无效');

    return {
      analysis: content,
      executable,
      complexity: this.estimateComplexity(content),
      requiredTools: this.extractRequiredTools(content)
    };
  }

  /**
   * 估算任务复杂度
   */
  private estimateComplexity(analysis: string): 'low' | 'medium' | 'high' {
    const complexityIndicators = [
      '多步骤', '复杂', '需要', '分析', '处理', '生成', '搜索', '计算'
    ];
    
    const indicatorCount = complexityIndicators.filter(indicator => 
      analysis.includes(indicator)
    ).length;

    if (indicatorCount <= 2) return 'low';
    if (indicatorCount <= 4) return 'medium';
    return 'high';
  }

  /**
   * 提取所需工具
   */
  private extractRequiredTools(analysis: string): string[] {
    const availableTools = this.toolRegistry.list();
    const requiredTools: string[] = [];

    for (const tool of availableTools) {
      if (analysis.toLowerCase().includes(tool.name.toLowerCase()) ||
          analysis.toLowerCase().includes(tool.description.toLowerCase())) {
        requiredTools.push(tool.name);
      }
    }

    return requiredTools;
  }
}

/**
 * 规划上下文
 */
export interface PlanningContext {
  conversationHistory?: string;
  userPreferences?: Record<string, any>;
  availableTools?: string[];
}

/**
 * 任务分析结果
 */
export interface TaskAnalysis {
  analysis: string;
  executable: boolean;
  complexity: 'low' | 'medium' | 'high';
  requiredTools: string[];
}

/**
 * 行动计划
 */
export interface ActionPlan {
  type: 'tool_call' | 'final_answer' | 'need_more_info';
  description: string;
  toolName?: string;
  parameters?: any;
  answer?: string;
  question?: string;
}

/**
 * 执行步骤
 */
export interface ExecutionStep {
  type: 'analysis' | 'thought' | 'action' | 'tool_result' | 'final_answer' | 'error' | 'clarification';
  content: string;
  timestamp: Date;
  toolName?: string;
  toolResult?: ToolResult;
}

/**
 * ReAct执行结果
 */
export interface ReActResult {
  success: boolean;
  finalAnswer: string;
  toolsUsed: string[];
  iterations: number;
}

/**
 * 任务执行结果
 */
export interface TaskExecutionResult {
  success: boolean;
  result: string;
  executionLog: ExecutionStep[];
  executionTime: number;
  toolsUsed?: string[];
  iterations?: number;
  error?: string;
}
