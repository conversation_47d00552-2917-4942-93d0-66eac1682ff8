import { LLMInterface } from '@/core/llm/LLMInterface';
import { PromptManager } from '@/utils/prompts/PromptManager';
import { ToolRegistry } from '@/core/tools/ToolRegistry';
import { Tool, ToolResult } from '@/types/tools';
import { LLMMessage, LLMResponse } from '@/types/llm';

/**
 * 任务规划器
 * 实现ReAct（Reasoning and Acting）模式的任务规划和执行
 */
export class TaskPlanner {
  private llm: LLMInterface;
  private promptManager: PromptManager;
  private toolRegistry: ToolRegistry;
  private maxIterations: number = 10;
  private maxThinkingSteps: number = 3;
  private currentUserInput: string = '';

  constructor(
    llm: LLMInterface,
    promptManager: PromptManager,
    toolRegistry: ToolRegistry
  ) {
    this.llm = llm;
    this.promptManager = promptManager;
    this.toolRegistry = toolRegistry;
  }

  /**
   * 执行任务规划和执行
   */
  async planAndExecute(userInput: string, context?: PlanningContext): Promise<TaskExecutionResult> {
    const startTime = Date.now();
    const executionLog: ExecutionStep[] = [];

    // 保存用户输入以供后续使用
    this.currentUserInput = userInput;

    console.log('🚀 TaskPlanner.planAndExecute 开始执行');
    console.log('📝 用户输入:', userInput);
    console.log('🔧 可用工具数量:', this.toolRegistry.list().length);
    console.log('🔧 可用工具列表:', this.toolRegistry.list().map(t => t.name));

    try {
      // 1. 初始任务分析
      console.log('📋 开始任务分析...');
      const taskAnalysis = await this.analyzeTask(userInput, context);
      console.log('📋 任务分析结果:', {
        executable: taskAnalysis.executable,
        complexity: taskAnalysis.complexity,
        requiredTools: taskAnalysis.requiredTools
      });
      console.log('📄 分析内容:', taskAnalysis.analysis);

      executionLog.push({
        type: 'analysis',
        content: taskAnalysis.analysis,
        timestamp: new Date()
      });

      if (!taskAnalysis.executable) {
        console.log('❌ 任务被判定为不可执行，直接返回');
        return {
          success: false,
          result: taskAnalysis.analysis,
          executionLog,
          executionTime: Date.now() - startTime,
          error: '任务无法执行'
        };
      }

      // 2. ReAct循环执行
      console.log('🔄 开始ReAct循环执行...');
      const reactResult = await this.executeReActLoop(userInput, taskAnalysis, executionLog);
      console.log('🔄 ReAct循环执行完成:', {
        success: reactResult.success,
        toolsUsed: reactResult.toolsUsed,
        iterations: reactResult.iterations
      });
      
      return {
        success: reactResult.success,
        result: reactResult.finalAnswer,
        executionLog,
        executionTime: Date.now() - startTime,
        toolsUsed: reactResult.toolsUsed,
        iterations: reactResult.iterations
      };

    } catch (error) {
      executionLog.push({
        type: 'error',
        content: `执行错误: ${error.message}`,
        timestamp: new Date()
      });

      return {
        success: false,
        result: '任务执行过程中发生错误',
        executionLog,
        executionTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * 分析任务
   */
  private async analyzeTask(userInput: string, context?: PlanningContext): Promise<TaskAnalysis> {
    const analysisPrompt = this.promptManager.generateTaskAnalysisPrompt(
      userInput,
      context?.conversationHistory
    );

    const messages: LLMMessage[] = [
      { role: 'system', content: analysisPrompt },
      { role: 'user', content: userInput }
    ];

    const response = await this.llm.generateText(messages);
    
    return this.parseTaskAnalysis(response.content);
  }

  /**
   * 执行ReAct循环
   */
  private async executeReActLoop(
    userInput: string,
    taskAnalysis: TaskAnalysis,
    executionLog: ExecutionStep[]
  ): Promise<ReActResult> {
    const availableTools = this.toolRegistry.list();
    const toolsUsed: string[] = [];
    let currentContext = userInput;
    let finalAnswer = '';
    let iterations = 0;

    console.log('🔄 开始ReAct循环，最大迭代次数:', this.maxIterations);

    for (let i = 0; i < this.maxIterations; i++) {
      iterations++;
      console.log(`\n🔄 === 第${iterations}轮迭代开始 ===`);

      // Thought: 思考下一步行动
      console.log('💭 生成思考...');
      const thought = await this.generateThought(currentContext, availableTools, executionLog);
      console.log('💭 思考结果:', thought.substring(0, 200) + '...');

      executionLog.push({
        type: 'thought',
        content: thought,
        timestamp: new Date()
      });

      // Action: 决定采取的行动
      console.log('🎯 生成行动计划...');
      const action = await this.generateAction(thought, availableTools);
      console.log('🎯 行动计划:', action);

      executionLog.push({
        type: 'action',
        content: `计划执行: ${action.type} - ${action.description}`,
        timestamp: new Date()
      });

      // 执行行动
      console.log('⚡ 执行行动，类型:', action.type);

      if (action.type === 'tool_call') {
        console.log('🔧 准备调用工具:', action.toolName, '参数:', action.parameters);

        // 调用工具
        const toolResult = await this.executeTool(action.toolName!, action.parameters!);
        console.log('🔧 工具执行完成:', {
          success: toolResult.success,
          hasData: !!toolResult.data,
          hasError: !!toolResult.error
        });

        executionLog.push({
          type: 'tool_result',
          content: `工具 ${action.toolName} 执行结果: ${JSON.stringify(toolResult.data)}`,
          timestamp: new Date(),
          toolName: action.toolName,
          toolResult
        });

        if (!toolsUsed.includes(action.toolName!)) {
          toolsUsed.push(action.toolName!);
          console.log('📝 添加到已使用工具列表:', action.toolName);
        }

        // 更新上下文
        currentContext += `\n\n工具执行结果:\n${JSON.stringify(toolResult.data)}`;

      } else if (action.type === 'final_answer') {
        console.log('✅ 生成最终答案');
        // 生成最终答案
        finalAnswer = action.answer!;
        executionLog.push({
          type: 'final_answer',
          content: finalAnswer,
          timestamp: new Date()
        });
        break;

      } else if (action.type === 'need_more_info') {
        console.log('❓ 需要更多信息');
        // 需要更多信息
        finalAnswer = action.question!;
        executionLog.push({
          type: 'clarification',
          content: finalAnswer,
          timestamp: new Date()
        });
        break;
      } else {
        console.log('⚠️ 未知的行动类型:', action.type);
      }

      // 检查是否应该继续
      if (await this.shouldStop(currentContext, executionLog)) {
        finalAnswer = await this.generateFinalAnswer(currentContext, executionLog);
        break;
      }
    }

    if (iterations >= this.maxIterations && !finalAnswer) {
      finalAnswer = '任务执行超过最大迭代次数，请简化任务或提供更明确的指令。';
    }

    return {
      success: !!finalAnswer,
      finalAnswer,
      toolsUsed,
      iterations
    };
  }

  /**
   * 生成思考内容
   */
  private async generateThought(
    context: string,
    availableTools: Tool[],
    executionLog: ExecutionStep[]
  ): Promise<string> {
    const systemPrompt = this.promptManager.generateSystemPrompt();
    const toolDescriptions = availableTools.map(tool => 
      `${tool.name}: ${tool.description}`
    ).join('\n');

    const recentSteps = executionLog.slice(-5).map(step => 
      `${step.type}: ${step.content}`
    ).join('\n');

    const prompt = `
你是一个智能助手，需要分析用户请求并决定如何帮助用户。

用户原始请求：${this.currentUserInput}

当前上下文：
${context}

可用工具：
${toolDescriptions}

最近的执行步骤：
${recentSteps}

请仔细分析用户的请求类型：
- 如果是计算、数学问题 → 需要使用 javascript_executor 工具
- 如果是搜索笔记、查找文档 → 需要使用 vault_query 工具
- 如果是网络搜索、最新信息 → 需要使用 web_search 工具
- 如果是插件操作 → 需要使用 plugin_manager 工具

请思考：用户想要什么？需要使用哪个工具？为什么？`;

    const messages: LLMMessage[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: prompt }
    ];

    const response = await this.llm.generateText(messages);
    return response.content;
  }

  /**
   * 生成行动计划
   */
  private async generateAction(thought: string, availableTools: Tool[]): Promise<ActionPlan> {
    const toolDescriptions = availableTools.map(tool => ({
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters
    }));

    const prompt = `
你需要根据用户的请求和当前思考，决定下一步的具体行动。

用户原始请求：${this.currentUserInput || ''}

当前思考：
${thought}

可用工具：
${JSON.stringify(toolDescriptions, null, 2)}

**工具选择指南**：
1. **vault_query** - 用于搜索Obsidian笔记内容
   - 关键词：搜索笔记、查找文档、我的笔记、vault内容

2. **web_search** - 用于网络搜索最新信息
   - 关键词：最新信息、网络搜索、实时数据、新闻

3. **javascript_executor** - 用于执行计算和代码
   - 关键词：计算、数学、代码执行、数据处理、编程

4. **plugin_manager** - 用于管理Obsidian插件
   - 关键词：插件、命令、Obsidian功能

5. **file_operation** - 用于文件和文件夹操作
   - 关键词：创建文件、删除文件、编辑文件、移动文件、新建文件夹

6. **note_template** - 用于创建模板化笔记
   - 关键词：创建笔记、模板、日记、会议记录、项目笔记

**决策规则**：
- 如果用户要求计算、数学运算 → 使用 javascript_executor
- 如果用户要求搜索笔记、查找文档 → 使用 vault_query
- 如果用户要求网络搜索、最新信息 → 使用 web_search
- 如果用户要求插件操作 → 使用 plugin_manager
- 如果用户要求文件操作（创建、删除、编辑、移动文件） → 使用 file_operation
- 如果用户要求创建特定类型的笔记（日记、会议记录等） → 使用 note_template
- 如果已经获得足够信息可以回答 → 使用 final_answer

**重要**：请严格按照以下要求返回：
1. 只返回纯JSON格式，不要使用markdown代码块（不要用三个反引号包裹）
2. 不要添加任何解释文字或其他内容
3. 确保JSON格式正确且可解析

返回格式：
{
  "type": "tool_call|final_answer|need_more_info",
  "description": "行动描述",
  "toolName": "工具名称（仅tool_call时需要）",
  "parameters": {"参数名": "参数值"},
  "answer": "最终答案（仅final_answer时需要）",
  "question": "需要询问的问题（仅need_more_info时需要）"
}`;

    const messages: LLMMessage[] = [
      { role: 'user', content: prompt }
    ];

    console.log('🤖 发送行动计划请求到LLM...');
    const response = await this.llm.generateText(messages);
    console.log('🤖 LLM行动计划原始响应:', response.content);

    try {
      // 清理响应内容，移除markdown代码块标记
      let cleanedContent = response.content.trim();

      // 移除markdown代码块标记
      if (cleanedContent.startsWith('```json')) {
        cleanedContent = cleanedContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        console.log('🧹 移除了markdown代码块标记');
      } else if (cleanedContent.startsWith('```')) {
        cleanedContent = cleanedContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
        console.log('🧹 移除了通用代码块标记');
      }

      console.log('🧹 清理后的内容:', cleanedContent);

      const parsedAction = JSON.parse(cleanedContent);
      console.log('✅ 行动计划解析成功:', parsedAction);

      // 验证必要字段
      if (!parsedAction.type) {
        throw new Error('缺少必要字段: type');
      }

      return parsedAction;
    } catch (error) {
      console.log('❌ 行动计划JSON解析失败:', error.message);
      console.log('📄 原始响应内容:', response.content);

      // 尝试从响应中提取工具调用信息
      const extractedAction = this.extractActionFromText(response.content);
      if (extractedAction) {
        console.log('🔧 从文本中提取到行动计划:', extractedAction);
        return extractedAction;
      }

      // 如果解析失败，返回默认的最终答案行动
      const fallbackAction = {
        type: 'final_answer',
        description: '解析行动计划失败，提供基于思考的答案',
        answer: thought
      };
      console.log('🔄 使用备用行动计划:', fallbackAction);
      return fallbackAction;
    }
  }

  /**
   * 从文本中提取行动计划
   */
  private extractActionFromText(text: string): any | null {
    console.log('🔍 尝试从文本中提取行动计划...');

    try {
      // 尝试匹配工具调用模式
      const toolCallPatterns = [
        /使用\s*(\w+)\s*工具/,
        /调用\s*(\w+)\s*工具/,
        /执行\s*(\w+)\s*工具/,
        /toolName["\s]*:\s*["\s]*(\w+)/,
        /web_search|vault_query|javascript_executor|plugin_manager|file_operation|note_template/
      ];

      for (const pattern of toolCallPatterns) {
        const match = text.match(pattern);
        if (match) {
          let toolName = match[1] || match[0];

          // 标准化工具名称
          if (toolName.includes('web_search') || text.includes('网络搜索') || text.includes('搜索')) {
            toolName = 'web_search';
          } else if (toolName.includes('vault_query') || text.includes('笔记') || text.includes('vault')) {
            toolName = 'vault_query';
          } else if (toolName.includes('javascript_executor') || text.includes('计算') || text.includes('代码')) {
            toolName = 'javascript_executor';
          } else if (toolName.includes('plugin_manager') || text.includes('插件')) {
            toolName = 'plugin_manager';
          } else if (toolName.includes('file_operation') || text.includes('创建文件') || text.includes('删除文件') || text.includes('编辑文件')) {
            toolName = 'file_operation';
          } else if (toolName.includes('note_template') || text.includes('模板') || text.includes('日记') || text.includes('会议记录')) {
            toolName = 'note_template';
          }

          // 提取查询参数
          let query = '';
          const queryPatterns = [
            /"query"\s*:\s*"([^"]+)"/,
            /搜索["\s]*([^"]+)["\s]*/,
            /查询["\s]*([^"]+)["\s]*/
          ];

          for (const qPattern of queryPatterns) {
            const qMatch = text.match(qPattern);
            if (qMatch) {
              query = qMatch[1];
              break;
            }
          }

          const extractedAction = {
            type: 'tool_call',
            description: `从文本中提取的工具调用: ${toolName}`,
            toolName: toolName,
            parameters: query ? { query } : {}
          };

          console.log('✅ 成功提取行动计划:', extractedAction);
          return extractedAction;
        }
      }

      console.log('❌ 无法从文本中提取工具调用信息');
      return null;
    } catch (error) {
      console.log('❌ 提取行动计划时出错:', error.message);
      return null;
    }
  }

  /**
   * 执行工具
   */
  private async executeTool(toolName: string, parameters: any): Promise<ToolResult> {
    console.log('🔧 TaskPlanner.executeTool 开始执行');
    console.log('🔧 工具名称:', toolName);
    console.log('🔧 工具参数:', parameters);

    try {
      console.log('🔧 调用 toolRegistry.execute...');
      const result = await this.toolRegistry.execute(toolName, parameters);
      console.log('🔧 工具执行结果:', {
        success: result.success,
        hasData: !!result.data,
        hasError: !!result.error,
        dataType: typeof result.data
      });

      if (result.error) {
        console.log('🔧 工具执行错误:', result.error);
      }

      return result;
    } catch (error) {
      console.log('❌ 工具执行异常:', error.message);
      console.log('❌ 异常堆栈:', error.stack);

      return {
        success: false,
        error: `工具执行失败: ${error.message}`
      };
    }
  }

  /**
   * 判断是否应该停止
   */
  private async shouldStop(context: string, executionLog: ExecutionStep[]): Promise<boolean> {
    // 简单的停止条件
    const recentSteps = executionLog.slice(-3);
    
    // 如果最近有最终答案，停止
    if (recentSteps.some(step => step.type === 'final_answer')) {
      return true;
    }

    // 如果最近的工具调用都失败了，停止
    const recentToolCalls = recentSteps.filter(step => step.type === 'tool_result');
    if (recentToolCalls.length >= 2 && 
        recentToolCalls.every(step => !step.toolResult?.success)) {
      return true;
    }

    return false;
  }

  /**
   * 生成最终答案
   */
  private async generateFinalAnswer(context: string, executionLog: ExecutionStep[]): Promise<string> {
    const prompt = this.promptManager.generateContentSummaryPrompt(
      context,
      executionLog.map(step => ({
        source: step.type,
        content: step.content,
        score: 1.0
      }))
    );

    const messages: LLMMessage[] = [
      { role: 'user', content: prompt }
    ];

    const response = await this.llm.generateText(messages);
    return response.content;
  }

  /**
   * 解析任务分析结果
   */
  private parseTaskAnalysis(content: string): TaskAnalysis {
    // 改进的解析逻辑，更积极地识别可执行任务
    const lowerContent = content.toLowerCase();

    // 检查是否包含明确的拒绝词汇
    const refusalWords = ['无法', '不能', '无效', '不支持', '无权限'];
    const hasRefusal = refusalWords.some(word => lowerContent.includes(word));

    // 检查是否包含工具相关的关键词
    const toolKeywords = [
      '搜索', '查找', '查询', '计算', '执行', '运行', '分析',
      '笔记', '文档', '网络', '代码', '插件', '命令'
    ];
    const hasToolKeywords = toolKeywords.some(word => lowerContent.includes(word));

    // 如果没有明确拒绝且包含工具关键词，认为是可执行的
    const executable = !hasRefusal || hasToolKeywords;

    return {
      analysis: content,
      executable,
      complexity: this.estimateComplexity(content),
      requiredTools: this.extractRequiredTools(content)
    };
  }

  /**
   * 估算任务复杂度
   */
  private estimateComplexity(analysis: string): 'low' | 'medium' | 'high' {
    const complexityIndicators = [
      '多步骤', '复杂', '需要', '分析', '处理', '生成', '搜索', '计算'
    ];
    
    const indicatorCount = complexityIndicators.filter(indicator => 
      analysis.includes(indicator)
    ).length;

    if (indicatorCount <= 2) return 'low';
    if (indicatorCount <= 4) return 'medium';
    return 'high';
  }

  /**
   * 提取所需工具
   */
  private extractRequiredTools(analysis: string): string[] {
    const availableTools = this.toolRegistry.list();
    const requiredTools: string[] = [];

    for (const tool of availableTools) {
      if (analysis.toLowerCase().includes(tool.name.toLowerCase()) ||
          analysis.toLowerCase().includes(tool.description.toLowerCase())) {
        requiredTools.push(tool.name);
      }
    }

    return requiredTools;
  }
}

/**
 * 规划上下文
 */
export interface PlanningContext {
  conversationHistory?: string;
  userPreferences?: Record<string, any>;
  availableTools?: string[];
}

/**
 * 任务分析结果
 */
export interface TaskAnalysis {
  analysis: string;
  executable: boolean;
  complexity: 'low' | 'medium' | 'high';
  requiredTools: string[];
}

/**
 * 行动计划
 */
export interface ActionPlan {
  type: 'tool_call' | 'final_answer' | 'need_more_info';
  description: string;
  toolName?: string;
  parameters?: any;
  answer?: string;
  question?: string;
}

/**
 * 执行步骤
 */
export interface ExecutionStep {
  type: 'analysis' | 'thought' | 'action' | 'tool_result' | 'final_answer' | 'error' | 'clarification';
  content: string;
  timestamp: Date;
  toolName?: string;
  toolResult?: ToolResult;
}

/**
 * ReAct执行结果
 */
export interface ReActResult {
  success: boolean;
  finalAnswer: string;
  toolsUsed: string[];
  iterations: number;
}

/**
 * 任务执行结果
 */
export interface TaskExecutionResult {
  success: boolean;
  result: string;
  executionLog: ExecutionStep[];
  executionTime: number;
  toolsUsed?: string[];
  iterations?: number;
  error?: string;
}
