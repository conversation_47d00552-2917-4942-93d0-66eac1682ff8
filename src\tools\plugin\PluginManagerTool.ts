import { App } from 'obsidian';
import { BaseTool, ToolConfig } from '../../core/tools/BaseTool';
import { ToolResult, ToolExecutionContext, ToolValidationResult } from '../../types/tools';
import { PluginDiscoveryTool } from './PluginDiscoveryTool';
import { PluginCommandQueryTool } from './PluginCommandQueryTool';
import { PluginCommandExecutorTool } from './PluginCommandExecutorTool';

/**
 * 插件管理工具
 * 集成插件发现、命令查询和命令执行功能的综合工具
 */
export class PluginManagerTool extends BaseTool {
  private app: App;
  private discoveryTool: PluginDiscoveryTool;
  private commandQueryTool: PluginCommandQueryTool;
  private commandExecutorTool: PluginCommandExecutorTool;

  constructor(app: App) {
    const config: ToolConfig = {
      name: 'plugin_manager',
      description: '综合的插件管理工具，支持插件发现、命令查询和执行',
      category: 'plugin',
      version: '1.0.0',
      parameters: {
        type: 'object',
        properties: {
          action: {
            type: 'string',
            description: '要执行的操作',
            enum: ['discover', 'query_commands', 'execute_command', 'get_stats'],
          },
          pluginId: {
            type: 'string',
            description: '插件ID（用于特定插件操作）'
          },
          commandId: {
            type: 'string',
            description: '命令ID（用于命令执行）'
          },
          filter: {
            type: 'string',
            description: '过滤条件（用于搜索）'
          },
          includeDisabled: {
            type: 'boolean',
            description: '是否包含禁用的插件',
            default: false
          },
          confirmExecution: {
            type: 'boolean',
            description: '执行命令前是否需要确认',
            default: true
          }
        },
        required: ['action']
      },
      permissions: {
        required: ['plugin_read'],
        optional: ['plugin_execute', 'plugin_dangerous'],
        dangerous: false,
        requiresConfirmation: false
      },
      metadata: {
        author: 'AI Coach Team',
        tags: ['plugin', 'management', 'comprehensive'],
        documentation: '提供完整的插件管理功能，包括发现、查询和执行',
        examples: [
          {
            name: '发现所有插件',
            description: '获取所有已安装插件的列表',
            input: { action: 'discover' },
            expectedOutput: { plugins: [] }
          },
          {
            name: '查询插件命令',
            description: '查询特定插件的可用命令',
            input: { action: 'query_commands', pluginId: 'calendar' },
            expectedOutput: { commands: [] }
          },
          {
            name: '执行插件命令',
            description: '执行指定的插件命令',
            input: { action: 'execute_command', commandId: 'daily-notes:open-today' },
            expectedOutput: { success: true }
          }
        ]
      }
    };

    super(config);
    this.app = app;
    this.discoveryTool = new PluginDiscoveryTool(app);
    this.commandQueryTool = new PluginCommandQueryTool(app);
    this.commandExecutorTool = new PluginCommandExecutorTool(app);
  }

  protected async executeInternal(
    args: Record<string, any>,
    context?: ToolExecutionContext
  ): Promise<ToolResult> {
    const { action } = args;

    try {
      switch (action) {
        case 'discover':
          return await this.handleDiscoverAction(args, context);
        
        case 'query_commands':
          return await this.handleQueryCommandsAction(args, context);
        
        case 'execute_command':
          return await this.handleExecuteCommandAction(args, context);
        
        case 'get_stats':
          return await this.handleGetStatsAction(args, context);
        
        default:
          return {
            success: false,
            error: `未知的操作: ${action}`,
            metadata: { action }
          };
      }
    } catch (error) {
      return {
        success: false,
        error: `插件管理操作失败: ${error.message}`,
        metadata: { action, errorType: 'plugin_manager_error' }
      };
    }
  }

  protected async validateInternal(args: Record<string, any>): Promise<ToolValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const { action, commandId, pluginId } = args;

    // 验证操作类型
    const validActions = ['discover', 'query_commands', 'execute_command', 'get_stats'];
    if (!validActions.includes(action)) {
      errors.push(`无效的操作: ${action}。有效操作: ${validActions.join(', ')}`);
    }

    // 根据操作类型验证必需参数
    switch (action) {
      case 'execute_command':
        if (!commandId) {
          errors.push('执行命令操作需要提供 commandId 参数');
        }
        break;
      
      case 'query_commands':
        if (pluginId && !this.discoveryTool.pluginExists(pluginId)) {
          warnings.push(`插件可能不存在: ${pluginId}`);
        }
        break;
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 处理插件发现操作
   */
  private async handleDiscoverAction(
    args: Record<string, any>,
    context?: ToolExecutionContext
  ): Promise<ToolResult> {
    const { includeDisabled, filter, pluginId } = args;

    if (pluginId) {
      // 获取特定插件的详细信息
      const pluginDetails = await this.discoveryTool.getPluginDetails(pluginId);
      if (!pluginDetails) {
        return {
          success: false,
          error: `插件不存在: ${pluginId}`,
          metadata: { pluginId }
        };
      }

      return {
        success: true,
        data: { plugin: pluginDetails },
        metadata: { action: 'discover_specific', pluginId }
      };
    } else {
      // 发现所有插件
      return await this.discoveryTool.execute({
        includeDisabled,
        filterByName: filter
      }, context);
    }
  }

  /**
   * 处理命令查询操作
   */
  private async handleQueryCommandsAction(
    args: Record<string, any>,
    context?: ToolExecutionContext
  ): Promise<ToolResult> {
    const { pluginId, filter } = args;

    return await this.commandQueryTool.execute({
      pluginId,
      commandFilter: filter,
      includeHotkeys: true
    }, context);
  }

  /**
   * 处理命令执行操作
   */
  private async handleExecuteCommandAction(
    args: Record<string, any>,
    context?: ToolExecutionContext
  ): Promise<ToolResult> {
    const { commandId, confirmExecution } = args;

    return await this.commandExecutorTool.execute({
      commandId,
      confirmExecution
    }, context);
  }

  /**
   * 处理统计信息获取操作
   */
  private async handleGetStatsAction(
    args: Record<string, any>,
    context?: ToolExecutionContext
  ): Promise<ToolResult> {
    try {
      const pluginStats = this.discoveryTool.getPluginStats();
      const commandStats = this.commandQueryTool.getAllPluginCommandStats();
      const executionStats = this.commandExecutorTool.getExecutionStats();

      return {
        success: true,
        data: {
          plugins: pluginStats,
          commands: commandStats,
          executions: executionStats,
          summary: {
            totalPlugins: pluginStats.totalPlugins,
            totalCommands: Object.values(commandStats).reduce((sum, count) => sum + count, 0),
            totalExecutions: executionStats.totalExecutions
          }
        },
        metadata: {
          action: 'get_stats',
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `获取统计信息失败: ${error.message}`,
        metadata: { action: 'get_stats' }
      };
    }
  }

  /**
   * 搜索插件和命令
   */
  async searchPluginsAndCommands(query: string): Promise<{
    plugins: any[];
    commands: any[];
  }> {
    // 搜索插件
    const pluginResult = await this.discoveryTool.execute({
      filterByName: query,
      includeDisabled: true
    });

    // 搜索命令
    const commandResult = await this.commandQueryTool.execute({
      commandFilter: query,
      includeHotkeys: true
    });

    return {
      plugins: pluginResult.success ? pluginResult.data.plugins : [],
      commands: commandResult.success ? commandResult.data.commands : []
    };
  }

  /**
   * 获取插件的完整信息（包括命令）
   */
  async getPluginFullInfo(pluginId: string): Promise<any> {
    const pluginDetails = await this.discoveryTool.getPluginDetails(pluginId);
    if (!pluginDetails) {
      return null;
    }

    const commandStats = this.commandQueryTool.getPluginCommandStats(pluginId);
    const commands = await this.commandQueryTool.execute({
      pluginId,
      includeHotkeys: true
    });

    return {
      ...pluginDetails,
      commandStats,
      commands: commands.success ? commands.data.commands : []
    };
  }

  /**
   * 批量执行命令
   */
  async executeBatchCommands(
    commandIds: string[],
    options?: {
      stopOnFailure?: boolean;
      confirmEach?: boolean;
    }
  ): Promise<any[]> {
    return await this.commandExecutorTool.executeCommandsBatch(commandIds, options);
  }
}
