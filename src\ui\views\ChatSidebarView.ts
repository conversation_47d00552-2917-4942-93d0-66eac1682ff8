import { ItemView, WorkspaceLeaf, TextAreaComponent, ButtonComponent, Notice, Modal, App, TextComponent } from 'obsidian';
import { OrchestrationEngine } from '../../core/orchestration/OrchestrationEngine';

export const CHAT_SIDEBAR_VIEW_TYPE = 'ai-coach-chat-sidebar';

/**
 * AI Coach 聊天侧边栏视图
 */
export class ChatSidebarView extends ItemView {
  private orchestrationEngine: OrchestrationEngine;
  private chatContainer: HTMLElement;
  private inputArea: TextAreaComponent;
  private sendButton: ButtonComponent;
  private isProcessing: boolean = false;
  private memoryContainer: HTMLElement;
  private currentTab: 'chat' | 'memory' = 'chat';

  constructor(leaf: WorkspaceLeaf, orchestrationEngine: OrchestrationEngine) {
    super(leaf);
    this.orchestrationEngine = orchestrationEngine;
  }

  getViewType(): string {
    return CHAT_SIDEBAR_VIEW_TYPE;
  }

  getDisplayText(): string {
    return 'AI Coach';
  }

  getIcon(): string {
    return 'bot';
  }

  async onOpen() {
    console.log('🎨 ChatSidebarView: 开始创建侧边栏界面');
    
    const container = this.containerEl.children[1];
    container.empty();
    container.addClass('ai-coach-sidebar');

    // 创建标签页切换
    this.createTabHeader(container);

    // 创建聊天界面
    this.createChatInterface(container);

    // 创建记忆管理界面
    this.createMemoryInterface(container);

    // 显示默认标签页
    this.switchTab('chat');

    console.log('✅ ChatSidebarView: 侧边栏界面创建完成');
  }

  async onClose() {
    console.log('ChatSidebarView: 关闭侧边栏');
  }

  /**
   * 创建标签页头部
   */
  private createTabHeader(container: Element) {
    const tabHeader = container.createDiv('ai-coach-tab-header');
    
    const chatTab = tabHeader.createDiv('ai-coach-tab');
    chatTab.setText('💬 对话');
    chatTab.addClass('ai-coach-tab-active');
    chatTab.addEventListener('click', () => this.switchTab('chat'));

    const memoryTab = tabHeader.createDiv('ai-coach-tab');
    memoryTab.setText('🧠 记忆');
    memoryTab.addEventListener('click', () => this.switchTab('memory'));
  }

  /**
   * 创建聊天界面
   */
  private createChatInterface(container: Element) {
    const chatPanel = container.createDiv('ai-coach-chat-panel');
    chatPanel.style.display = 'flex';
    chatPanel.style.flexDirection = 'column';
    chatPanel.style.height = 'calc(100% - 40px)';

    // 聊天消息容器
    this.chatContainer = chatPanel.createDiv('ai-coach-chat-container');
    this.chatContainer.style.flex = '1';
    this.chatContainer.style.overflowY = 'auto';
    this.chatContainer.style.padding = '10px';
    this.chatContainer.style.borderBottom = '1px solid var(--background-modifier-border)';

    // 输入区域
    const inputContainer = chatPanel.createDiv('ai-coach-input-container');
    inputContainer.style.padding = '10px';
    inputContainer.style.display = 'flex';
    inputContainer.style.flexDirection = 'column';
    inputContainer.style.gap = '8px';

    // 输入框
    this.inputArea = new TextAreaComponent(inputContainer);
    this.inputArea.inputEl.placeholder = '输入您的问题...';
    this.inputArea.inputEl.style.minHeight = '60px';
    this.inputArea.inputEl.style.resize = 'vertical';
    this.inputArea.inputEl.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        this.sendMessage();
      }
    });

    // 按钮容器
    const buttonContainer = inputContainer.createDiv();
    buttonContainer.style.display = 'flex';
    buttonContainer.style.gap = '8px';

    // 发送按钮
    this.sendButton = new ButtonComponent(buttonContainer);
    this.sendButton.setButtonText('发送');
    this.sendButton.setCta();
    this.sendButton.onClick(() => this.sendMessage());

    // 清空按钮
    const clearButton = new ButtonComponent(buttonContainer);
    clearButton.setButtonText('清空');
    clearButton.onClick(() => this.clearChat());

    // 添加欢迎消息
    this.addWelcomeMessage();
  }

  /**
   * 创建记忆管理界面
   */
  private createMemoryInterface(container: Element) {
    const memoryPanel = container.createDiv('ai-coach-memory-panel');
    memoryPanel.style.display = 'none';
    memoryPanel.style.height = 'calc(100% - 40px)';
    memoryPanel.style.padding = '10px';

    this.memoryContainer = memoryPanel;

    // 记忆管理标题
    const title = memoryPanel.createEl('h3');
    title.setText('🧠 长期记忆管理');
    title.style.marginTop = '0';

    // 刷新按钮
    const refreshButton = new ButtonComponent(memoryPanel);
    refreshButton.setButtonText('🔄 刷新记忆');
    refreshButton.onClick(() => this.loadMemories());

    // 记忆列表容器
    const memoriesContainer = memoryPanel.createDiv('ai-coach-memories-container');
    memoriesContainer.style.marginTop = '15px';
    memoriesContainer.style.maxHeight = 'calc(100% - 100px)';
    memoriesContainer.style.overflowY = 'auto';

    this.loadMemories();
  }

  /**
   * 切换标签页
   */
  public switchTab(tab: 'chat' | 'memory') {
    this.currentTab = tab;

    // 更新标签页样式
    const tabs = this.containerEl.querySelectorAll('.ai-coach-tab');
    tabs.forEach((tabEl, index) => {
      if ((index === 0 && tab === 'chat') || (index === 1 && tab === 'memory')) {
        tabEl.addClass('ai-coach-tab-active');
      } else {
        tabEl.removeClass('ai-coach-tab-active');
      }
    });

    // 显示/隐藏面板
    const chatPanel = this.containerEl.querySelector('.ai-coach-chat-panel') as HTMLElement;
    const memoryPanel = this.containerEl.querySelector('.ai-coach-memory-panel') as HTMLElement;

    if (tab === 'chat') {
      chatPanel.style.display = 'flex';
      memoryPanel.style.display = 'none';
    } else {
      chatPanel.style.display = 'none';
      memoryPanel.style.display = 'block';
      this.loadMemories(); // 切换到记忆页面时刷新
    }
  }

  /**
   * 添加欢迎消息
   */
  private addWelcomeMessage() {
    const welcomeMsg = this.chatContainer.createDiv('ai-coach-message ai-coach-message-assistant');
    welcomeMsg.innerHTML = `
      <div class="ai-coach-message-content">
        <strong>👋 欢迎使用 AI Coach Advanced！</strong><br><br>
        我可以帮助您：<br>
        • 🔍 搜索和查询您的笔记<br>
        • 📝 创建和编辑文件<br>
        • 📋 使用模板创建结构化笔记<br>
        • 🌐 搜索网络信息<br>
        • ⚡ 执行计算和代码<br>
        • 🧠 管理长期记忆<br><br>
        请输入您的问题或指令！
      </div>
    `;
  }

  /**
   * 发送消息
   */
  private async sendMessage() {
    const message = this.inputArea.getValue().trim();
    console.log('💬 ChatSidebarView.sendMessage: 用户发送消息:', message);
    
    if (!message || this.isProcessing) {
      console.log('⚠️ 消息为空或正在处理中，忽略');
      return;
    }

    console.log('⚡ 开始处理用户消息...');
    this.isProcessing = true;
    this.updateSendButton();

    try {
      // 添加用户消息到界面
      console.log('💬 添加用户消息到界面...');
      this.addMessage('user', message);
      this.inputArea.setValue('');

      // 显示处理中状态
      console.log('⏳ 显示处理中状态...');
      const processingMessage = this.addMessage('assistant', '正在思考中...');

      // 处理用户输入
      console.log('🎯 调用 orchestrationEngine.processUserInput...');
      const result = await this.orchestrationEngine.processUserInput(message);
      console.log('🎯 processUserInput 返回结果:', {
        success: result.success,
        hasResponse: !!result.response,
        hasError: !!result.error,
        hasExecutionLog: !!(result.executionLog && result.executionLog.length > 0),
        toolsUsed: result.toolsUsed
      });

      // 移除处理中消息
      if (processingMessage) {
        processingMessage.remove();
      }

      if (result.success) {
        console.log('✅ 处理成功，添加AI回复...');
        this.addMessage('assistant', result.response);
        
        // 如果有执行日志，显示详细信息
        if (result.executionLog && result.executionLog.length > 0) {
          console.log('📋 添加执行详情...');
          this.addExecutionDetails(result);
        }
      } else {
        console.log('❌ 处理失败:', result.error);
        this.addMessage('assistant', `抱歉，处理您的请求时出现了问题：${result.error || '未知错误'}`);
      }

    } catch (error) {
      console.error('❌ ChatSidebarView.sendMessage 异常:', error);
      console.error('❌ 错误堆栈:', error.stack);
      this.addMessage('assistant', `处理消息时发生错误：${error.message}`);
    } finally {
      console.log('🏁 消息处理完成，重置状态');
      this.isProcessing = false;
      this.updateSendButton();
      this.inputArea.inputEl.focus();
    }
  }

  /**
   * 添加消息到聊天界面
   */
  private addMessage(role: 'user' | 'assistant', content: string): HTMLElement {
    const messageEl = this.chatContainer.createDiv(`ai-coach-message ai-coach-message-${role}`);
    
    const contentEl = messageEl.createDiv('ai-coach-message-content');
    contentEl.innerHTML = this.formatMessage(content);
    
    // 滚动到底部
    this.chatContainer.scrollTop = this.chatContainer.scrollHeight;
    
    return messageEl;
  }

  /**
   * 格式化消息内容
   */
  private formatMessage(content: string): string {
    // 简单的markdown渲染
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>')
      .replace(/\n/g, '<br>');
  }

  /**
   * 添加执行详情
   */
  private addExecutionDetails(result: any) {
    if (!result.executionLog || result.executionLog.length === 0) return;

    const detailsEl = this.chatContainer.createDiv('ai-coach-execution-details');
    detailsEl.innerHTML = `
      <details>
        <summary>📋 执行详情 (${result.toolsUsed?.length || 0} 个工具)</summary>
        <div class="ai-coach-execution-log">
          ${result.executionLog.map((step: any) => `
            <div class="ai-coach-log-step">
              <strong>${step.type}:</strong> ${step.content.substring(0, 100)}${step.content.length > 100 ? '...' : ''}
            </div>
          `).join('')}
        </div>
      </details>
    `;
  }

  /**
   * 更新发送按钮状态
   */
  private updateSendButton() {
    this.sendButton.setDisabled(this.isProcessing);
    this.sendButton.setButtonText(this.isProcessing ? '处理中...' : '发送');
  }

  /**
   * 清空聊天记录
   */
  private clearChat() {
    this.chatContainer.empty();
    this.addWelcomeMessage();
    new Notice('聊天记录已清空');
  }

  /**
   * 加载长期记忆
   */
  private async loadMemories() {
    console.log('🧠 加载长期记忆...');
    
    try {
      // 获取长期记忆工具
      const tools = this.orchestrationEngine.getAvailableTools();
      const longTermMemoryTool = tools.find(tool => tool.name === 'long_term_memory');
      
      if (!longTermMemoryTool) {
        this.showMemoryError('长期记忆工具未找到');
        return;
      }

      // 调用工具获取记忆列表
      const result = await longTermMemoryTool.execute({ action: 'list' });
      
      if (result.success && result.data) {
        this.displayMemories(result.data);
      } else {
        this.showMemoryError(result.error || '获取记忆失败');
      }
    } catch (error) {
      console.error('加载记忆失败:', error);
      this.showMemoryError(`加载记忆失败: ${error.message}`);
    }
  }

  /**
   * 显示记忆列表
   */
  private displayMemories(memories: any[]) {
    const container = this.memoryContainer.querySelector('.ai-coach-memories-container');
    if (!container) return;

    container.empty();

    if (!memories || memories.length === 0) {
      container.createDiv('ai-coach-no-memories').setText('暂无长期记忆');
      return;
    }

    memories.forEach((memory, index) => {
      const memoryEl = container.createDiv('ai-coach-memory-item');
      memoryEl.innerHTML = `
        <div class="ai-coach-memory-header">
          <strong>${memory.key || `记忆 ${index + 1}`}</strong>
          <span class="ai-coach-memory-date">${new Date(memory.timestamp || Date.now()).toLocaleDateString()}</span>
        </div>
        <div class="ai-coach-memory-content">${memory.content || memory.value || ''}</div>
        <div class="ai-coach-memory-actions">
          <button class="ai-coach-btn ai-coach-btn-edit" data-key="${memory.key}">编辑</button>
          <button class="ai-coach-btn ai-coach-btn-delete" data-key="${memory.key}">删除</button>
        </div>
      `;

      // 添加编辑和删除事件
      const editBtn = memoryEl.querySelector('.ai-coach-btn-edit') as HTMLElement;
      const deleteBtn = memoryEl.querySelector('.ai-coach-btn-delete') as HTMLElement;

      editBtn?.addEventListener('click', () => this.editMemory(memory));
      deleteBtn?.addEventListener('click', () => this.deleteMemory(memory.key));
    });

    // 添加新建记忆按钮
    const addButton = container.createDiv('ai-coach-add-memory');
    addButton.innerHTML = '<button class="ai-coach-btn ai-coach-btn-primary">+ 添加新记忆</button>';
    addButton.querySelector('button')?.addEventListener('click', () => this.addNewMemory());
  }

  /**
   * 显示记忆错误
   */
  private showMemoryError(error: string) {
    const container = this.memoryContainer.querySelector('.ai-coach-memories-container');
    if (!container) return;

    container.empty();
    container.createDiv('ai-coach-memory-error').setText(`错误: ${error}`);
  }

  /**
   * 编辑记忆
   */
  private async editMemory(memory: any) {
    const modal = new MemoryEditModal(this.app, memory, async (updatedMemory) => {
      try {
        const tools = this.orchestrationEngine.getAvailableTools();
        const longTermMemoryTool = tools.find(tool => tool.name === 'long_term_memory');

        if (longTermMemoryTool) {
          const result = await longTermMemoryTool.execute({
            action: 'store',
            key: updatedMemory.key,
            content: updatedMemory.content,
            metadata: updatedMemory.metadata
          });

          if (result.success) {
            new Notice('记忆已更新');
            this.loadMemories(); // 刷新列表
          } else {
            new Notice(`更新失败: ${result.error}`);
          }
        }
      } catch (error) {
        console.error('更新记忆失败:', error);
        new Notice(`更新失败: ${error.message}`);
      }
    });

    modal.open();
  }

  /**
   * 删除记忆
   */
  private async deleteMemory(key: string) {
    if (!confirm(`确定要删除记忆 "${key}" 吗？`)) {
      return;
    }

    try {
      const tools = this.orchestrationEngine.getAvailableTools();
      const longTermMemoryTool = tools.find(tool => tool.name === 'long_term_memory');

      if (longTermMemoryTool) {
        const result = await longTermMemoryTool.execute({ action: 'delete', key });

        if (result.success) {
          new Notice('记忆已删除');
          this.loadMemories(); // 刷新列表
        } else {
          new Notice(`删除失败: ${result.error}`);
        }
      }
    } catch (error) {
      console.error('删除记忆失败:', error);
      new Notice(`删除失败: ${error.message}`);
    }
  }

  /**
   * 添加新记忆
   */
  private async addNewMemory() {
    const modal = new MemoryEditModal(this.app, null, async (newMemory) => {
      try {
        const tools = this.orchestrationEngine.getAvailableTools();
        const longTermMemoryTool = tools.find(tool => tool.name === 'long_term_memory');

        if (longTermMemoryTool) {
          const result = await longTermMemoryTool.execute({
            action: 'store',
            key: newMemory.key,
            content: newMemory.content,
            metadata: newMemory.metadata
          });

          if (result.success) {
            new Notice('记忆已添加');
            this.loadMemories(); // 刷新列表
          } else {
            new Notice(`添加失败: ${result.error}`);
          }
        }
      } catch (error) {
        console.error('添加记忆失败:', error);
        new Notice(`添加失败: ${error.message}`);
      }
    });

    modal.open();
  }
}

/**
 * 记忆编辑模态框
 */
class MemoryEditModal extends Modal {
  private memory: any;
  private onSave: (memory: any) => void;
  private keyInput: TextComponent;
  private contentInput: TextAreaComponent;
  private tagsInput: TextComponent;

  constructor(app: App, memory: any, onSave: (memory: any) => void) {
    super(app);
    this.memory = memory;
    this.onSave = onSave;
  }

  onOpen() {
    const { contentEl } = this;
    contentEl.empty();

    contentEl.createEl('h2', { text: this.memory ? '编辑记忆' : '添加新记忆' });

    // 记忆键名
    const keyContainer = contentEl.createDiv();
    keyContainer.createEl('label', { text: '记忆键名:' });
    this.keyInput = new TextComponent(keyContainer);
    this.keyInput.inputEl.style.width = '100%';
    this.keyInput.inputEl.style.marginTop = '5px';
    this.keyInput.setValue(this.memory?.key || '');

    // 记忆内容
    const contentContainer = contentEl.createDiv();
    contentContainer.style.marginTop = '15px';
    contentContainer.createEl('label', { text: '记忆内容:' });
    this.contentInput = new TextAreaComponent(contentContainer);
    this.contentInput.inputEl.style.width = '100%';
    this.contentInput.inputEl.style.height = '150px';
    this.contentInput.inputEl.style.marginTop = '5px';
    this.contentInput.setValue(this.memory?.content || this.memory?.value || '');

    // 标签
    const tagsContainer = contentEl.createDiv();
    tagsContainer.style.marginTop = '15px';
    tagsContainer.createEl('label', { text: '标签 (用逗号分隔):' });
    this.tagsInput = new TextComponent(tagsContainer);
    this.tagsInput.inputEl.style.width = '100%';
    this.tagsInput.inputEl.style.marginTop = '5px';
    const tags = this.memory?.metadata?.tags || [];
    this.tagsInput.setValue(Array.isArray(tags) ? tags.join(', ') : '');

    // 按钮
    const buttonContainer = contentEl.createDiv();
    buttonContainer.style.marginTop = '20px';
    buttonContainer.style.display = 'flex';
    buttonContainer.style.gap = '10px';
    buttonContainer.style.justifyContent = 'flex-end';

    const cancelButton = new ButtonComponent(buttonContainer);
    cancelButton.setButtonText('取消');
    cancelButton.onClick(() => this.close());

    const saveButton = new ButtonComponent(buttonContainer);
    saveButton.setButtonText('保存');
    saveButton.setCta();
    saveButton.onClick(() => this.save());
  }

  private save() {
    const key = this.keyInput.getValue().trim();
    const content = this.contentInput.getValue().trim();
    const tagsStr = this.tagsInput.getValue().trim();

    if (!key) {
      new Notice('请输入记忆键名');
      return;
    }

    if (!content) {
      new Notice('请输入记忆内容');
      return;
    }

    const tags = tagsStr ? tagsStr.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

    const updatedMemory = {
      key,
      content,
      metadata: {
        tags,
        lastModified: new Date().toISOString(),
        source: 'manual'
      }
    };

    this.onSave(updatedMemory);
    this.close();
  }

  onClose() {
    const { contentEl } = this;
    contentEl.empty();
  }
}
