import {
  Tool,
  ToolResult,
  ToolRegistry as IToolRegistry,
  ToolCategory,
  ToolExecutionContext,
  ToolValidationResult,
  ToolDefinition
} from '@/types/tools';

/**
 * 工具注册表实现
 * 负责工具的注册、发现、管理和执行
 */
export class ToolRegistry implements IToolRegistry {
  private tools: Map<string, Tool> = new Map();
  private toolsByCategory: Map<ToolCategory, Set<string>> = new Map();
  private executionHistory: ToolExecutionRecord[] = [];
  private maxHistorySize: number = 1000;

  constructor() {
    // 初始化分类映射
    const categories: ToolCategory[] = ['vault', 'web', 'plugin', 'javascript', 'memory', 'utility', 'ai', 'custom'];
    categories.forEach(category => {
      this.toolsByCategory.set(category, new Set());
    });
  }

  /**
   * 注册工具
   */
  async register(tool: Tool): Promise<void> {
    try {
      // 检查工具名称是否已存在
      if (this.tools.has(tool.name)) {
        throw new Error(`Tool with name '${tool.name}' already exists`);
      }

      // 验证工具配置
      this.validateToolConfig(tool);

      // 初始化工具
      if (tool.initialize) {
        await tool.initialize();
      }

      // 注册工具
      this.tools.set(tool.name, tool);

      // 添加到分类索引
      const categorySet = this.toolsByCategory.get(tool.category);
      if (categorySet) {
        categorySet.add(tool.name);
      }

      console.log(`Tool registered: ${tool.name} (${tool.category})`);
    } catch (error) {
      console.error(`Failed to register tool ${tool.name}:`, error);
      throw error;
    }
  }

  /**
   * 注销工具
   */
  async unregister(name: string): Promise<void> {
    const tool = this.tools.get(name);
    if (!tool) {
      throw new Error(`Tool not found: ${name}`);
    }

    try {
      // 清理工具资源
      if (tool.cleanup) {
        await tool.cleanup();
      }

      // 从注册表移除
      this.tools.delete(name);

      // 从分类索引移除
      const categorySet = this.toolsByCategory.get(tool.category);
      if (categorySet) {
        categorySet.delete(name);
      }

      console.log(`Tool unregistered: ${name}`);
    } catch (error) {
      console.error(`Failed to unregister tool ${name}:`, error);
      throw error;
    }
  }

  /**
   * 获取工具
   */
  get(name: string): Tool | undefined {
    return this.tools.get(name);
  }

  /**
   * 列出工具
   */
  list(category?: ToolCategory): Tool[] {
    if (category) {
      const categorySet = this.toolsByCategory.get(category);
      if (!categorySet) {
        return [];
      }
      return Array.from(categorySet)
        .map(name => this.tools.get(name))
        .filter((tool): tool is Tool => tool !== undefined);
    }

    return Array.from(this.tools.values());
  }

  /**
   * 执行工具
   */
  async execute(
    name: string,
    args: Record<string, any>,
    context?: ToolExecutionContext
  ): Promise<ToolResult> {
    const startTime = Date.now();
    console.log('🔧 ToolRegistry.execute 开始执行');
    console.log('🔧 工具名称:', name);
    console.log('🔧 工具参数:', args);
    console.log('🔧 已注册工具:', Array.from(this.tools.keys()));

    const tool = this.tools.get(name);

    if (!tool) {
      console.log('❌ 工具未找到:', name);
      const result: ToolResult = {
        success: false,
        error: `Tool not found: ${name}`,
        metadata: {
          executionTime: Date.now() - startTime,
        }
      };
      this.recordExecution(name, args, result, context);
      return result;
    }

    console.log('✅ 找到工具:', tool.name, '类别:', tool.category);

    try {
      console.log('🔧 调用工具的execute方法...');
      const result = await tool.execute(args, context);
      console.log('🔧 工具执行完成:', {
        success: result.success,
        hasData: !!result.data,
        hasError: !!result.error,
        executionTime: Date.now() - startTime
      });

      this.recordExecution(name, args, result, context);
      return result;
    } catch (error) {
      console.log('❌ 工具执行异常:', error.message);
      console.log('❌ 异常详情:', error);

      const result: ToolResult = {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        metadata: {
          executionTime: Date.now() - startTime,
        }
      };
      this.recordExecution(name, args, result, context);
      return result;
    }
  }

  /**
   * 验证工具参数
   */
  async validate(name: string, args: Record<string, any>): Promise<ToolValidationResult> {
    const tool = this.tools.get(name);
    if (!tool) {
      return {
        valid: false,
        errors: [`Tool not found: ${name}`],
        warnings: [],
      };
    }

    if (tool.validate) {
      return await tool.validate(args);
    }

    // 基本验证
    return {
      valid: true,
      errors: [],
      warnings: [],
    };
  }

  /**
   * 获取工具定义
   */
  getToolDefinition(name: string): ToolDefinition | undefined {
    const tool = this.tools.get(name);
    if (!tool) {
      return undefined;
    }

    return {
      name: tool.name,
      description: tool.description,
      category: tool.category,
      parameters: tool.parameters,
      permissions: tool.permissions,
      metadata: tool.metadata,
    };
  }

  /**
   * 获取工具定义列表
   */
  getToolDefinitions(category?: ToolCategory): ToolDefinition[] {
    const tools = this.list(category);
    return tools.map(tool => ({
      name: tool.name,
      description: tool.description,
      category: tool.category,
      parameters: tool.parameters,
      permissions: tool.permissions,
      metadata: tool.metadata,
    }));
  }

  /**
   * 批量注册工具
   */
  async registerBatch(tools: Tool[]): Promise<void> {
    const results = await Promise.allSettled(
      tools.map(tool => this.register(tool))
    );

    const failures = results
      .map((result, index) => ({ result, tool: tools[index] }))
      .filter(({ result }) => result.status === 'rejected')
      .map(({ result, tool }) => ({
        tool: tool.name,
        error: (result as PromiseRejectedResult).reason
      }));

    if (failures.length > 0) {
      console.warn('Some tools failed to register:', failures);
    }
  }

  /**
   * 获取工具统计信息
   */
  getStats(): ToolRegistryStats {
    const totalTools = this.tools.size;
    const toolsByCategory: Record<string, number> = {};

    for (const [category, toolSet] of this.toolsByCategory.entries()) {
      toolsByCategory[category] = toolSet.size;
    }

    const recentExecutions = this.executionHistory
      .filter(record => Date.now() - record.timestamp.getTime() < 24 * 60 * 60 * 1000)
      .length;

    return {
      totalTools,
      toolsByCategory,
      recentExecutions,
      totalExecutions: this.executionHistory.length,
    };
  }

  /**
   * 获取执行历史
   */
  getExecutionHistory(limit?: number): ToolExecutionRecord[] {
    const history = [...this.executionHistory].reverse();
    return limit ? history.slice(0, limit) : history;
  }

  /**
   * 清理执行历史
   */
  clearExecutionHistory(): void {
    this.executionHistory = [];
  }

  /**
   * 验证工具配置
   */
  private validateToolConfig(tool: Tool): void {
    if (!tool.name || typeof tool.name !== 'string') {
      throw new Error('Tool name is required and must be a string');
    }

    if (!tool.description || typeof tool.description !== 'string') {
      throw new Error('Tool description is required and must be a string');
    }

    if (!tool.category) {
      throw new Error('Tool category is required');
    }

    if (!tool.parameters || typeof tool.parameters !== 'object') {
      throw new Error('Tool parameters are required');
    }

    if (!tool.permissions || typeof tool.permissions !== 'object') {
      throw new Error('Tool permissions are required');
    }

    if (!tool.metadata || typeof tool.metadata !== 'object') {
      throw new Error('Tool metadata is required');
    }
  }

  /**
   * 记录执行历史
   */
  private recordExecution(
    toolName: string,
    args: Record<string, any>,
    result: ToolResult,
    context?: ToolExecutionContext
  ): void {
    const record: ToolExecutionRecord = {
      toolName,
      args,
      result,
      context,
      timestamp: new Date(),
    };

    this.executionHistory.push(record);

    // 限制历史记录大小
    if (this.executionHistory.length > this.maxHistorySize) {
      this.executionHistory = this.executionHistory.slice(-this.maxHistorySize);
    }
  }
}

/**
 * 工具执行记录
 */
export interface ToolExecutionRecord {
  toolName: string;
  args: Record<string, any>;
  result: ToolResult;
  context?: ToolExecutionContext;
  timestamp: Date;
}

/**
 * 工具注册表统计信息
 */
export interface ToolRegistryStats {
  totalTools: number;
  toolsByCategory: Record<string, number>;
  recentExecutions: number;
  totalExecutions: number;
}
