# AI Coach Advanced - 用户指南

## 🎯 快速开始

### 第一次使用

1. **打开 AI 助手**
   - 使用命令面板 (Ctrl/Cmd + P)
   - 搜索 "打开AI助手对话"
   - 或点击状态栏中的 AI Coach 图标

2. **开始对话**
   ```
   你: Hello! 你能帮我做什么？
   AI: 你好！我是 AI Coach Advanced，你的智能助手。我可以帮你：
   - 搜索和分析你的 Obsidian 笔记
   - 在网络上查找最新信息
   - 执行 JavaScript 代码
   - 管理 Obsidian 插件
   - 记住我们的对话内容
   
   有什么我可以帮助你的吗？
   ```

## 🛠️ 核心功能

### 1. 智能对话

AI Coach Advanced 使用先进的 ReAct（推理和行动）模式，能够：

- **理解复杂请求**: 自动分解多步骤任务
- **选择合适工具**: 智能选择最佳工具组合
- **提供详细解释**: 解释思考过程和行动步骤

#### 示例对话
```
你: 帮我分析一下我关于机器学习的笔记，然后搜索最新的AI发展趋势

AI: 我来帮你完成这个任务。首先让我搜索你的笔记中关于机器学习的内容。

[使用 Vault 查询工具搜索...]

我在你的笔记中找到了以下机器学习相关内容：
- 《深度学习基础》笔记
- 《神经网络架构》研究
- 《机器学习算法对比》总结

现在让我搜索最新的AI发展趋势...

[使用网络搜索工具...]

根据最新信息，2024年AI发展的主要趋势包括：
1. 大语言模型的持续改进
2. 多模态AI的突破
3. AI在科学研究中的应用

结合你的笔记和最新趋势，我建议你关注...
```

### 2. Vault 知识库查询

#### 基本搜索
```
你: 搜索我关于"项目管理"的笔记
AI: [搜索并返回相关笔记内容和摘要]
```

#### 语义搜索
```
你: 找一下我写过的关于提高效率的方法
AI: [使用语义搜索找到相关内容，即使没有直接包含"效率"关键词]
```

#### 高级查询
```
你: 分析我所有关于编程的笔记，总结主要技术栈
AI: [分析多个笔记，提供技术栈总结和建议]
```

### 3. 网络搜索

#### 实时信息查询
```
你: 今天的天气怎么样？
AI: [搜索当前天气信息]
```

#### 研究辅助
```
你: 搜索关于量子计算的最新研究进展
AI: [搜索学术和新闻来源，提供最新信息]
```

#### 事实核查
```
你: 帮我验证一下这个说法是否正确：[某个声明]
AI: [搜索权威来源进行事实核查]
```

### 4. JavaScript 代码执行

#### 简单计算
```
你: 帮我计算 1 到 100 的平方和
AI: 我来为你编写并执行代码：

```javascript
let sum = 0;
for (let i = 1; i <= 100; i++) {
    sum += i * i;
}
console.log(`1到100的平方和是: ${sum}`);
```

执行结果：1到100的平方和是: 338350
```

#### 数据处理
```
你: 帮我处理这个数组 [1,2,3,4,5]，计算平均值和标准差
AI: [生成并执行统计计算代码]
```

#### 文本处理
```
你: 帮我写一个函数来统计文本中的词频
AI: [创建词频统计函数并演示使用]
```

### 5. 插件管理

#### 查看已安装插件
```
你: 显示我安装了哪些插件
AI: [列出所有已安装的插件及其状态]
```

#### 插件功能查询
```
你: Calendar 插件有什么功能？
AI: [查询并解释 Calendar 插件的功能]
```

#### 执行插件命令
```
你: 帮我创建一个新的日记页面
AI: [使用 Daily Notes 插件创建今日日记]
```

## 🎨 高级用法

### 1. 多工具协作

AI Coach Advanced 可以智能地组合使用多个工具：

```
你: 基于我的学习笔记，搜索相关的在线课程，然后帮我制定学习计划

AI: 我来帮你完成这个复杂任务：

1. 首先分析你的学习笔记...
   [使用 Vault 查询工具]
   
2. 然后搜索相关的在线课程...
   [使用网络搜索工具]
   
3. 最后生成个性化学习计划...
   [使用 JavaScript 执行工具处理数据]

[提供完整的学习计划]
```

### 2. 对话上下文管理

AI 会记住对话历史，支持连续对话：

```
你: 搜索我关于Python的笔记
AI: [返回Python相关笔记]

你: 基于这些内容，我应该学习什么新技术？
AI: [基于之前搜索的内容提供建议]

你: 帮我制定一个学习时间表
AI: [结合前面的对话内容制定时间表]
```

### 3. 自定义工作流

#### 研究工作流
```
你: 我要研究"区块链技术"，帮我建立一个完整的研究流程

AI: 我来为你设计一个系统的研究流程：

1. 搜索你现有的相关笔记
2. 查找最新的学术论文和新闻
3. 分析技术趋势和发展方向
4. 生成研究大纲和关键问题
5. 创建学习资源清单

让我开始执行...
```

#### 内容创作工作流
```
你: 我要写一篇关于AI的文章，帮我准备素材

AI: 我来帮你准备文章素材：

1. 分析你已有的AI相关笔记
2. 搜索最新的AI发展动态
3. 整理关键观点和数据
4. 生成文章大纲建议
5. 提供引用来源

开始执行...
```

## ⚙️ 个性化设置

### 1. LLM 配置优化

#### 温度设置
- **创意任务**: 0.8-1.0（更有创造性）
- **分析任务**: 0.3-0.5（更准确）
- **代码生成**: 0.1-0.3（更精确）

#### 模型选择
- **GPT-4**: 复杂推理和分析
- **GPT-3.5-turbo**: 日常对话和简单任务
- **Gemini-pro**: 多模态任务

### 2. 工具配置

#### Vault 查询优化
```
- 索引更新频率: 实时/每小时/每日
- 搜索结果数量: 5-20个
- 语义搜索阈值: 0.7-0.9
```

#### 网络搜索设置
```
- 默认搜索引擎: DuckDuckGo/Google/Bing
- 结果数量: 3-10个
- 搜索超时: 10-30秒
```

#### JavaScript 执行安全
```
- 安全模式: 启用（推荐）
- 执行超时: 5-30秒
- 内存限制: 10-50MB
```

## 🔍 故障排除

### 常见问题

#### 1. AI 回复很慢
- 检查网络连接
- 尝试更换 LLM 模型
- 减少上下文长度

#### 2. 搜索结果不准确
- 调整搜索关键词
- 修改语义搜索阈值
- 重建 Vault 索引

#### 3. 代码执行失败
- 检查代码语法
- 确认安全模式设置
- 查看错误日志

### 性能优化

#### 1. 提高响应速度
- 使用更快的 LLM 模型
- 减少工具调用次数
- 优化网络连接

#### 2. 节省 API 费用
- 使用较小的模型
- 减少上下文长度
- 启用响应缓存

## 📚 最佳实践

### 1. 有效的提问方式

#### ✅ 好的提问
```
"分析我关于项目管理的笔记，总结主要方法论，然后搜索最新的敏捷开发趋势"
```

#### ❌ 不好的提问
```
"帮我"
```

### 2. 充分利用工具

#### 组合使用
- Vault查询 + 网络搜索 = 全面的研究
- 代码执行 + 数据分析 = 深入的洞察
- 插件管理 + 工作流优化 = 高效的操作

### 3. 管理对话历史

#### 定期清理
- 结束长对话后开始新对话
- 定期清理对话历史
- 导出重要对话内容

## 🚀 进阶技巧

### 1. 创建自定义命令

通过对话训练 AI 执行特定任务：

```
你: 我想创建一个"每日回顾"命令，帮我：
1. 搜索今天创建或修改的笔记
2. 生成今日学习总结
3. 提出明天的学习建议

AI: 我理解了你的需求。以后你只需要说"执行每日回顾"，我就会自动完成这些步骤。
```

### 2. 批量处理

```
你: 帮我批量处理所有标记为"待整理"的笔记，为每个笔记生成摘要和标签建议

AI: [自动处理多个笔记并提供建议]
```

### 3. 智能提醒

```
你: 根据我的学习笔记，提醒我需要复习的内容

AI: [分析笔记创建时间和内容，提供复习建议]
```

---

## 📞 获取帮助

- **用户社区**: 加入 Obsidian 社区讨论
- **问题反馈**: 在 GitHub 提交 Issue
- **功能建议**: 通过社区渠道提出建议
- **文档更新**: 查看最新版本的文档

祝你使用愉快！🎉
