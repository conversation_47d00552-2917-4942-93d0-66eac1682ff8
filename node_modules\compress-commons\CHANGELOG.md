## Changelog

**5.0.2** — <small>_February 26, 2024_</small> — [Diff](https://github.com/archiverjs/node-compress-commons/compare/5.0.1...5.0.2)

**5.0.1** — <small>_September 3, 2023_</small> — [Diff](https://github.com/archiverjs/node-compress-commons/compare/5.0.0...5.0.1)

**5.0.0** — <small>_September 2, 2023_</small> — [Diff](https://github.com/archiverjs/node-compress-commons/compare/4.1.2...5.0.0)


**4.1.2** — <small>_September 2, 2023_</small> — [Diff](https://github.com/archiverjs/node-compress-commons/compare/4.1.1...4.1.2)

**4.1.1** — <small>_May 30th, 2021_</small> — [Diff](https://github.com/archiverjs/node-compress-commons/compare/4.1.0...4.1.1)

### Maintenance
- Bump mocha from 8.2.1 to 8.4.0 (#70)
- Bump crc32-stream from 4.0.1 to 4.0.2 (#59)
- Bump y18n from 4.0.0 to 4.0.1 (#69)
- Bump chai from 4.2.0 to 4.3.4 (#67)
- Bump actions/setup-node from 2.1.4 to 2.1.5 (#71)

**4.1.0** — <small>_March 2, 2021_</small> — [Diff](https://github.com/archiverjs/node-compress-commons/compare/4.0.1...4.1.0)

### Features

- Allow prepending forward slash in entry name (#63)

### Maintenance

- Bump actions/setup-node from v2.1.2 to v2.1.4 (#58)

**4.0.1** — <small>_July 20, 2020_</small> — [Diff](https://github.com/archiverjs/node-compress-commons/compare/4.0.0...4.0.1)

* Bump crc32-stream from 3.0.1 to 4.0.0 (#43) @dependabot

**4.0.0** — <small>_July 18, 2020_</small> — [Diff](https://github.com/archiverjs/node-compress-commons/compare/3.0.0...4.0.0)

* Bump mocha from 5.2.0 to 8.0.1 (#36) @dependabot
* Bump readable-stream from 2.3.7 to 3.6.0 (#39) @dependabot
* Bump actions/setup-node from v1 to v2.1.0 (#41) @dependabot
* Bump rimraf from 2.7.1 to 3.0.2 (#38) @dependabot
* Bump mkdirp from 0.5.5 to 1.0.4 (#37) @dependabot
* Bump actions/checkout from v1 to v2.3.1 (#40) @dependabot
* remove support for node < 10 (#42) @ctalkington

**3.0.0** — <small>_April 14, 2020_</small> — [Diff](https://github.com/archiverjs/node-compress-commons/compare/2.1.1...3.0.0)

- breaking: slowly catch up with node LTS, remove support for versions under 8.
- update multiple deps.

**2.1.1** — <small>_August 2, 2019_</small> — [Diff](https://github.com/archiverjs/node-compress-commons/compare/2.1.0...2.1.1)

- update crc32-stream to v3.0.1

**2.1.0** — <small>_August 2, 2019_</small> — [Diff](https://github.com/archiverjs/node-compress-commons/compare/2.0.0...2.1.0)

- update crc32-stream to v3.0.0

**2.0.0** — <small>_July 19, 2019_</small> — [Diff](https://github.com/archiverjs/node-compress-commons/compare/1.2.2...2.0.0)

- breaking: follow node LTS, remove support for versions under 6.
- test: now targeting node v10 and v12
- fix: update Buffer calls to alloc/from
- fix: Add offset to buffer call (#31)
- other: update normalize-path@3 (#34)
- other: update dependencies

[Release Archive](https://github.com/archiverjs/node-compress-commons/releases)
