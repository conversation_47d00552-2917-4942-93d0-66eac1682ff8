/**
 * JavaScript沙箱执行环境
 * 提供安全的JS代码执行，限制访问权限和资源使用
 */
export class JSSandbox {
  private timeout: number;
  private memoryLimit: number;
  private allowedGlobals: Set<string>;
  private blockedPatterns: RegExp[];

  constructor(options: SandboxOptions = {}) {
    this.timeout = options.timeout || 5000;
    this.memoryLimit = options.memoryLimit || 10 * 1024 * 1024; // 10MB
    this.allowedGlobals = new Set(options.allowedGlobals || this.getDefaultAllowedGlobals());
    this.blockedPatterns = options.blockedPatterns || this.getDefaultBlockedPatterns();
  }

  /**
   * 执行JavaScript代码
   */
  async execute(code: string, context: SandboxContext = {}): Promise<SandboxResult> {
    const startTime = Date.now();
    
    try {
      // 预处理代码
      const processedCode = this.preprocessCode(code);
      
      // 验证代码安全性
      const securityCheck = this.validateCodeSecurity(processedCode);
      if (!securityCheck.safe) {
        return {
          success: false,
          error: `代码安全检查失败: ${securityCheck.reason}`,
          executionTime: Date.now() - startTime
        };
      }

      // 创建沙箱环境
      const sandbox = this.createSandboxEnvironment(context);
      
      // 执行代码
      const result = await this.executeInSandbox(processedCode, sandbox);
      
      return {
        success: true,
        result: result.value,
        output: result.output,
        executionTime: Date.now() - startTime,
        memoryUsed: result.memoryUsed
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        executionTime: Date.now() - startTime
      };
    }
  }

  /**
   * 预处理代码
   */
  private preprocessCode(code: string): string {
    // 移除注释
    let processed = code.replace(/\/\*[\s\S]*?\*\//g, '');
    processed = processed.replace(/\/\/.*$/gm, '');
    
    // 添加严格模式
    if (!processed.includes('use strict')) {
      processed = '"use strict";\n' + processed;
    }
    
    return processed;
  }

  /**
   * 验证代码安全性
   */
  private validateCodeSecurity(code: string): SecurityCheckResult {
    // 检查被禁止的模式
    for (const pattern of this.blockedPatterns) {
      if (pattern.test(code)) {
        return {
          safe: false,
          reason: `代码包含被禁止的模式: ${pattern.source}`
        };
      }
    }

    // 检查代码长度
    if (code.length > 50000) {
      return {
        safe: false,
        reason: '代码长度超过限制'
      };
    }

    // 检查嵌套深度
    const maxNesting = this.getMaxNestingDepth(code);
    if (maxNesting > 20) {
      return {
        safe: false,
        reason: '代码嵌套深度过深'
      };
    }

    return { safe: true };
  }

  /**
   * 创建沙箱环境
   */
  private createSandboxEnvironment(context: SandboxContext): SandboxEnvironment {
    const output: string[] = [];
    
    // 创建受限的全局对象
    const sandbox: SandboxEnvironment = {
      // 基础对象
      Object,
      Array,
      String,
      Number,
      Boolean,
      Date,
      Math,
      JSON,
      RegExp,
      Error,
      
      // 控制台输出
      console: {
        log: (...args: any[]) => output.push(args.map(arg => String(arg)).join(' ')),
        warn: (...args: any[]) => output.push('WARN: ' + args.map(arg => String(arg)).join(' ')),
        error: (...args: any[]) => output.push('ERROR: ' + args.map(arg => String(arg)).join(' ')),
        info: (...args: any[]) => output.push('INFO: ' + args.map(arg => String(arg)).join(' '))
      },
      
      // 用户提供的上下文
      ...context,
      
      // 内部方法
      __output: output,
      __memoryUsage: 0
    };

    return sandbox;
  }

  /**
   * 在沙箱中执行代码
   */
  private async executeInSandbox(code: string, sandbox: SandboxEnvironment): Promise<ExecutionResult> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error('代码执行超时'));
      }, this.timeout);

      try {
        // 创建函数并执行
        const func = new Function(...Object.keys(sandbox), `
          try {
            ${code}
          } catch (error) {
            throw error;
          }
        `);

        const result = func(...Object.values(sandbox));
        
        clearTimeout(timeoutId);
        
        resolve({
          value: result,
          output: sandbox.__output.join('\n'),
          memoryUsed: this.estimateMemoryUsage(sandbox)
        });

      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  /**
   * 估算内存使用量
   */
  private estimateMemoryUsage(sandbox: SandboxEnvironment): number {
    try {
      return JSON.stringify(sandbox).length * 2; // 粗略估算
    } catch {
      return 0;
    }
  }

  /**
   * 获取代码最大嵌套深度
   */
  private getMaxNestingDepth(code: string): number {
    let maxDepth = 0;
    let currentDepth = 0;
    
    for (const char of code) {
      if (char === '{' || char === '(' || char === '[') {
        currentDepth++;
        maxDepth = Math.max(maxDepth, currentDepth);
      } else if (char === '}' || char === ')' || char === ']') {
        currentDepth--;
      }
    }
    
    return maxDepth;
  }

  /**
   * 获取默认允许的全局对象
   */
  private getDefaultAllowedGlobals(): string[] {
    return [
      'Object', 'Array', 'String', 'Number', 'Boolean', 'Date', 'Math', 'JSON', 'RegExp', 'Error',
      'parseInt', 'parseFloat', 'isNaN', 'isFinite', 'encodeURIComponent', 'decodeURIComponent'
    ];
  }

  /**
   * 获取默认被禁止的模式
   */
  private getDefaultBlockedPatterns(): RegExp[] {
    return [
      /eval\s*\(/,                    // eval函数
      /Function\s*\(/,                // Function构造函数
      /setTimeout\s*\(/,              // setTimeout
      /setInterval\s*\(/,             // setInterval
      /require\s*\(/,                 // require (Node.js)
      /import\s+/,                    // ES6 import
      /export\s+/,                    // ES6 export
      /process\./,                    // Node.js process
      /global\./,                     // Node.js global
      /window\./,                     // Browser window
      /document\./,                   // Browser document
      /fetch\s*\(/,                   // fetch API
      /XMLHttpRequest/,               // XMLHttpRequest
      /WebSocket/,                    // WebSocket
      /localStorage/,                 // localStorage
      /sessionStorage/,               // sessionStorage
      /indexedDB/,                    // indexedDB
      /navigator\./,                  // navigator
      /location\./,                   // location
      /history\./,                    // history
      /alert\s*\(/,                   // alert
      /confirm\s*\(/,                 // confirm
      /prompt\s*\(/,                  // prompt
      /while\s*\(\s*true\s*\)/,       // 无限循环
      /for\s*\(\s*;\s*;\s*\)/,        // 无限循环
      /\.\s*constructor/,             // 构造函数访问
      /__proto__/,                    // 原型链访问
      /prototype\s*\[/,               // 原型访问
    ];
  }
}

/**
 * 沙箱选项
 */
export interface SandboxOptions {
  timeout?: number;
  memoryLimit?: number;
  allowedGlobals?: string[];
  blockedPatterns?: RegExp[];
}

/**
 * 沙箱上下文
 */
export interface SandboxContext {
  [key: string]: any;
}

/**
 * 沙箱环境
 */
export interface SandboxEnvironment extends SandboxContext {
  console: {
    log: (...args: any[]) => void;
    warn: (...args: any[]) => void;
    error: (...args: any[]) => void;
    info: (...args: any[]) => void;
  };
  __output: string[];
  __memoryUsage: number;
}

/**
 * 沙箱执行结果
 */
export interface SandboxResult {
  success: boolean;
  result?: any;
  output?: string;
  error?: string;
  executionTime: number;
  memoryUsed?: number;
}

/**
 * 执行结果
 */
interface ExecutionResult {
  value: any;
  output: string;
  memoryUsed: number;
}

/**
 * 安全检查结果
 */
interface SecurityCheckResult {
  safe: boolean;
  reason?: string;
}
