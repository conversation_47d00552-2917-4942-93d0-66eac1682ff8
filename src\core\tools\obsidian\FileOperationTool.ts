import { App, TFile, TFolder, Notice } from 'obsidian';
import { BaseTool } from '../BaseTool';
import { ToolResult } from '../types/ToolTypes';

/**
 * Obsidian文件操作工具
 * 提供创建、编辑、删除、移动文件等基础操作
 */
export class FileOperationTool extends BaseTool {
  constructor(private app: App) {
    super(
      'file_operation',
      'Obsidian文件操作',
      '创建、编辑、删除、移动Obsidian文件和文件夹',
      'obsidian',
      {
        action: {
          type: 'string',
          required: true,
          description: '操作类型: create, read, update, delete, move, list, mkdir'
        },
        path: {
          type: 'string',
          required: true,
          description: '文件或文件夹路径（相对于vault根目录）'
        },
        content: {
          type: 'string',
          required: false,
          description: '文件内容（create和update操作时需要）'
        },
        newPath: {
          type: 'string',
          required: false,
          description: '新路径（move操作时需要）'
        },
        recursive: {
          type: 'boolean',
          required: false,
          description: '是否递归操作（list和delete操作时可选）'
        }
      }
    );
  }

  async execute(args: any): Promise<ToolResult> {
    const { action, path, content, newPath, recursive = false } = args;

    try {
      console.log(`🗂️ FileOperationTool: 执行${action}操作，路径: ${path}`);

      switch (action.toLowerCase()) {
        case 'create':
          return await this.createFile(path, content || '');
        
        case 'read':
          return await this.readFile(path);
        
        case 'update':
          return await this.updateFile(path, content || '');
        
        case 'delete':
          return await this.deleteFile(path, recursive);
        
        case 'move':
          return await this.moveFile(path, newPath);
        
        case 'list':
          return await this.listFiles(path, recursive);
        
        case 'mkdir':
          return await this.createFolder(path);
        
        default:
          return {
            success: false,
            error: `不支持的操作类型: ${action}。支持的操作: create, read, update, delete, move, list, mkdir`
          };
      }
    } catch (error) {
      console.error('FileOperationTool执行失败:', error);
      return {
        success: false,
        error: `文件操作失败: ${error.message}`
      };
    }
  }

  /**
   * 创建文件
   */
  private async createFile(filePath: string, content: string): Promise<ToolResult> {
    try {
      // 确保路径以.md结尾（如果没有扩展名）
      if (!filePath.includes('.')) {
        filePath += '.md';
      }

      // 检查文件是否已存在
      const existingFile = this.app.vault.getAbstractFileByPath(filePath);
      if (existingFile) {
        return {
          success: false,
          error: `文件已存在: ${filePath}`
        };
      }

      // 创建文件
      const file = await this.app.vault.create(filePath, content);
      
      console.log(`✅ 文件创建成功: ${filePath}`);
      new Notice(`文件创建成功: ${file.name}`);

      return {
        success: true,
        data: {
          action: 'create',
          path: filePath,
          name: file.name,
          size: file.stat.size,
          created: new Date(file.stat.ctime).toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `创建文件失败: ${error.message}`
      };
    }
  }

  /**
   * 读取文件内容
   */
  private async readFile(filePath: string): Promise<ToolResult> {
    try {
      const file = this.app.vault.getAbstractFileByPath(filePath);
      
      if (!file || !(file instanceof TFile)) {
        return {
          success: false,
          error: `文件不存在: ${filePath}`
        };
      }

      const content = await this.app.vault.read(file);
      
      return {
        success: true,
        data: {
          action: 'read',
          path: filePath,
          name: file.name,
          content: content,
          size: file.stat.size,
          modified: new Date(file.stat.mtime).toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `读取文件失败: ${error.message}`
      };
    }
  }

  /**
   * 更新文件内容
   */
  private async updateFile(filePath: string, content: string): Promise<ToolResult> {
    try {
      const file = this.app.vault.getAbstractFileByPath(filePath);
      
      if (!file || !(file instanceof TFile)) {
        return {
          success: false,
          error: `文件不存在: ${filePath}`
        };
      }

      await this.app.vault.modify(file, content);
      
      console.log(`✅ 文件更新成功: ${filePath}`);
      new Notice(`文件更新成功: ${file.name}`);

      return {
        success: true,
        data: {
          action: 'update',
          path: filePath,
          name: file.name,
          size: file.stat.size,
          modified: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `更新文件失败: ${error.message}`
      };
    }
  }

  /**
   * 删除文件或文件夹
   */
  private async deleteFile(filePath: string, recursive: boolean = false): Promise<ToolResult> {
    try {
      const file = this.app.vault.getAbstractFileByPath(filePath);
      
      if (!file) {
        return {
          success: false,
          error: `文件或文件夹不存在: ${filePath}`
        };
      }

      // 如果是文件夹且不是递归删除，检查是否为空
      if (file instanceof TFolder && !recursive) {
        const children = file.children;
        if (children.length > 0) {
          return {
            success: false,
            error: `文件夹不为空，请使用recursive=true进行递归删除: ${filePath}`
          };
        }
      }

      await this.app.vault.delete(file, recursive);
      
      console.log(`✅ 删除成功: ${filePath}`);
      new Notice(`删除成功: ${file.name}`);

      return {
        success: true,
        data: {
          action: 'delete',
          path: filePath,
          name: file.name,
          type: file instanceof TFile ? 'file' : 'folder',
          recursive: recursive
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `删除失败: ${error.message}`
      };
    }
  }

  /**
   * 移动/重命名文件
   */
  private async moveFile(oldPath: string, newPath: string): Promise<ToolResult> {
    try {
      if (!newPath) {
        return {
          success: false,
          error: '移动操作需要提供newPath参数'
        };
      }

      const file = this.app.vault.getAbstractFileByPath(oldPath);
      
      if (!file) {
        return {
          success: false,
          error: `文件不存在: ${oldPath}`
        };
      }

      await this.app.vault.rename(file, newPath);
      
      console.log(`✅ 移动成功: ${oldPath} -> ${newPath}`);
      new Notice(`移动成功: ${file.name}`);

      return {
        success: true,
        data: {
          action: 'move',
          oldPath: oldPath,
          newPath: newPath,
          name: file.name,
          type: file instanceof TFile ? 'file' : 'folder'
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `移动失败: ${error.message}`
      };
    }
  }

  /**
   * 列出文件和文件夹
   */
  private async listFiles(dirPath: string = '', recursive: boolean = false): Promise<ToolResult> {
    try {
      let targetFolder: TFolder;
      
      if (!dirPath || dirPath === '/' || dirPath === '.') {
        targetFolder = this.app.vault.getRoot();
      } else {
        const folder = this.app.vault.getAbstractFileByPath(dirPath);
        if (!folder || !(folder instanceof TFolder)) {
          return {
            success: false,
            error: `文件夹不存在: ${dirPath}`
          };
        }
        targetFolder = folder;
      }

      const items = this.collectFiles(targetFolder, recursive);

      return {
        success: true,
        data: {
          action: 'list',
          path: dirPath || '/',
          recursive: recursive,
          items: items,
          count: items.length
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `列出文件失败: ${error.message}`
      };
    }
  }

  /**
   * 创建文件夹
   */
  private async createFolder(folderPath: string): Promise<ToolResult> {
    try {
      // 检查文件夹是否已存在
      const existingFolder = this.app.vault.getAbstractFileByPath(folderPath);
      if (existingFolder) {
        return {
          success: false,
          error: `文件夹已存在: ${folderPath}`
        };
      }

      await this.app.vault.createFolder(folderPath);
      
      console.log(`✅ 文件夹创建成功: ${folderPath}`);
      new Notice(`文件夹创建成功: ${folderPath}`);

      return {
        success: true,
        data: {
          action: 'mkdir',
          path: folderPath,
          created: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `创建文件夹失败: ${error.message}`
      };
    }
  }

  /**
   * 递归收集文件信息
   */
  private collectFiles(folder: TFolder, recursive: boolean): any[] {
    const items: any[] = [];

    for (const child of folder.children) {
      if (child instanceof TFile) {
        items.push({
          type: 'file',
          name: child.name,
          path: child.path,
          extension: child.extension,
          size: child.stat.size,
          created: new Date(child.stat.ctime).toISOString(),
          modified: new Date(child.stat.mtime).toISOString()
        });
      } else if (child instanceof TFolder) {
        const folderItem = {
          type: 'folder',
          name: child.name,
          path: child.path,
          children: recursive ? this.collectFiles(child, true) : child.children.length
        };
        items.push(folderItem);
      }
    }

    return items;
  }
}
