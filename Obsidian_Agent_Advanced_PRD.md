# Obsidian 智能体插件 (高级版) - 产品需求文档 (PRD)

### 1. 引言

*   **1.1 项目名称**：Obsidian 智能体插件 (高级版)
*   **1.2 版本**：0.2.0 (基于LLM的高级功能探索)
*   **1.3 项目目标**：
    *   构建一个由大语言模型 (LLM) 驱动的 Obsidian 智能助手，能够理解用户的自然语言指令。
    *   使插件能够以用户的 Obsidian Vault 作为核心知识库进行查询和信息提取。
    *   赋予插件通过 LLM 决策并调用一系列基础工具（插件管理、网络查询、JS脚本编写、记忆管理、知识库查询）的能力，以完成更复杂的任务和提供更智能的辅助。
    *   提升用户在 Obsidian 中的信息处理、知识管理和任务执行效率。
*   **1.4 用户价值**：
    *   通过自然语言交互降低复杂操作的学习成本。
    *   深度整合 Vault 数据，使笔记内容真正成为可利用的动态知识。
    *   自动化处理重复性任务，解放用户精力。
    *   提供超越传统插件的智能化、个性化辅助体验。

### 2. 用户故事 (高级版)

*   **US-LLM-1 (自然语言指令)**：作为一名 Obsidian 用户，我希望能用自然语言向插件下达指令，例如“帮我总结一下上周关于‘凤凰项目’的所有会议记录，并创建一个新的笔记包含这些总结，标题为‘凤凰项目周总结 - [本周日期]’”。
*   **US-LLM-2 (任务拆解与执行)**：作为一名 Obsidian 用户，当我提出一个多步骤请求，如“查找关于‘量子计算最新突破’的资料，总结要点，并帮我起草一篇相关的博客文章初稿”，我希望插件能理解、拆解并逐步执行。
*   **US-KB-1 (Vault知识查询)**：作为一名 Obsidian 用户，我希望插件能在我提问时，主动搜索我的 Vault 笔记来寻找答案，例如“‘阿尔法项目’的当前负责人是谁？”或“我上次记录的关于‘敏捷开发’的最佳实践有哪些？”
*   **US-TOOL-WEB-1 (网络查询)**：作为一名 Obsidian 用户，当插件无法在我的 Vault 中找到所需信息或需要最新资讯时，我希望它能进行网络搜索，例如“马斯克最近关于星舰的访谈说了什么？”
*   **US-TOOL-PLUGIN-1 (插件调用)**：作为一名 Obsidian 用户，我希望插件能调用其他已安装的 Obsidian 插件来完成特定任务，例如“使用‘Tasks’插件帮我创建一个名为‘完成项目报告’的任务，截止日期是本周五”。
*   **US-TOOL-JS-1 (动态功能实现)**：作为一名 Obsidian 用户，对于插件本身没有的、但可以通过简单脚本实现的临时功能，我希望插件能尝试编写并执行JS脚本来实现，例如“帮我统计当前笔记中所有加粗文本的字数”。
*   **US-TOOL-MEM-1 (上下文记忆)**：作为一名 Obsidian 用户，我希望插件能记住我们当前对话的上下文，以及我的一些常用偏好（如常用的总结格式、常关注的主题），以便提供更连贯和个性化的服务。
*   **US-LLM-GEN-1 (内容辅助生成)**：作为一名 Obsidian 用户，我希望能让插件辅助我进行内容创作，例如“根据我提供的这些要点，帮我扩展成一段详细描述”或“帮我润色一下这段文字”。

### 3. 产品功能列表 (高级版)

*   **PF-LLM-CORE-1**: 基于 LLM 的高级自然语言理解 (NLU)，支持复杂指令和多意图识别。
*   **PF-LLM-CORE-2**: 基于 LLM 的动态任务规划与拆解，能够将复杂用户请求分解为可执行的子任务序列。
*   **PF-LLM-CORE-3**: 基于 LLM 的决策制定，包括选择合适的工具、判断信息的相关性等。
*   **PF-LLM-CORE-4**: 基于 LLM 的可控内容生成（总结、草稿、改写、润色等）。
*   **PF-KB-QUERY-1**: 对 Obsidian Vault 内容进行语义化索引和高效查询，支持自然语言提问。
*   **PF-TOOL-PLUGIN-MGT-1**: 发现已安装的 Obsidian 插件列表及其可执行命令。
*   **PF-TOOL-PLUGIN-MGT-2**: 根据 LLM 决策，调用指定 Obsidian 插件的特定命令并处理其返回结果。
*   **PF-TOOL-WEB-SEARCH-1**: 执行网络搜索查询，提取和汇总搜索结果中的相关信息。
*   **PF-TOOL-JS-SCRIPT-1**: 根据 LLM 生成的指令，动态编写 JavaScript 代码片段。
*   **PF-TOOL-JS-SCRIPT-2**: 在受控的沙箱环境中安全执行生成的 JavaScript 脚本，并获取其输出。
*   **PF-TOOL-MEMORY-1**: 短期对话上下文记忆，保持对话连贯性。
*   **PF-TOOL-MEMORY-2**: 长期用户偏好和关键信息记忆与查询（例如，存储在 Vault 内的特定配置笔记中）。
*   **PF-UI-1**: 提供清晰的用户交互界面，用于输入指令、展示结果和必要的澄清对话。

### 4. 功能详细描述 (高级版 - 重点模块)

#### 4.1 LLM驱动的指令处理与任务执行流程
*   **输入接收**：用户通过插件界面输入自然语言指令。
*   **意图理解与任务规划 (LLM)**：LLM 分析指令，识别用户意图，将复杂任务拆解为一系列子步骤。确定每个步骤可能需要的数据、工具或操作。
*   **工具选择与参数准备 (LLM)**：如果步骤需要工具，LLM 选择最合适的工具，并准备调用该工具所需的参数。
*   **工具执行与结果获取**：插件框架调用选定的工具，执行操作，并将结果返回给 LLM。
    *   **Vault知识库查询**：LLM 构造查询语句 -> 知识库接口执行查询 -> 返回相关笔记片段给 LLM。
    *   **插件调用**：LLM 指定插件名和命令 -> 插件交互代理执行 -> 返回结果给 LLM。
    *   **网络查询**：LLM 构造搜索查询 -> 网络搜索模块执行 -> 返回摘要信息给 LLM。
    *   **JS脚本**：LLM 生成JS代码 -> JS沙箱执行 -> 返回结果给 LLM。
    *   **记忆存取**：LLM 请求存取记忆 -> 记忆模块操作 -> 返回信息给 LLM。
*   **结果评估与迭代 (LLM)**：LLM 评估工具执行结果。如果任务未完成或结果不满意，可能重新规划、选择不同工具或再次调用工具。
*   **内容生成与反馈 (LLM)**：LLM 基于处理结果生成最终答复或所需内容，通过UI呈现给用户。

#### 4.2 Vault 知识库集成
*   **数据源**：用户 Obsidian Vault 中的所有 Markdown 文件。
*   **索引**：插件在后台或用户触发时，对 Vault 内容进行索引（例如，使用文本嵌入模型生成向量索引），以支持快速语义搜索。
*   **查询接口**：LLM 可以通过自然语言或关键词向知识库接口发起查询，接口返回最相关的笔记内容片段。
*   **隐私**：索引和查询过程应尽可能在本地完成，除非用户明确同意使用云端 LLM 进行更高级的分析。

#### 4.3 工具调用：JS脚本编写与执行
*   **场景**：当用户请求一个插件当前不具备、但逻辑相对简单的功能时，LLM 可以尝试生成JS脚本来实现。
*   **生成**：LLM 根据任务描述生成JS代码。代码应主要利用 Obsidian API 进行文件操作、内容读写等。
*   **安全沙箱**：JS脚本必须在严格隔离的沙箱环境中执行，限制其对系统资源的访问权限，防止恶意代码执行。沙箱应能捕获脚本错误和输出。
*   **用户确认**：对于可能修改数据的JS脚本，执行前应向用户明确提示并请求确认。

### 5. 非功能性需求 (高级版)
*   **NFR-LLM-PERF-1 (响应速度)**：LLM 调用和复杂任务处理的响应时间应在用户可接受范围内，对耗时操作应有进度提示。
*   **NFR-LLM-ACC-1 (准确性)**：LLM 的理解、决策和内容生成的准确性是核心。需要持续优化提示工程和模型选择。
*   **NFR-SEC-1 (安全性)**：JS脚本执行、插件调用、网络访问必须有严格的安全控制和用户授权机制。
*   **NFR-PRIV-1 (隐私保护)**：明确告知用户数据（尤其是Vault内容和LLM交互数据）的使用方式和范围，优先本地处理。
*   **NFR-CONF-1 (可配置性)**：允许用户配置LLM服务接入点、知识库索引更新频率、工具使用权限等。
*   **NFR-ROBUST-1 (鲁棒性)**：插件在处理无效指令、工具调用失败、网络异常等情况时应能优雅降级并提供清晰错误提示。

### 6. 发布/验收标准 (高级版 - 初步)
*   成功集成至少一种 LLM 服务 (云端或本地)。
*   实现对 Vault 内容的基本语义查询。
*   成功实现至少2-3种核心工具的LLM驱动调用（例如：插件管理、Vault查询、网络搜索、记忆存取）。
*   用户可以通过自然语言指令完成至少一个结合了LLM理解、Vault查询和内容生成的简单任务场景。
*   JS脚本执行具备基础的沙箱保护。
