import { App, TFile, EventRef } from 'obsidian';
import { VaultIndexer } from './VaultIndexer';
import { VaultFile } from '@/types/vault';

/**
 * Vault文件监听器
 * 监听文件变化并自动更新索引
 */
export class VaultWatcher {
  private app: App;
  private indexer: VaultIndexer;
  private eventRefs: EventRef[] = [];
  private updateQueue: Set<string> = new Set();
  private updateTimer: NodeJS.Timeout | null = null;
  private batchUpdateDelay: number = 1000; // 1秒延迟批量更新
  private isEnabled: boolean = false;

  constructor(app: App, indexer: VaultIndexer) {
    this.app = app;
    this.indexer = indexer;
  }

  /**
   * 启动文件监听
   */
  start(): void {
    if (this.isEnabled) return;

    this.isEnabled = true;

    // 监听文件创建
    const createRef = this.app.vault.on('create', (file) => {
      if (file instanceof TFile && file.extension === 'md') {
        this.scheduleUpdate(file.path, 'create');
      }
    });

    // 监听文件修改
    const modifyRef = this.app.vault.on('modify', (file) => {
      if (file instanceof TFile && file.extension === 'md') {
        this.scheduleUpdate(file.path, 'modify');
      }
    });

    // 监听文件删除
    const deleteRef = this.app.vault.on('delete', (file) => {
      if (file instanceof TFile && file.extension === 'md') {
        this.scheduleUpdate(file.path, 'delete');
      }
    });

    // 监听文件重命名
    const renameRef = this.app.vault.on('rename', (file, oldPath) => {
      if (file instanceof TFile && file.extension === 'md') {
        this.scheduleUpdate(oldPath, 'delete');
        this.scheduleUpdate(file.path, 'create');
      }
    });

    this.eventRefs = [createRef, modifyRef, deleteRef, renameRef];
    console.log('VaultWatcher started');
  }

  /**
   * 停止文件监听
   */
  stop(): void {
    if (!this.isEnabled) return;

    this.isEnabled = false;

    // 移除事件监听器
    this.eventRefs.forEach(ref => {
      this.app.vault.offref(ref);
    });
    this.eventRefs = [];

    // 清理更新定时器
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
      this.updateTimer = null;
    }

    this.updateQueue.clear();
    console.log('VaultWatcher stopped');
  }

  /**
   * 调度更新
   */
  private scheduleUpdate(filePath: string, operation: 'create' | 'modify' | 'delete'): void {
    this.updateQueue.add(`${operation}:${filePath}`);

    // 重置定时器
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
    }

    this.updateTimer = setTimeout(() => {
      this.processBatchUpdates();
    }, this.batchUpdateDelay);
  }

  /**
   * 处理批量更新
   */
  private async processBatchUpdates(): Promise<void> {
    if (this.updateQueue.size === 0) return;

    const updates = Array.from(this.updateQueue);
    this.updateQueue.clear();
    this.updateTimer = null;

    console.log(`Processing ${updates.length} file updates`);

    const operations = {
      create: new Set<string>(),
      modify: new Set<string>(),
      delete: new Set<string>()
    };

    // 分类操作
    updates.forEach(update => {
      const [operation, filePath] = update.split(':', 2);
      operations[operation as keyof typeof operations].add(filePath);
    });

    try {
      // 处理删除操作
      for (const filePath of operations.delete) {
        await this.indexer.deleteIndex(filePath);
      }

      // 处理创建和修改操作
      const filesToIndex = [...operations.create, ...operations.modify];
      await this.indexFiles(filesToIndex);

      console.log(`Batch update completed: ${filesToIndex.length} files indexed, ${operations.delete.size} files deleted`);
    } catch (error) {
      console.error('Failed to process batch updates:', error);
    }
  }

  /**
   * 索引文件列表
   */
  private async indexFiles(filePaths: string[]): Promise<void> {
    const vaultFiles: VaultFile[] = [];

    for (const filePath of filePaths) {
      try {
        const file = this.app.vault.getAbstractFileByPath(filePath);
        if (!(file instanceof TFile)) {
          console.warn(`File not found: ${filePath}`);
          continue;
        }

        const content = await this.app.vault.read(file);
        const vaultFile: VaultFile = {
          path: filePath,
          name: file.name,
          extension: file.extension,
          size: file.stat.size,
          mtime: file.stat.mtime,
          content
        };

        vaultFiles.push(vaultFile);
      } catch (error) {
        console.error(`Failed to read file ${filePath}:`, error);
      }
    }

    if (vaultFiles.length > 0) {
      await this.indexer.indexFiles(vaultFiles);
    }
  }

  /**
   * 强制更新所有文件
   */
  async forceUpdateAll(): Promise<void> {
    const files = this.app.vault.getMarkdownFiles();
    const filePaths = files.map(file => file.path);
    
    console.log(`Force updating ${filePaths.length} files`);
    await this.indexFiles(filePaths);
  }

  /**
   * 获取监听状态
   */
  getStatus(): {
    enabled: boolean;
    queueSize: number;
    isProcessing: boolean;
  } {
    return {
      enabled: this.isEnabled,
      queueSize: this.updateQueue.size,
      isProcessing: this.updateTimer !== null
    };
  }

  /**
   * 设置批量更新延迟
   */
  setBatchUpdateDelay(delay: number): void {
    this.batchUpdateDelay = Math.max(100, delay); // 最小100ms
  }

  /**
   * 手动触发文件更新
   */
  async updateFile(filePath: string): Promise<void> {
    try {
      const file = this.app.vault.getAbstractFileByPath(filePath);
      if (!(file instanceof TFile)) {
        throw new Error(`File not found: ${filePath}`);
      }

      await this.indexFiles([filePath]);
      console.log(`Manually updated file: ${filePath}`);
    } catch (error) {
      console.error(`Failed to manually update file ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * 获取待处理的更新队列
   */
  getPendingUpdates(): string[] {
    return Array.from(this.updateQueue);
  }

  /**
   * 清空更新队列
   */
  clearUpdateQueue(): void {
    this.updateQueue.clear();
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
      this.updateTimer = null;
    }
  }
}
