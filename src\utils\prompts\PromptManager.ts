import { PromptTemplateEngine, PromptTemplate, PromptContext } from './PromptTemplate';
import { BaseTemplates } from './BaseTemplates';
import { Tool, ToolDefinition } from '@/types/tools';
import { ConversationContext } from '@/types/memory';

/**
 * 提示管理器
 * 负责动态生成和管理提示，根据上下文和可用工具组装最适合的提示
 */
export class PromptManager {
  private templateEngine: PromptTemplateEngine;
  private availableTools: Tool[] = [];
  private conversationHistory: ConversationContext[] = [];

  constructor() {
    this.templateEngine = new PromptTemplateEngine();
    this.initializeBaseTemplates();
  }

  /**
   * 初始化基础模板
   */
  private initializeBaseTemplates(): void {
    const templates = BaseTemplates.getAllTemplates();
    templates.forEach(template => {
      this.templateEngine.registerTemplate(template);
    });
  }

  /**
   * 设置可用工具
   */
  setAvailableTools(tools: Tool[]): void {
    this.availableTools = tools;
  }

  /**
   * 设置对话历史
   */
  setConversationHistory(history: ConversationContext[]): void {
    this.conversationHistory = history;
  }

  /**
   * 生成系统提示
   */
  generateSystemPrompt(): string {
    const toolDescriptions = this.availableTools.map(tool => ({
      name: tool.name,
      description: tool.description,
    }));

    return this.templateEngine.render('system_prompt', {
      tools: toolDescriptions,
    });
  }

  /**
   * 生成任务分析提示
   */
  generateTaskAnalysisPrompt(userInput: string, context?: string): string {
    return this.templateEngine.render('task_analysis', {
      userInput,
      context: context || this.buildContextFromHistory(),
    });
  }

  /**
   * 生成工具选择提示
   */
  generateToolSelectionPrompt(taskDescription: string): string {
    const toolDescriptions = this.availableTools.map(tool => ({
      name: tool.name,
      description: tool.description,
      parameters: Object.entries(tool.parameters.properties || {}).map(([name, param]) => ({
        name,
        type: param.type,
        description: param.description,
      })),
    }));

    return this.templateEngine.render('tool_selection', {
      taskDescription,
      tools: toolDescriptions,
    });
  }

  /**
   * 生成Vault查询提示
   */
  generateVaultQueryPrompt(userInput: string): string {
    return this.templateEngine.render('vault_query', {
      userInput,
    });
  }

  /**
   * 生成内容总结提示
   */
  generateContentSummaryPrompt(
    userInput: string, 
    results: any[], 
    suggestions?: string
  ): string {
    return this.templateEngine.render('content_summary', {
      userInput,
      results,
      suggestions,
    });
  }

  /**
   * 生成错误处理提示
   */
  generateErrorHandlingPrompt(userInput: string, error: string): string {
    return this.templateEngine.render('error_handling', {
      userInput,
      error,
    });
  }

  /**
   * 生成工具结果处理提示
   */
  generateToolResultProcessingPrompt(
    userInput: string,
    toolName: string,
    parameters: any,
    result: any
  ): string {
    return this.templateEngine.render('tool_result_processing', {
      userInput,
      toolName,
      parameters,
      result,
    });
  }

  /**
   * 动态生成完整的对话提示
   */
  generateConversationPrompt(
    userInput: string,
    includeHistory: boolean = true,
    maxHistoryLength: number = 5
  ): {
    systemPrompt: string;
    messages: Array<{ role: string; content: string }>;
  } {
    const systemPrompt = this.generateSystemPrompt();
    const messages: Array<{ role: string; content: string }> = [];

    // 添加历史对话（如果需要）
    if (includeHistory && this.conversationHistory.length > 0) {
      const recentHistory = this.conversationHistory
        .slice(-maxHistoryLength)
        .flatMap(conv => conv.messages.slice(-2)); // 每个对话取最后2条消息

      recentHistory.forEach(msg => {
        messages.push({
          role: msg.role,
          content: msg.content,
        });
      });
    }

    // 添加当前用户输入
    messages.push({
      role: 'user',
      content: userInput,
    });

    return {
      systemPrompt,
      messages,
    };
  }

  /**
   * 根据任务类型生成专门的提示
   */
  generateTaskSpecificPrompt(
    taskType: 'query' | 'create' | 'analyze' | 'execute',
    userInput: string,
    additionalContext?: any
  ): string {
    switch (taskType) {
      case 'query':
        return this.generateVaultQueryPrompt(userInput);
      
      case 'create':
        return this.generateCreationPrompt(userInput, additionalContext);
      
      case 'analyze':
        return this.generateAnalysisPrompt(userInput, additionalContext);
      
      case 'execute':
        return this.generateExecutionPrompt(userInput, additionalContext);
      
      default:
        return this.generateTaskAnalysisPrompt(userInput);
    }
  }

  /**
   * 生成创建内容的提示
   */
  private generateCreationPrompt(userInput: string, context?: any): string {
    return `基于以下要求创建内容：

用户需求：${userInput}

${context ? `参考信息：${JSON.stringify(context, null, 2)}` : ''}

请创建符合要求的内容，确保：
1. 内容结构清晰
2. 信息准确完整
3. 格式符合Obsidian Markdown规范
4. 包含适当的链接和标签`;
  }

  /**
   * 生成分析任务的提示
   */
  private generateAnalysisPrompt(userInput: string, context?: any): string {
    return `请分析以下内容：

分析目标：${userInput}

${context ? `相关数据：${JSON.stringify(context, null, 2)}` : ''}

请提供：
1. 关键发现和洞察
2. 数据趋势和模式
3. 结论和建议
4. 支撑证据`;
  }

  /**
   * 生成执行任务的提示
   */
  private generateExecutionPrompt(userInput: string, context?: any): string {
    return `执行以下任务：

任务描述：${userInput}

${context ? `执行环境：${JSON.stringify(context, null, 2)}` : ''}

请：
1. 确认任务理解正确
2. 选择合适的工具和方法
3. 按步骤执行
4. 报告执行结果`;
  }

  /**
   * 从对话历史构建上下文
   */
  private buildContextFromHistory(): string {
    if (this.conversationHistory.length === 0) {
      return '';
    }

    const recentConversation = this.conversationHistory.slice(-1)[0];
    const recentMessages = recentConversation.messages.slice(-3);

    return recentMessages
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n');
  }

  /**
   * 注册自定义模板
   */
  registerCustomTemplate(template: PromptTemplate): void {
    this.templateEngine.registerTemplate(template);
  }

  /**
   * 获取模板引擎（用于高级用法）
   */
  getTemplateEngine(): PromptTemplateEngine {
    return this.templateEngine;
  }

  /**
   * 优化提示长度
   */
  optimizePromptLength(prompt: string, maxLength: number = 4000): string {
    if (prompt.length <= maxLength) {
      return prompt;
    }

    // 简单的截断策略，保留开头和结尾
    const keepStart = Math.floor(maxLength * 0.6);
    const keepEnd = Math.floor(maxLength * 0.3);
    
    const start = prompt.substring(0, keepStart);
    const end = prompt.substring(prompt.length - keepEnd);
    
    return `${start}\n\n[... 内容已截断 ...]\n\n${end}`;
  }

  /**
   * 验证提示质量
   */
  validatePromptQuality(prompt: string): {
    score: number;
    issues: string[];
    suggestions: string[];
  } {
    const issues: string[] = [];
    const suggestions: string[] = [];
    let score = 100;

    // 检查长度
    if (prompt.length < 50) {
      issues.push('提示过短，可能缺少必要信息');
      score -= 20;
    } else if (prompt.length > 8000) {
      issues.push('提示过长，可能影响处理效率');
      score -= 10;
    }

    // 检查结构
    if (!prompt.includes('：') && !prompt.includes(':')) {
      suggestions.push('建议添加明确的指令结构');
      score -= 5;
    }

    // 检查变量替换
    if (prompt.includes('{{') || prompt.includes('}}')) {
      issues.push('存在未替换的模板变量');
      score -= 15;
    }

    return {
      score: Math.max(0, score),
      issues,
      suggestions,
    };
  }
}
