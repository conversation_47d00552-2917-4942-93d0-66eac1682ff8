# AI Coach Advanced - 侧边栏与记忆管理指南

## 🎨 侧边栏界面

### 📱 新的用户界面

AI Coach Advanced 现在采用侧边栏形式，提供更好的用户体验：

- **🔄 持久化界面** - 不再是弹出窗口，侧边栏可以保持打开状态
- **📋 标签页设计** - 对话和记忆管理分别在不同标签页
- **🎯 更好的集成** - 与Obsidian界面完美融合
- **⚡ 快速访问** - 通过命令面板或快捷键快速打开

### 🚀 打开侧边栏

#### 方法1: 命令面板
1. 按 `Ctrl+P` (Windows/Linux) 或 `Cmd+P` (Mac) 打开命令面板
2. 输入 "AI助手" 或 "AI Coach"
3. 选择 "打开AI助手侧边栏"

#### 方法2: 快捷键
- 可以在Obsidian设置中为 "打开AI助手侧边栏" 命令设置自定义快捷键

#### 方法3: 状态栏
- 点击状态栏中的AI Coach图标

### 📋 标签页功能

#### 💬 对话标签页
- **实时对话** - 与AI助手进行自然语言交流
- **工具调用** - 自动调用各种工具完成任务
- **执行详情** - 查看工具调用的详细过程
- **聊天历史** - 保持对话上下文

#### 🧠 记忆标签页
- **查看记忆** - 浏览所有长期记忆
- **编辑记忆** - 修改现有记忆内容
- **添加记忆** - 手动添加新的记忆
- **删除记忆** - 移除不需要的记忆

## 🧠 长期记忆管理

### 📖 什么是长期记忆

长期记忆是AI助手用来记住重要信息的系统：

- **个人偏好** - 你的工作习惯、喜好设置
- **项目信息** - 正在进行的项目详情
- **学习内容** - 重要的学习笔记和知识点
- **工作流程** - 常用的工作方法和流程
- **联系信息** - 重要的人员和联系方式

### 🔍 查看记忆

1. **打开记忆标签页**
   - 在侧边栏中点击 "🧠 记忆" 标签
   - 或使用命令 "管理长期记忆"

2. **浏览记忆列表**
   - 每个记忆显示键名、内容预览和创建日期
   - 按时间倒序排列，最新的在前面

3. **刷新记忆**
   - 点击 "🔄 刷新记忆" 按钮获取最新列表

### ✏️ 编辑记忆

1. **选择要编辑的记忆**
   - 在记忆列表中找到目标记忆
   - 点击 "编辑" 按钮

2. **修改记忆内容**
   - **记忆键名**: 记忆的唯一标识符
   - **记忆内容**: 具体的信息内容
   - **标签**: 用逗号分隔的标签，便于分类

3. **保存更改**
   - 点击 "保存" 按钮确认修改
   - 系统会自动更新时间戳

### ➕ 添加新记忆

1. **点击添加按钮**
   - 在记忆列表底部点击 "+ 添加新记忆"

2. **填写记忆信息**
   - **键名**: 为记忆起一个有意义的名称
   - **内容**: 详细的记忆内容
   - **标签**: 可选的分类标签

3. **保存记忆**
   - 点击 "保存" 按钮创建新记忆

### 🗑️ 删除记忆

1. **选择要删除的记忆**
   - 在记忆列表中找到目标记忆
   - 点击 "删除" 按钮

2. **确认删除**
   - 系统会弹出确认对话框
   - 点击 "确定" 完成删除

⚠️ **注意**: 删除操作不可撤销，请谨慎操作

## 🎯 使用场景

### 📝 个人信息管理

```
记忆键名: 个人偏好
记忆内容: 
- 喜欢使用Markdown格式
- 工作时间: 9:00-18:00
- 常用编程语言: Python, TypeScript
- 项目管理方法: GTD + 番茄工作法
标签: 个人, 偏好, 工作习惯
```

### 🚀 项目跟踪

```
记忆键名: 网站重构项目
记忆内容:
- 项目状态: 进行中
- 技术栈: React + TypeScript + Vite
- 截止日期: 2024年7月15日
- 负责人: 张三
- 关键里程碑: 设计完成(✓), 开发(50%), 测试(待定)
标签: 项目, 开发, 重构
```

### 📚 学习记录

```
记忆键名: 机器学习学习进度
记忆内容:
- 当前学习: 深度学习基础
- 已完成: 线性回归, 逻辑回归, 决策树
- 下一步: 神经网络, CNN
- 学习资源: 《深度学习》花书, Coursera课程
- 实践项目: 图像分类器
标签: 学习, 机器学习, AI
```

### 👥 联系信息

```
记忆键名: 重要联系人
记忆内容:
- 项目经理: 李四 (<EMAIL>)
- 技术负责人: 王五 (<EMAIL>)
- 设计师: 赵六 (<EMAIL>)
- 会议时间: 每周三下午3点
标签: 联系人, 团队, 工作
```

## 🔧 高级功能

### 🏷️ 标签系统

使用标签来组织和分类记忆：

- **工作相关**: `工作`, `项目`, `会议`, `任务`
- **学习相关**: `学习`, `课程`, `书籍`, `技能`
- **个人相关**: `个人`, `偏好`, `习惯`, `目标`
- **技术相关**: `编程`, `工具`, `框架`, `语言`

### 🔍 搜索和过滤

虽然当前版本还没有内置搜索功能，但你可以：

1. **使用描述性键名** - 便于快速定位
2. **合理使用标签** - 按类别组织记忆
3. **定期整理** - 删除过时的记忆

### 🔄 自动记忆

AI助手在对话过程中会自动学习和记忆：

- **用户偏好** - 从对话中学习你的习惯
- **项目信息** - 记住你提到的项目详情
- **工作流程** - 学习你常用的工作方法

## 🛠️ 故障排除

### 记忆加载失败

**问题**: 记忆标签页显示 "获取记忆失败"

**解决方案**:
1. 检查插件是否正确初始化
2. 重新加载插件
3. 查看控制台错误信息

### 记忆保存失败

**问题**: 编辑或添加记忆时保存失败

**解决方案**:
1. 确保记忆键名不为空
2. 检查记忆内容格式
3. 重试操作

### 侧边栏无法打开

**问题**: 命令执行后侧边栏没有出现

**解决方案**:
1. 检查插件是否启用
2. 重启Obsidian
3. 查看插件设置

## 📞 获取帮助

如果遇到问题：

1. **查看调试日志** - 打开开发者工具查看控制台
2. **重启插件** - 禁用后重新启用插件
3. **重启Obsidian** - 完全重启应用程序
4. **报告问题** - 通过GitHub Issues报告具体问题

---

通过新的侧边栏界面和记忆管理功能，AI Coach Advanced 为你提供了更强大、更个性化的AI助手体验！🎉
