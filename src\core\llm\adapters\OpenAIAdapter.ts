import { LLMInterface, ModelInfo, LLMError, LLMErrorCodes } from '../LLMInterface';
import { LLMMessage, LLMResponse, LLMGenerateOptions, ToolDefinition } from '@/types/llm';

/**
 * OpenAI API适配器
 * 支持GPT系列模型和OpenAI兼容的API
 */
export class OpenAIAdapter extends LLMInterface {
  private readonly defaultBaseUrl = 'https://api.openai.com/v1';

  constructor(apiKey: string, model: string, baseUrl?: string, timeout: number = 30000) {
    super(apiKey, model, baseUrl || 'https://api.openai.com/v1', timeout);
  }

  async generateText(messages: LLMMessage[], options?: LLMGenerateOptions): Promise<LLMResponse> {
    try {
      const requestBody = this.buildRequestBody(messages, options);
      const response = await this.makeRequest('/chat/completions', requestBody);
      
      return this.parseResponse(response);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async generateWithTools(
    messages: LLMMessage[], 
    tools: ToolDefinition[], 
    options?: LLMGenerateOptions
  ): Promise<LLMResponse> {
    try {
      const requestBody = this.buildRequestBody(messages, options, tools);
      const response = await this.makeRequest('/chat/completions', requestBody);
      
      return this.parseResponse(response);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async generateTextStream(
    messages: LLMMessage[],
    onChunk: (chunk: string) => void,
    options?: LLMGenerateOptions
  ): Promise<void> {
    try {
      const requestBody = this.buildRequestBody(messages, { ...options, stream: true });
      await this.makeStreamRequest('/chat/completions', requestBody, onChunk);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async validateConnection(): Promise<boolean> {
    try {
      await this.makeRequest('/models');
      return true;
    } catch (error) {
      console.error('OpenAI connection validation failed:', error);
      return false;
    }
  }

  async getModelInfo(): Promise<ModelInfo> {
    const modelMap: Record<string, Partial<ModelInfo>> = {
      'gpt-3.5-turbo': {
        maxTokens: 4096,
        supportsFunctionCalling: true,
        costPer1kTokens: { input: 0.0015, output: 0.002 }
      },
      'gpt-4': {
        maxTokens: 8192,
        supportsFunctionCalling: true,
        costPer1kTokens: { input: 0.03, output: 0.06 }
      },
      'gpt-4-turbo-preview': {
        maxTokens: 128000,
        supportsFunctionCalling: true,
        costPer1kTokens: { input: 0.01, output: 0.03 }
      }
    };

    const modelInfo = modelMap[this.model] || {};
    
    return {
      name: this.model,
      provider: 'OpenAI',
      maxTokens: modelInfo.maxTokens || 4096,
      supportsFunctionCalling: modelInfo.supportsFunctionCalling || false,
      supportsStreaming: true,
      costPer1kTokens: modelInfo.costPer1kTokens
    };
  }

  estimateTokens(text: string): number {
    // 简单的token估算：大约4个字符=1个token（英文），中文字符约1.5个字符=1个token
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const otherChars = text.length - chineseChars;
    return Math.ceil(chineseChars / 1.5 + otherChars / 4);
  }

  async getSupportedModels(): Promise<string[]> {
    try {
      const response = await this.makeRequest('/models');
      return response.data
        .filter((model: any) => model.id.includes('gpt'))
        .map((model: any) => model.id)
        .sort();
    } catch (error) {
      // 如果无法获取模型列表，返回默认支持的模型
      return ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo-preview'];
    }
  }

  private buildRequestBody(
    messages: LLMMessage[], 
    options?: LLMGenerateOptions,
    tools?: ToolDefinition[]
  ): any {
    const body: any = {
      model: this.model,
      messages: this.convertMessages(messages),
      max_tokens: options?.maxTokens || 2000,
      temperature: options?.temperature || 0.7,
      stream: options?.stream || false
    };

    if (options?.stopSequences?.length) {
      body.stop = options.stopSequences;
    }

    if (tools?.length) {
      body.tools = tools;
      body.tool_choice = 'auto';
    }

    return body;
  }

  private convertMessages(messages: LLMMessage[]): any[] {
    return messages.map(msg => {
      const converted: any = {
        role: msg.role,
        content: msg.content
      };

      if (msg.name) {
        converted.name = msg.name;
      }

      if (msg.tool_calls) {
        converted.tool_calls = msg.tool_calls;
      }

      if (msg.tool_call_id) {
        converted.tool_call_id = msg.tool_call_id;
      }

      return converted;
    });
  }

  private async makeRequest(endpoint: string, body?: any): Promise<any> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
    };

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(url, {
        method: body ? 'POST' : 'GET',
        headers,
        body: body ? JSON.stringify(body) : undefined,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`HTTP ${response.status}: ${errorData.error?.message || response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  private async makeStreamRequest(
    endpoint: string, 
    body: any, 
    onChunk: (chunk: string) => void
  ): Promise<void> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
    };

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`HTTP ${response.status}: ${errorData.error?.message || response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Failed to get response reader');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') return;

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices?.[0]?.delta?.content;
              if (content) {
                onChunk(content);
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  private parseResponse(response: any): LLMResponse {
    const choice = response.choices?.[0];
    if (!choice) {
      throw new Error('No choices in response');
    }

    const result: LLMResponse = {
      content: choice.message?.content || '',
      usage: response.usage ? {
        prompt_tokens: response.usage.prompt_tokens,
        completion_tokens: response.usage.completion_tokens,
        total_tokens: response.usage.total_tokens
      } : undefined
    };

    if (choice.message?.tool_calls) {
      result.tool_calls = choice.message.tool_calls;
    }

    return result;
  }

  private handleError(error: any): LLMError {
    if (error.name === 'AbortError') {
      return new LLMError('Request timeout', LLMErrorCodes.TIMEOUT);
    }

    if (error.message?.includes('401')) {
      return new LLMError('Invalid API key', LLMErrorCodes.INVALID_API_KEY, 401);
    }

    if (error.message?.includes('429')) {
      return new LLMError('Rate limit exceeded', LLMErrorCodes.RATE_LIMIT_EXCEEDED, 429);
    }

    if (error.message?.includes('404')) {
      return new LLMError('Model not found', LLMErrorCodes.MODEL_NOT_FOUND, 404);
    }

    if (error.message?.includes('insufficient_quota')) {
      return new LLMError('Insufficient quota', LLMErrorCodes.INSUFFICIENT_QUOTA, 403);
    }

    if (error.message?.includes('400')) {
      return new LLMError('Invalid request', LLMErrorCodes.INVALID_REQUEST, 400);
    }

    if (error.message?.includes('500') || error.message?.includes('502') || error.message?.includes('503')) {
      return new LLMError('Server error', LLMErrorCodes.SERVER_ERROR, 500);
    }

    return new LLMError(
      error.message || 'Unknown error',
      LLMErrorCodes.UNKNOWN_ERROR,
      undefined,
      error
    );
  }
}
