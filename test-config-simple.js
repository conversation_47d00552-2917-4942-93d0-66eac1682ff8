/**
 * 简单的配置更新测试
 * 只测试配置管理器的核心功能
 */

// 模拟 Obsidian Plugin
class MockPlugin {
  constructor() {
    this.savedData = null;
  }
  
  async loadData() {
    return this.savedData;
  }
  
  async saveData(data) {
    this.savedData = data;
    console.log('✅ 配置已保存');
    return Promise.resolve();
  }
}

// 模拟 Notice
class MockNotice {
  constructor(message) {
    console.log('📢 通知:', message);
  }
}

// 设置全局变量
global.Notice = MockNotice;

async function testConfigManager() {
  console.log('🧪 开始测试配置管理器...\n');
  
  try {
    // 动态导入配置管理器
    const fs = require('fs');
    const path = require('path');
    
    // 读取源代码文件
    const configManagerCode = fs.readFileSync(
      path.join(__dirname, 'src', 'core', 'config', 'ConfigManager.ts'), 
      'utf8'
    );
    
    const configTypesCode = fs.readFileSync(
      path.join(__dirname, 'src', 'types', 'config.ts'), 
      'utf8'
    );
    
    const llmFactoryCode = fs.readFileSync(
      path.join(__dirname, 'src', 'core', 'llm', 'LLMFactory.ts'), 
      'utf8'
    );
    
    // 简单的TypeScript到JavaScript转换
    function simpleTranspile(code) {
      return code
        .replace(/import\s+.*?from\s+['"][^'"]*['"];?\s*/g, '') // 移除import
        .replace(/export\s+/g, '') // 移除export
        .replace(/:\s*[A-Za-z<>[\]|&\s,{}]+(?=\s*[=;,)])/g, '') // 移除类型注解
        .replace(/\?\s*:/g, ':') // 移除可选属性的?
        .replace(/private\s+/g, '') // 移除private
        .replace(/public\s+/g, '') // 移除public
        .replace(/readonly\s+/g, '') // 移除readonly
        .replace(/async\s+/g, 'async ') // 保持async
        .replace(/interface\s+\w+\s*{[^}]*}/g, '') // 移除interface
        .replace(/type\s+\w+\s*=\s*[^;]+;/g, ''); // 移除type定义
    }
    
    // 创建执行环境
    const vm = require('vm');
    const context = {
      console,
      Promise,
      JSON,
      Date,
      setTimeout,
      btoa: (str) => Buffer.from(str).toString('base64'),
      atob: (str) => Buffer.from(str, 'base64').toString(),
      Notice: MockNotice,
      global: {},
      
      // 模拟的默认配置
      DEFAULT_CONFIG: {
        llm: {
          provider: 'openai',
          apiKey: '',
          model: 'gpt-3.5-turbo',
          maxTokens: 2000,
          temperature: 0.7,
          timeout: 30000,
          baseUrl: ''
        },
        tools: {
          vault: { enabled: true, indexingEnabled: true, chunkSize: 1000 },
          web: { enabled: true, apiKey: '', defaultEngine: 'duckduckgo' },
          javascript: { enabled: false, safeMode: true, timeout: 5000 },
          plugin: { enabled: true },
          memory: { enabled: true }
        },
        ui: {
          language: 'zh',
          showProgress: true,
          theme: 'auto'
        },
        security: {
          encryptApiKeys: false,
          maxRequestsPerMinute: 60,
          logLevel: 'info',
          enableAuditLog: false
        }
      },
      
      // 模拟LLMFactory
      LLMFactory: {
        validateConfig: function(config) {
          const errors = [];
          
          if (!config.apiKey || !config.apiKey.trim()) {
            errors.push('API密钥不能为空');
          }
          
          if (!config.model || !config.model.trim()) {
            errors.push('模型名称不能为空');
          }
          
          return {
            valid: errors.length === 0,
            errors
          };
        }
      }
    };
    
    // 执行配置管理器代码
    vm.createContext(context);
    
    // 简化的ConfigManager实现
    const configManagerImpl = `
      class ConfigManager {
        constructor(plugin) {
          this.plugin = plugin;
          this.config = JSON.parse(JSON.stringify(DEFAULT_CONFIG));
          this.configVersion = '1.0.0';
        }
        
        async loadConfig() {
          try {
            const data = await this.plugin.loadData();
            if (data) {
              this.config = this.mergeConfig(DEFAULT_CONFIG, data);
            }
          } catch (error) {
            console.error('Failed to load config:', error);
            this.config = JSON.parse(JSON.stringify(DEFAULT_CONFIG));
          }
          return this.config;
        }
        
        async saveConfig() {
          try {
            this.validateConfig();
            const configToSave = JSON.parse(JSON.stringify(this.config));
            configToSave.version = this.configVersion;
            configToSave.lastUpdated = new Date().toISOString();
            
            await this.plugin.saveData(configToSave);
          } catch (error) {
            console.error('Failed to save config:', error);
            throw error;
          }
        }
        
        getConfig() {
          return JSON.parse(JSON.stringify(this.config));
        }
        
        updateConfig(updates) {
          this.config = this.mergeConfig(this.config, updates);
        }
        
        async validateLLMConfig() {
          return LLMFactory.validateConfig(this.config.llm);
        }
        
        validateConfig() {
          if (this.config.llm.apiKey && this.config.llm.apiKey.trim()) {
            const llmValidation = LLMFactory.validateConfig(this.config.llm);
            if (!llmValidation.valid) {
              console.warn('LLM配置验证失败:', llmValidation.errors);
            }
          }
        }
        
        mergeConfig(target, source) {
          const result = JSON.parse(JSON.stringify(target));
          
          for (const key in source) {
            if (source[key] !== undefined) {
              if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
                result[key] = this.mergeConfig(result[key] || {}, source[key]);
              } else {
                result[key] = source[key];
              }
            }
          }
          
          return result;
        }
      }
      
      global.ConfigManager = ConfigManager;
    `;
    
    vm.runInContext(configManagerImpl, context);
    
    // 创建测试实例
    const plugin = new MockPlugin();
    const ConfigManager = context.global.ConfigManager;
    const configManager = new ConfigManager(plugin);
    
    console.log('✅ 配置管理器创建成功\n');
    
    // 测试1: 加载默认配置
    console.log('🔧 测试1: 加载默认配置...');
    const initialConfig = await configManager.loadConfig();
    console.log('✅ 默认配置加载成功');
    console.log('  - 提供商:', initialConfig.llm.provider);
    console.log('  - API密钥:', initialConfig.llm.apiKey || '(空)');
    console.log('  - 模型:', initialConfig.llm.model);
    console.log('');
    
    // 测试2: 更新API密钥
    console.log('🔧 测试2: 更新API密钥...');
    configManager.updateConfig({
      llm: {
        apiKey: 'test-api-key-12345'
      }
    });
    await configManager.saveConfig();
    
    const configAfterApiKey = configManager.getConfig();
    console.log('✅ API密钥更新成功:', configAfterApiKey.llm.apiKey);
    console.log('');
    
    // 测试3: 更新多个配置项
    console.log('🔧 测试3: 更新多个配置项...');
    configManager.updateConfig({
      llm: {
        provider: 'google',
        model: 'gemini-pro',
        temperature: 0.8
      },
      tools: {
        vault: {
          enabled: false
        }
      }
    });
    await configManager.saveConfig();
    
    const configAfterMultiple = configManager.getConfig();
    console.log('✅ 多项配置更新成功:');
    console.log('  - 提供商:', configAfterMultiple.llm.provider);
    console.log('  - 模型:', configAfterMultiple.llm.model);
    console.log('  - 温度:', configAfterMultiple.llm.temperature);
    console.log('  - API密钥保持:', configAfterMultiple.llm.apiKey);
    console.log('  - Vault工具:', configAfterMultiple.tools.vault.enabled);
    console.log('');
    
    // 测试4: 验证配置
    console.log('🔧 测试4: 验证配置...');
    const validation = await configManager.validateLLMConfig();
    console.log('✅ 配置验证结果:', validation.valid ? '通过' : '失败');
    if (!validation.valid) {
      console.log('❌ 验证错误:', validation.errors);
    }
    console.log('');
    
    // 测试5: 测试空API密钥的情况
    console.log('🔧 测试5: 测试空API密钥...');
    configManager.updateConfig({
      llm: {
        apiKey: ''
      }
    });
    await configManager.saveConfig();
    
    const emptyKeyValidation = await configManager.validateLLMConfig();
    console.log('✅ 空API密钥验证结果:', emptyKeyValidation.valid ? '通过' : '失败');
    if (!emptyKeyValidation.valid) {
      console.log('❌ 验证错误:', emptyKeyValidation.errors);
    }
    
    console.log('\n🎉 配置管理器测试全部通过！');
    return true;
    
  } catch (error) {
    console.error('\n❌ 配置管理器测试失败:');
    console.error('错误类型:', error.constructor.name);
    console.error('错误消息:', error.message);
    console.error('错误堆栈:', error.stack);
    return false;
  }
}

// 运行测试
if (require.main === module) {
  testConfigManager().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { testConfigManager };
