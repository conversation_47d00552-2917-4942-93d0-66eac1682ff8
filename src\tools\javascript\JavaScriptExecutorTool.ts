import { BaseTool, ToolConfig } from '../../core/tools/BaseTool';
import { ToolResult, ToolExecutionContext, ToolValidationResult } from '../../types/tools';
import { JSSandbox, SandboxOptions, SandboxResult } from './JSSandbox';
import { JSCodeValidator, ValidationOptions } from './JSCodeValidator';

/**
 * JavaScript执行工具
 * 提供安全的JavaScript代码执行环境
 */
export class JavaScriptExecutorTool extends BaseTool {
  private sandbox: JSSandbox;
  private validator: JSCodeValidator;
  private executionHistory: ExecutionRecord[] = [];
  private maxHistorySize: number = 50;

  constructor(options?: SandboxOptions) {
    const config: ToolConfig = {
      name: 'javascript_executor',
      description: '在安全沙箱中执行JavaScript代码',
      category: 'javascript',
      version: '1.0.0',
      parameters: {
        type: 'object',
        properties: {
          code: {
            type: 'string',
            description: '要执行的JavaScript代码'
          },
          context: {
            type: 'object',
            description: '执行上下文变量'
          },
          timeout: {
            type: 'number',
            description: '执行超时时间（毫秒）',
            default: 5000,
            minimum: 1000,
            maximum: 30000
          },
          validateOnly: {
            type: 'boolean',
            description: '仅验证代码，不执行',
            default: false
          },
          formatCode: {
            type: 'boolean',
            description: '是否格式化代码',
            default: true
          },
          allowDangerous: {
            type: 'boolean',
            description: '是否允许潜在危险的操作',
            default: false
          }
        },
        required: ['code']
      },
      permissions: {
        required: ['javascript_execute'],
        optional: ['javascript_dangerous'],
        dangerous: true,
        requiresConfirmation: true
      },
      metadata: {
        author: 'AI Coach Team',
        tags: ['javascript', 'execution', 'sandbox'],
        documentation: '在安全的沙箱环境中执行JavaScript代码',
        examples: [
          {
            name: '简单计算',
            description: '执行简单的数学计算',
            input: { code: 'const result = 2 + 3; console.log(result); result;' },
            expectedOutput: { result: 5, output: '5' }
          },
          {
            name: '数组操作',
            description: '对数组进行操作',
            input: { 
              code: 'const arr = [1, 2, 3]; const doubled = arr.map(x => x * 2); doubled;',
              context: {}
            },
            expectedOutput: { result: [2, 4, 6] }
          }
        ]
      }
    };

    super(config);
    this.sandbox = new JSSandbox(options);
    this.validator = new JSCodeValidator();
  }

  protected async executeInternal(
    args: Record<string, any>,
    context?: ToolExecutionContext
  ): Promise<ToolResult> {
    const startTime = Date.now();
    
    try {
      const {
        code,
        context: userContext = {},
        timeout = 5000,
        validateOnly = false,
        formatCode = true,
        allowDangerous = false
      } = args;

      // 验证代码
      const validationOptions: ValidationOptions = {
        format: formatCode,
        allowDangerousPatterns: allowDangerous
      };

      const validationResult = await this.validator.validateCode(code, validationOptions);
      
      if (!validationResult.valid) {
        const record = this.createExecutionRecord(code, {
          success: false,
          error: validationResult.errors.join('; '),
          executionTime: Date.now() - startTime
        }, context);
        this.addToHistory(record);

        return {
          success: false,
          error: `代码验证失败: ${validationResult.errors.join('; ')}`,
          data: {
            validation: validationResult
          },
          metadata: {
            executionTime: Date.now() - startTime,
            validationOnly: true
          }
        };
      }

      // 如果只是验证，返回验证结果
      if (validateOnly) {
        return {
          success: true,
          data: {
            validation: validationResult,
            processedCode: validationResult.processedCode
          },
          metadata: {
            executionTime: Date.now() - startTime,
            validationOnly: true
          }
        };
      }

      // 检查权限
      if (validationResult.warnings.length > 0 && !allowDangerous) {
        if (context && !context.permissions.includes('javascript_dangerous')) {
          return {
            success: false,
            error: '代码包含潜在风险，需要危险操作权限',
            data: {
              validation: validationResult,
              warnings: validationResult.warnings
            }
          };
        }
      }

      // 更新沙箱超时设置
      this.sandbox = new JSSandbox({ timeout });

      // 执行代码
      const executionResult = await this.sandbox.execute(
        validationResult.processedCode,
        userContext
      );

      // 记录执行历史
      const record = this.createExecutionRecord(code, executionResult, context);
      this.addToHistory(record);

      return {
        success: executionResult.success,
        data: {
          result: executionResult.result,
          output: executionResult.output,
          validation: validationResult,
          processedCode: validationResult.processedCode
        },
        error: executionResult.error,
        metadata: {
          executionTime: executionResult.executionTime,
          memoryUsed: executionResult.memoryUsed,
          codeLength: code.length,
          processedCodeLength: validationResult.processedCode.length
        }
      };

    } catch (error) {
      const record = this.createExecutionRecord(args.code, {
        success: false,
        error: error.message,
        executionTime: Date.now() - startTime
      }, context);
      this.addToHistory(record);

      return {
        success: false,
        error: `JavaScript执行失败: ${error.message}`,
        metadata: {
          executionTime: Date.now() - startTime,
          errorType: 'execution_error'
        }
      };
    }
  }

  protected async validateInternal(args: Record<string, any>): Promise<ToolValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const { code, timeout } = args;

    // 验证代码参数
    if (!code || typeof code !== 'string') {
      errors.push('代码参数必须是非空字符串');
    } else {
      if (code.length > 100000) {
        errors.push('代码长度不能超过100KB');
      }

      if (code.length < 5) {
        warnings.push('代码过短，可能无法执行有意义的操作');
      }
    }

    // 验证超时时间
    if (timeout && (timeout < 1000 || timeout > 30000)) {
      errors.push('超时时间必须在1000-30000毫秒之间');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 创建执行记录
   */
  private createExecutionRecord(
    code: string,
    result: SandboxResult,
    context?: ToolExecutionContext
  ): ExecutionRecord {
    return {
      id: this.generateExecutionId(),
      code: code.substring(0, 1000), // 限制存储的代码长度
      result,
      timestamp: new Date(),
      userId: context?.userId || 'anonymous',
      sessionId: context?.sessionId
    };
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(record: ExecutionRecord): void {
    this.executionHistory.push(record);
    
    // 限制历史记录大小
    if (this.executionHistory.length > this.maxHistorySize) {
      this.executionHistory = this.executionHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * 生成执行ID
   */
  private generateExecutionId(): string {
    return `js_exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取执行历史
   */
  getExecutionHistory(limit?: number): ExecutionRecord[] {
    const history = [...this.executionHistory].reverse();
    return limit ? history.slice(0, limit) : history;
  }

  /**
   * 获取执行统计
   */
  getExecutionStats(): {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageExecutionTime: number;
    totalCodeLength: number;
  } {
    const total = this.executionHistory.length;
    const successful = this.executionHistory.filter(r => r.result.success).length;
    const failed = total - successful;
    
    const avgTime = total > 0 
      ? this.executionHistory.reduce((sum, r) => sum + r.result.executionTime, 0) / total 
      : 0;

    const totalCodeLength = this.executionHistory.reduce((sum, r) => sum + r.code.length, 0);

    return {
      totalExecutions: total,
      successfulExecutions: successful,
      failedExecutions: failed,
      averageExecutionTime: Math.round(avgTime),
      totalCodeLength
    };
  }

  /**
   * 清理执行历史
   */
  clearExecutionHistory(): void {
    this.executionHistory = [];
  }

  /**
   * 获取代码建议
   */
  async getCodeSuggestions(code: string): Promise<string[]> {
    const validationResult = await this.validator.validateCode(code, { format: false });
    return validationResult.suggestions;
  }

  /**
   * 格式化代码
   */
  async formatCode(code: string): Promise<string> {
    const validationResult = await this.validator.validateCode(code, { format: true });
    return validationResult.processedCode;
  }
}

/**
 * 执行记录
 */
export interface ExecutionRecord {
  id: string;
  code: string;
  result: SandboxResult;
  timestamp: Date;
  userId: string;
  sessionId?: string;
}
