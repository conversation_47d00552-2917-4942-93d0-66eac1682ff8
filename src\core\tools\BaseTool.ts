import { 
  Tool, 
  ToolResult, 
  ToolParameters, 
  ToolPermissions, 
  ToolMetadata, 
  ToolCategory,
  ToolExecutionContext,
  ToolValidationResult,
  ToolResultMetadata
} from '@/types/tools';

/**
 * 工具基础抽象类
 * 提供工具实现的通用功能和标准接口
 */
export abstract class BaseTool implements Tool {
  public readonly name: string;
  public readonly description: string;
  public readonly category: ToolCategory;
  public readonly version: string;
  public readonly parameters: ToolParameters;
  public readonly permissions: ToolPermissions;
  public readonly metadata: ToolMetadata;

  private initialized: boolean = false;
  private executionCount: number = 0;
  private lastExecutionTime: Date | null = null;

  constructor(config: ToolConfig) {
    this.name = config.name;
    this.description = config.description;
    this.category = config.category;
    this.version = config.version || '1.0.0';
    this.parameters = config.parameters;
    this.permissions = config.permissions;
    this.metadata = config.metadata;
  }

  /**
   * 执行工具
   */
  async execute(args: Record<string, any>, context?: ToolExecutionContext): Promise<ToolResult> {
    const startTime = Date.now();
    const requestId = context?.requestId || this.generateRequestId();

    try {
      // 验证参数
      const validation = await this.validate(args);
      if (!validation.valid) {
        return {
          success: false,
          error: `参数验证失败: ${validation.errors.join(', ')}`,
          metadata: {
            executionTime: Date.now() - startTime,
            requestId,
          }
        };
      }

      // 检查权限
      if (context) {
        const permissionCheck = this.checkPermissions(context);
        if (!permissionCheck.allowed) {
          return {
            success: false,
            error: `权限不足: ${permissionCheck.reason}`,
            metadata: {
              executionTime: Date.now() - startTime,
              requestId,
            }
          };
        }
      }

      // 确保工具已初始化
      if (!this.initialized) {
        await this.initialize();
        this.initialized = true;
      }

      // 执行具体逻辑
      const result = await this.executeInternal(args, context);

      // 更新统计信息
      this.executionCount++;
      this.lastExecutionTime = new Date();

      // 添加元数据
      const metadata: ToolResultMetadata = {
        executionTime: Date.now() - startTime,
        requestId,
        ...result.metadata,
      };

      return {
        ...result,
        metadata,
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        metadata: {
          executionTime: Date.now() - startTime,
          requestId,
        }
      };
    }
  }

  /**
   * 验证参数
   */
  async validate(args: Record<string, any>): Promise<ToolValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查必需参数
    if (this.parameters.required) {
      for (const requiredParam of this.parameters.required) {
        if (!(requiredParam in args)) {
          errors.push(`缺少必需参数: ${requiredParam}`);
        }
      }
    }

    // 检查参数类型和格式
    for (const [paramName, paramDef] of Object.entries(this.parameters.properties)) {
      if (paramName in args) {
        const value = args[paramName];
        const typeCheck = this.validateParameterType(value, paramDef);
        if (!typeCheck.valid) {
          errors.push(`参数 ${paramName} ${typeCheck.error}`);
        }
      }
    }

    // 调用自定义验证
    const customValidation = await this.validateInternal(args);
    errors.push(...customValidation.errors);
    warnings.push(...customValidation.warnings);

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 初始化工具
   */
  async initialize(): Promise<void> {
    // 子类可以重写此方法进行初始化
  }

  /**
   * 清理工具资源
   */
  async cleanup(): Promise<void> {
    // 子类可以重写此方法进行清理
    this.initialized = false;
  }

  /**
   * 获取工具统计信息
   */
  getStats(): ToolStats {
    return {
      executionCount: this.executionCount,
      lastExecutionTime: this.lastExecutionTime,
      initialized: this.initialized,
    };
  }

  /**
   * 检查权限
   */
  protected checkPermissions(context: ToolExecutionContext): PermissionCheckResult {
    // 检查必需权限
    for (const requiredPermission of this.permissions.required) {
      if (!context.permissions.includes(requiredPermission)) {
        return {
          allowed: false,
          reason: `缺少必需权限: ${requiredPermission}`,
        };
      }
    }

    // 检查危险操作
    if (this.permissions.dangerous && !context.permissions.includes('dangerous_operations')) {
      return {
        allowed: false,
        reason: '此工具执行危险操作，需要特殊权限',
      };
    }

    return { allowed: true };
  }

  /**
   * 验证参数类型
   */
  protected validateParameterType(value: any, paramDef: any): { valid: boolean; error?: string } {
    switch (paramDef.type) {
      case 'string':
        if (typeof value !== 'string') {
          return { valid: false, error: '类型错误，期望字符串' };
        }
        if (paramDef.pattern && !new RegExp(paramDef.pattern).test(value)) {
          return { valid: false, error: '格式不匹配' };
        }
        break;

      case 'number':
        if (typeof value !== 'number') {
          return { valid: false, error: '类型错误，期望数字' };
        }
        if (paramDef.minimum !== undefined && value < paramDef.minimum) {
          return { valid: false, error: `值过小，最小值为 ${paramDef.minimum}` };
        }
        if (paramDef.maximum !== undefined && value > paramDef.maximum) {
          return { valid: false, error: `值过大，最大值为 ${paramDef.maximum}` };
        }
        break;

      case 'boolean':
        if (typeof value !== 'boolean') {
          return { valid: false, error: '类型错误，期望布尔值' };
        }
        break;

      case 'array':
        if (!Array.isArray(value)) {
          return { valid: false, error: '类型错误，期望数组' };
        }
        break;

      case 'object':
        if (typeof value !== 'object' || value === null || Array.isArray(value)) {
          return { valid: false, error: '类型错误，期望对象' };
        }
        break;
    }

    // 检查枚举值
    if (paramDef.enum && !paramDef.enum.includes(value)) {
      return { valid: false, error: `值不在允许范围内: ${paramDef.enum.join(', ')}` };
    }

    return { valid: true };
  }

  /**
   * 生成请求ID
   */
  protected generateRequestId(): string {
    return `${this.name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 子类需要实现的具体执行逻辑
   */
  protected abstract executeInternal(
    args: Record<string, any>, 
    context?: ToolExecutionContext
  ): Promise<ToolResult>;

  /**
   * 子类可以重写的自定义验证逻辑
   */
  protected async validateInternal(args: Record<string, any>): Promise<ToolValidationResult> {
    return { valid: true, errors: [], warnings: [] };
  }
}

/**
 * 工具配置接口
 */
export interface ToolConfig {
  name: string;
  description: string;
  category: ToolCategory;
  version?: string;
  parameters: ToolParameters;
  permissions: ToolPermissions;
  metadata: ToolMetadata;
}

/**
 * 工具统计信息
 */
export interface ToolStats {
  executionCount: number;
  lastExecutionTime: Date | null;
  initialized: boolean;
}

/**
 * 权限检查结果
 */
export interface PermissionCheckResult {
  allowed: boolean;
  reason?: string;
}
