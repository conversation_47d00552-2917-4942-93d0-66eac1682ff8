import { LLMInterface, LLMError, LLMErrorCodes } from './LLMInterface';
import { OpenAIAdapter } from './adapters/OpenAIAdapter';
import { GeminiAdapter } from './adapters/GeminiAdapter';
import { DeepSeekAdapter } from './adapters/DeepSeekAdapter';
import { LLMConfig as ConfigLLMConfig } from '@/types/config';

/**
 * LLM工厂类
 * 负责创建和管理不同的LLM提供商适配器
 */
export class LLMFactory {
  private static instances: Map<string, LLMInterface> = new Map();

  /**
   * 创建LLM实例
   * @param config LLM配置
   * @returns LLM接口实例
   */
  static createLLM(config: ConfigLLMConfig): LLMInterface {
    const key = this.generateKey(config);
    
    // 检查是否已有实例
    if (this.instances.has(key)) {
      const instance = this.instances.get(key)!;
      instance.updateConfig({
        apiKey: config.apiKey,
        model: config.model,
        baseUrl: config.baseUrl,
        timeout: config.timeout,
      });
      return instance;
    }

    // 创建新实例
    let instance: LLMInterface;
    
    switch (config.provider) {
      case 'openai':
        instance = new OpenAIAdapter(
          config.apiKey,
          config.model,
          config.baseUrl,
          config.timeout
        );
        break;
        
      case 'gemini':
        instance = new GeminiAdapter(
          config.apiKey,
          config.model,
          config.baseUrl,
          config.timeout
        );
        break;
        
      case 'deepseek':
        instance = new DeepSeekAdapter(
          config.apiKey,
          config.model,
          config.baseUrl || 'https://api.deepseek.com',
          config.timeout
        );
        break;
        
      case 'custom':
        // 对于自定义提供商，默认使用OpenAI兼容接口
        if (!config.baseUrl) {
          throw new LLMError(
            'Custom provider requires baseUrl',
            LLMErrorCodes.INVALID_REQUEST
          );
        }
        instance = new OpenAIAdapter(
          config.apiKey,
          config.model,
          config.baseUrl,
          config.timeout
        );
        break;
        
      default:
        throw new LLMError(
          `Unsupported LLM provider: ${config.provider}`,
          LLMErrorCodes.INVALID_REQUEST
        );
    }

    this.instances.set(key, instance);
    return instance;
  }

  /**
   * 获取已创建的LLM实例
   * @param config LLM配置
   * @returns LLM接口实例或undefined
   */
  static getLLM(config: ConfigLLMConfig): LLMInterface | undefined {
    const key = this.generateKey(config);
    return this.instances.get(key);
  }

  /**
   * 移除LLM实例
   * @param config LLM配置
   */
  static removeLLM(config: ConfigLLMConfig): void {
    const key = this.generateKey(config);
    this.instances.delete(key);
  }

  /**
   * 清除所有LLM实例
   */
  static clearAll(): void {
    this.instances.clear();
  }

  /**
   * 获取支持的提供商列表
   * @returns 提供商列表
   */
  static getSupportedProviders(): Array<{
    id: string;
    name: string;
    description: string;
    requiresApiKey: boolean;
    requiresBaseUrl: boolean;
    defaultModels: string[];
  }> {
    return [
      {
        id: 'openai',
        name: 'OpenAI',
        description: 'OpenAI GPT系列模型',
        requiresApiKey: true,
        requiresBaseUrl: false,
        defaultModels: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo-preview'],
      },
      {
        id: 'gemini',
        name: 'Google Gemini',
        description: 'Google Gemini系列模型',
        requiresApiKey: true,
        requiresBaseUrl: false,
        defaultModels: ['gemini-pro', 'gemini-pro-vision'],
      },
      {
        id: 'deepseek',
        name: 'DeepSeek',
        description: 'DeepSeek系列模型',
        requiresApiKey: true,
        requiresBaseUrl: false,
        defaultModels: ['deepseek-chat', 'deepseek-coder'],
      },
      {
        id: 'custom',
        name: '自定义',
        description: 'OpenAI兼容的自定义API',
        requiresApiKey: true,
        requiresBaseUrl: true,
        defaultModels: [],
      },
    ];
  }

  /**
   * 验证配置
   * @param config LLM配置
   * @returns 验证结果
   */
  static validateConfig(config: ConfigLLMConfig): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!config.apiKey?.trim()) {
      errors.push('API密钥不能为空');
    }

    if (!config.model?.trim()) {
      errors.push('模型名称不能为空');
    }

    if (config.provider === 'custom' && !config.baseUrl?.trim()) {
      errors.push('自定义提供商需要指定baseUrl');
    }

    if (config.timeout && config.timeout < 1000) {
      errors.push('超时时间不能少于1秒');
    }

    if (config.maxTokens && config.maxTokens < 1) {
      errors.push('最大token数必须大于0');
    }

    if (config.temperature && (config.temperature < 0 || config.temperature > 2)) {
      errors.push('温度值必须在0-2之间');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * 生成实例缓存键
   * @param config LLM配置
   * @returns 缓存键
   */
  private static generateKey(config: ConfigLLMConfig): string {
    return `${config.provider}:${config.model}:${config.baseUrl || 'default'}`;
  }
}
