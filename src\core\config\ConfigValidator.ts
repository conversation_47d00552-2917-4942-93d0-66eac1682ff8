import { PluginConfig, LLMConfig, ToolsConfig, UIConfig, SecurityConfig } from '@/types/config';

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

export class ConfigValidator {
  /**
   * 验证完整的插件配置
   */
  static validateConfig(config: PluginConfig): ValidationResult {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: []
    };

    // 验证LLM配置
    const llmResult = this.validateLLMConfig(config.llm);
    result.errors.push(...llmResult.errors);
    result.warnings.push(...llmResult.warnings);

    // 验证工具配置
    const toolsResult = this.validateToolsConfig(config.tools);
    result.errors.push(...toolsResult.errors);
    result.warnings.push(...toolsResult.warnings);

    // 验证UI配置
    const uiResult = this.validateUIConfig(config.ui);
    result.errors.push(...uiResult.errors);
    result.warnings.push(...uiResult.warnings);

    // 验证安全配置
    const securityResult = this.validateSecurityConfig(config.security);
    result.errors.push(...securityResult.errors);
    result.warnings.push(...securityResult.warnings);

    result.valid = result.errors.length === 0;
    return result;
  }

  /**
   * 验证LLM配置
   */
  static validateLLMConfig(config: LLMConfig): ValidationResult {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: []
    };

    // 检查必需字段
    if (!config.provider) {
      result.errors.push('LLM提供商不能为空');
    }

    if (!config.apiKey?.trim()) {
      result.errors.push('API密钥不能为空');
    } else if (config.apiKey.length < 10) {
      result.warnings.push('API密钥长度可能不正确');
    }

    if (!config.model?.trim()) {
      result.errors.push('模型名称不能为空');
    }

    // 检查数值范围
    if (config.maxTokens && (config.maxTokens < 1 || config.maxTokens > 100000)) {
      result.errors.push('最大Token数必须在1-100000之间');
    }

    if (config.temperature && (config.temperature < 0 || config.temperature > 2)) {
      result.errors.push('温度值必须在0-2之间');
    }

    if (config.timeout && config.timeout < 1000) {
      result.errors.push('超时时间不能少于1秒');
    }

    // 检查自定义提供商的baseUrl
    if (config.provider === 'custom' && !config.baseUrl?.trim()) {
      result.errors.push('自定义提供商需要指定baseUrl');
    }

    // 检查URL格式
    if (config.baseUrl && !this.isValidUrl(config.baseUrl)) {
      result.errors.push('baseUrl格式不正确');
    }

    result.valid = result.errors.length === 0;
    return result;
  }

  /**
   * 验证工具配置
   */
  static validateToolsConfig(config: ToolsConfig): ValidationResult {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: []
    };

    // 验证Vault配置
    if (config.vault.maxResults && (config.vault.maxResults < 1 || config.vault.maxResults > 100)) {
      result.errors.push('Vault最大搜索结果数必须在1-100之间');
    }

    if (config.vault.chunkSize && (config.vault.chunkSize < 100 || config.vault.chunkSize > 10000)) {
      result.errors.push('文本分块大小必须在100-10000字符之间');
    }

    // 验证Web搜索配置
    if (config.web.maxResults && (config.web.maxResults < 1 || config.web.maxResults > 50)) {
      result.errors.push('网络搜索最大结果数必须在1-50之间');
    }

    if (config.web.searchEngine === 'google' && config.web.enabled && !config.web.apiKey?.trim()) {
      result.warnings.push('使用Google搜索需要配置API密钥');
    }

    // 验证JavaScript配置
    if (config.javascript.timeout && (config.javascript.timeout < 1000 || config.javascript.timeout > 60000)) {
      result.errors.push('JavaScript执行超时时间必须在1-60秒之间');
    }

    if (config.javascript.memoryLimit && (config.javascript.memoryLimit < 1024 * 1024 || config.javascript.memoryLimit > 500 * 1024 * 1024)) {
      result.errors.push('JavaScript内存限制必须在1MB-500MB之间');
    }

    if (config.javascript.enabled && !config.javascript.requireConfirmation) {
      result.warnings.push('启用JavaScript执行但未要求用户确认，存在安全风险');
    }

    result.valid = result.errors.length === 0;
    return result;
  }

  /**
   * 验证UI配置
   */
  static validateUIConfig(config: UIConfig): ValidationResult {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: []
    };

    const validThemes = ['auto', 'light', 'dark'];
    if (!validThemes.includes(config.theme)) {
      result.errors.push('无效的主题设置');
    }

    const validLanguages = ['zh', 'en'];
    if (!validLanguages.includes(config.language)) {
      result.errors.push('无效的语言设置');
    }

    result.valid = result.errors.length === 0;
    return result;
  }

  /**
   * 验证安全配置
   */
  static validateSecurityConfig(config: SecurityConfig): ValidationResult {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: []
    };

    const validLogLevels = ['debug', 'info', 'warn', 'error'];
    if (!validLogLevels.includes(config.logLevel)) {
      result.errors.push('无效的日志级别');
    }

    if (config.maxRequestsPerMinute && (config.maxRequestsPerMinute < 1 || config.maxRequestsPerMinute > 1000)) {
      result.errors.push('请求频率限制必须在1-1000之间');
    }

    if (config.maxRequestsPerMinute && config.maxRequestsPerMinute < 10) {
      result.warnings.push('请求频率限制过低，可能影响使用体验');
    }

    if (!config.encryptApiKeys) {
      result.warnings.push('未启用API密钥加密，存在安全风险');
    }

    result.valid = result.errors.length === 0;
    return result;
  }

  /**
   * 检查配置兼容性
   */
  static checkCompatibility(config: PluginConfig): ValidationResult {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: []
    };

    // 检查工具之间的依赖关系
    if (config.tools.vault.enabled && !config.tools.vault.indexingEnabled) {
      result.warnings.push('启用了Vault查询但未启用索引，搜索性能可能较差');
    }

    if (config.tools.javascript.enabled && config.tools.plugin.enabled) {
      result.warnings.push('同时启用JavaScript执行和插件调用可能存在安全风险');
    }

    // 检查性能相关配置
    if (config.llm.maxTokens && config.llm.maxTokens > 4000 && config.llm.provider === 'openai' && config.llm.model === 'gpt-3.5-turbo') {
      result.warnings.push('GPT-3.5-turbo模型的最大Token数建议不超过4000');
    }

    if (config.tools.vault.chunkSize && config.tools.vault.chunkSize > 2000) {
      result.warnings.push('文本分块大小过大可能影响搜索精度');
    }

    result.valid = result.errors.length === 0;
    return result;
  }

  /**
   * 验证URL格式
   */
  private static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取配置建议
   */
  static getConfigSuggestions(config: PluginConfig): string[] {
    const suggestions: string[] = [];

    // LLM配置建议
    if (config.llm.temperature > 1.0) {
      suggestions.push('温度值较高，回答可能较为随机，建议设置为0.7-1.0');
    }

    if (config.llm.maxTokens && config.llm.maxTokens < 1000) {
      suggestions.push('最大Token数较低，可能影响回答完整性');
    }

    // 工具配置建议
    if (!config.tools.vault.enabled) {
      suggestions.push('建议启用Vault查询功能以获得更好的知识库体验');
    }

    if (config.tools.web.enabled && config.tools.web.searchEngine === 'duckduckgo') {
      suggestions.push('DuckDuckGo搜索结果可能有限，如需更好效果可考虑使用Google Custom Search');
    }

    // 安全配置建议
    if (config.security.maxRequestsPerMinute > 100) {
      suggestions.push('请求频率较高，请注意API费用控制');
    }

    return suggestions;
  }
}
