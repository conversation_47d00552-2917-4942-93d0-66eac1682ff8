import { App, Command } from 'obsidian';
import { BaseTool, ToolConfig } from '../../core/tools/BaseTool';
import { ToolResult, ToolExecutionContext, PluginCommand, PluginHotkey } from '../../types/tools';

/**
 * 插件命令查询工具
 * 用于查询指定插件的可执行命令
 */
export class PluginCommandQueryTool extends BaseTool {
  private app: App;

  constructor(app: App) {
    const config: ToolConfig = {
      name: 'plugin_command_query',
      description: '查询指定插件的可执行命令',
      category: 'plugin',
      version: '1.0.0',
      parameters: {
        type: 'object',
        properties: {
          pluginId: {
            type: 'string',
            description: '插件ID（可选，如果不提供则查询所有命令）'
          },
          commandFilter: {
            type: 'string',
            description: '命令名称过滤器（支持部分匹配）'
          },
          includeHotkeys: {
            type: 'boolean',
            description: '是否包含快捷键信息',
            default: true
          },
          sortBy: {
            type: 'string',
            description: '排序方式',
            enum: ['name', 'id', 'plugin'],
            default: 'name'
          }
        },
        required: []
      },
      permissions: {
        required: ['plugin_read'],
        optional: [],
        dangerous: false,
        requiresConfirmation: false
      },
      metadata: {
        author: 'AI Coach Team',
        tags: ['plugin', 'command', 'query'],
        documentation: '查询Obsidian插件的可执行命令列表',
        examples: [
          {
            name: '查询特定插件的命令',
            description: '获取指定插件的所有可用命令',
            input: { pluginId: 'calendar' },
            expectedOutput: { commands: [] }
          },
          {
            name: '搜索命令',
            description: '搜索包含特定关键词的命令',
            input: { commandFilter: 'create' },
            expectedOutput: { commands: [] }
          }
        ]
      }
    };

    super(config);
    this.app = app;
  }

  protected async executeInternal(
    args: Record<string, any>,
    context?: ToolExecutionContext
  ): Promise<ToolResult> {
    try {
      const {
        pluginId,
        commandFilter = '',
        includeHotkeys = true,
        sortBy = 'name'
      } = args;

      const commands = this.getCommands(pluginId, commandFilter, includeHotkeys);
      this.sortCommands(commands, sortBy);

      const result = {
        commands,
        totalCount: commands.length,
        pluginId: pluginId || 'all',
        filter: commandFilter
      };

      return {
        success: true,
        data: result,
        metadata: {
          source: 'obsidian_command_manager',
          timestamp: new Date().toISOString(),
          queryType: pluginId ? 'plugin_specific' : 'global'
        }
      };

    } catch (error) {
      return {
        success: false,
        error: `命令查询失败: ${error.message}`,
        metadata: {
          errorType: 'command_query_error'
        }
      };
    }
  }

  /**
   * 获取命令列表
   */
  private getCommands(
    pluginId?: string, 
    filter: string = '', 
    includeHotkeys: boolean = true
  ): PluginCommand[] {
    const commands: PluginCommand[] = [];
    const commandManager = this.app.commands;

    for (const [commandId, command] of Object.entries(commandManager.commands)) {
      // 如果指定了插件ID，只返回该插件的命令
      if (pluginId && !commandId.startsWith(`${pluginId}:`)) {
        continue;
      }

      // 应用过滤器
      if (filter && !command.name.toLowerCase().includes(filter.toLowerCase())) {
        continue;
      }

      const pluginCommand: PluginCommand = {
        id: commandId,
        name: command.name,
        description: this.getCommandDescription(command),
        callback: command.callback ? 'function' : undefined
      };

      // 添加快捷键信息
      if (includeHotkeys) {
        pluginCommand.hotkeys = this.getCommandHotkeys(commandId);
      }

      commands.push(pluginCommand);
    }

    return commands;
  }

  /**
   * 获取命令描述
   */
  private getCommandDescription(command: Command): string | undefined {
    // Obsidian的Command对象可能没有直接的description字段
    // 我们可以尝试从其他地方获取描述信息
    return (command as any).description || undefined;
  }

  /**
   * 获取命令的快捷键
   */
  private getCommandHotkeys(commandId: string): PluginHotkey[] {
    const hotkeys: PluginHotkey[] = [];
    
    try {
      // 获取快捷键管理器
      const hotkeyManager = this.app.hotkeyManager;
      const commandHotkeys = hotkeyManager.getHotkeys(commandId);

      if (commandHotkeys && commandHotkeys.length > 0) {
        for (const hotkey of commandHotkeys) {
          hotkeys.push({
            modifiers: hotkey.modifiers || [],
            key: hotkey.key || ''
          });
        }
      }
    } catch (error) {
      console.warn(`Failed to get hotkeys for command ${commandId}:`, error);
    }

    return hotkeys;
  }

  /**
   * 排序命令列表
   */
  private sortCommands(commands: PluginCommand[], sortBy: string): void {
    commands.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'id':
          return a.id.localeCompare(b.id);
        case 'plugin':
          const aPlugin = this.extractPluginFromCommandId(a.id);
          const bPlugin = this.extractPluginFromCommandId(b.id);
          return aPlugin.localeCompare(bPlugin);
        default:
          return a.name.localeCompare(b.name);
      }
    });
  }

  /**
   * 从命令ID中提取插件名称
   */
  private extractPluginFromCommandId(commandId: string): string {
    const parts = commandId.split(':');
    return parts.length > 1 ? parts[0] : 'core';
  }

  /**
   * 获取特定插件的命令统计
   */
  getPluginCommandStats(pluginId: string): {
    totalCommands: number;
    commandsWithHotkeys: number;
    commandsWithoutHotkeys: number;
  } {
    const commands = this.getCommands(pluginId, '', true);
    const commandsWithHotkeys = commands.filter(cmd => cmd.hotkeys && cmd.hotkeys.length > 0);

    return {
      totalCommands: commands.length,
      commandsWithHotkeys: commandsWithHotkeys.length,
      commandsWithoutHotkeys: commands.length - commandsWithHotkeys.length
    };
  }

  /**
   * 搜索命令
   */
  async searchCommands(query: string, options?: {
    pluginId?: string;
    exactMatch?: boolean;
    includeDescription?: boolean;
  }): Promise<PluginCommand[]> {
    const {
      pluginId,
      exactMatch = false,
      includeDescription = true
    } = options || {};

    const allCommands = this.getCommands(pluginId, '', true);
    const searchQuery = query.toLowerCase();

    return allCommands.filter(command => {
      const nameMatch = exactMatch 
        ? command.name.toLowerCase() === searchQuery
        : command.name.toLowerCase().includes(searchQuery);

      const descriptionMatch = includeDescription && command.description
        ? command.description.toLowerCase().includes(searchQuery)
        : false;

      const idMatch = command.id.toLowerCase().includes(searchQuery);

      return nameMatch || descriptionMatch || idMatch;
    });
  }

  /**
   * 检查命令是否存在
   */
  commandExists(commandId: string): boolean {
    return commandId in this.app.commands.commands;
  }

  /**
   * 获取命令详细信息
   */
  getCommandDetails(commandId: string): PluginCommand | null {
    const command = this.app.commands.commands[commandId];
    if (!command) {
      return null;
    }

    return {
      id: commandId,
      name: command.name,
      description: this.getCommandDescription(command),
      callback: command.callback ? 'function' : undefined,
      hotkeys: this.getCommandHotkeys(commandId)
    };
  }

  /**
   * 获取所有插件的命令统计
   */
  getAllPluginCommandStats(): Record<string, number> {
    const stats: Record<string, number> = {};
    const commandManager = this.app.commands;

    for (const commandId of Object.keys(commandManager.commands)) {
      const pluginName = this.extractPluginFromCommandId(commandId);
      stats[pluginName] = (stats[pluginName] || 0) + 1;
    }

    return stats;
  }

  /**
   * 获取最常用的命令（基于快捷键设置）
   */
  getMostUsedCommands(limit: number = 10): PluginCommand[] {
    const commands = this.getCommands(undefined, '', true);
    
    // 按快捷键数量排序（有快捷键的命令可能更常用）
    const sortedCommands = commands.sort((a, b) => {
      const aHotkeys = a.hotkeys?.length || 0;
      const bHotkeys = b.hotkeys?.length || 0;
      return bHotkeys - aHotkeys;
    });

    return sortedCommands.slice(0, limit);
  }
}
