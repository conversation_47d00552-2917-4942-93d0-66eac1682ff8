# AI Coach Advanced - 安装指南

## 📋 系统要求

### 必需条件
- **Obsidian**: 版本 1.0.0 或更高
- **操作系统**: Windows 10+, macOS 10.15+, 或 Linux
- **网络连接**: 用于LLM API调用和网络搜索

### 推荐配置
- **内存**: 8GB RAM 或更多
- **存储**: 至少 100MB 可用空间
- **网络**: 稳定的互联网连接

## 🚀 安装方法

### 方法一：手动安装（推荐）

1. **下载插件包**
   - 从 [Releases](https://github.com/your-repo/ai-coach-advanced/releases) 页面下载最新版本
   - 下载 `ai-coach-advanced-v0.2.0.zip` 文件

2. **解压文件**
   ```bash
   # 解压到临时目录
   unzip ai-coach-advanced-v0.2.0.zip -d temp/
   ```

3. **复制到插件目录**
   - 找到你的 Obsidian vault 目录
   - 导航到 `.obsidian/plugins/` 文件夹
   - 创建新文件夹 `ai-coach-advanced`
   - 将解压的文件复制到该文件夹

   ```
   your-vault/
   └── .obsidian/
       └── plugins/
           └── ai-coach-advanced/
               ├── main.js
               ├── manifest.json
               ├── styles.css
               └── README.md
   ```

4. **启用插件**
   - 打开 Obsidian
   - 进入 设置 → 第三方插件
   - 找到 "AI Coach Advanced"
   - 点击启用

### 方法二：开发者安装

如果你想从源码安装或进行开发：

1. **克隆仓库**
   ```bash
   git clone https://github.com/your-repo/ai-coach-advanced.git
   cd ai-coach-advanced
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **构建插件**
   ```bash
   npm run build:prod
   ```

4. **链接到 Obsidian**
   ```bash
   # 创建符号链接到你的 vault 插件目录
   ln -s $(pwd)/dist /path/to/your/vault/.obsidian/plugins/ai-coach-advanced
   ```

## ⚙️ 初始配置

### 1. 配置 LLM 提供商

安装完成后，你需要配置至少一个 LLM 提供商：

#### OpenAI 配置
1. 进入插件设置
2. 选择 "LLM 配置" 标签
3. 选择 "OpenAI" 作为提供商
4. 输入你的 OpenAI API 密钥
5. 选择模型（推荐 gpt-3.5-turbo 或 gpt-4）

#### Google Gemini 配置
1. 选择 "Google" 作为提供商
2. 输入你的 Google AI API 密钥
3. 选择模型（推荐 gemini-pro）

#### DeepSeek 配置
1. 选择 "DeepSeek" 作为提供商
2. 输入你的 DeepSeek API 密钥
3. 选择模型（推荐 deepseek-chat）

### 2. 配置工具设置

#### Vault 查询工具
- **启用索引**: 开启 Vault 内容索引
- **嵌入模型**: 选择文本嵌入模型
- **索引更新**: 配置自动索引更新

#### 网络搜索工具
- **启用搜索**: 开启网络搜索功能
- **默认引擎**: 选择默认搜索引擎
- **API 配置**: 配置搜索引擎 API（可选）

#### JavaScript 执行工具
- **启用执行**: 开启 JavaScript 代码执行
- **安全模式**: 启用安全沙箱模式
- **超时设置**: 配置代码执行超时时间

### 3. 验证安装

1. **打开命令面板** (Ctrl/Cmd + P)
2. **搜索 "AI Coach"** 相关命令
3. **运行 "打开AI助手对话"**
4. **发送测试消息**: "Hello, are you working?"

如果一切正常，你应该会收到 AI 助手的回复。

## 🔧 故障排除

### 常见问题

#### 1. 插件无法启用
- **检查文件权限**: 确保插件文件有正确的读取权限
- **检查文件完整性**: 重新下载并解压插件包
- **重启 Obsidian**: 完全关闭并重新打开 Obsidian

#### 2. API 调用失败
- **检查网络连接**: 确保能够访问 LLM 提供商的 API
- **验证 API 密钥**: 确保 API 密钥正确且有效
- **检查配额**: 确认 API 账户有足够的使用配额

#### 3. 工具执行错误
- **检查权限设置**: 确保工具有必要的执行权限
- **查看控制台**: 打开开发者工具查看错误信息
- **重置配置**: 尝试重置插件配置到默认值

#### 4. 性能问题
- **减少并发请求**: 避免同时发送多个复杂请求
- **优化 Vault 大小**: 对于大型 Vault，考虑分批索引
- **调整超时设置**: 增加 API 调用和工具执行的超时时间

### 日志和调试

#### 启用调试模式
1. 打开开发者工具 (F12)
2. 在控制台中输入: `app.plugins.plugins['ai-coach-advanced'].enableDebug()`
3. 查看详细的调试信息

#### 查看日志
- **控制台日志**: 在浏览器开发者工具中查看
- **插件日志**: 在插件设置中查看执行历史
- **错误报告**: 在设置页面导出错误报告

### 获取帮助

如果问题仍然存在：

1. **查看文档**: 阅读完整的用户指南
2. **检查 FAQ**: 查看常见问题解答
3. **提交 Issue**: 在 GitHub 上报告问题
4. **社区支持**: 在 Obsidian 社区寻求帮助

## 🔄 更新插件

### 自动更新（未来功能）
插件将支持自动检查和更新功能。

### 手动更新
1. 下载新版本的插件包
2. 备份当前配置（可选）
3. 替换插件文件
4. 重启 Obsidian
5. 验证更新是否成功

## 🗑️ 卸载插件

如果需要卸载插件：

1. **禁用插件**: 在 Obsidian 设置中禁用插件
2. **删除文件**: 删除 `.obsidian/plugins/ai-coach-advanced/` 文件夹
3. **清理数据**: 删除插件相关的配置文件（可选）
4. **重启 Obsidian**: 完全重启应用

---

## 📞 支持信息

- **版本**: v0.2.0
- **最后更新**: 2024年12月
- **兼容性**: Obsidian 1.0.0+
- **许可证**: MIT

如有任何问题，请参考用户指南或联系支持团队。
