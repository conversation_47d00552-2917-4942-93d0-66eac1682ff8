import { App } from 'obsidian';
import { LLMInterface } from '@/core/llm/LLMInterface';
import { LLMFactory } from '@/core/llm/LLMFactory';
import { PromptManager } from '@/utils/prompts/PromptManager';
import { ToolRegistry } from '@/core/tools/ToolRegistry';
import { ToolExecutor } from '@/core/tools/ToolExecutor';
import { TaskPlanner, TaskExecutionResult, PlanningContext } from './TaskPlanner';
import { ConversationManager, ConversationState } from './ConversationManager';
import { ToolCoordinator } from './ToolCoordinator';
import { ShortTermMemoryTool } from '@/tools/memory/ShortTermMemoryTool';
import { LongTermMemoryTool } from '@/tools/memory/LongTermMemoryTool';
import { ConfigManager } from '@/core/config/ConfigManager';
import { LLMConfig } from '@/types/config';

/**
 * LLM编排引擎
 * 插件的智能中枢，协调所有组件的工作
 */
export class OrchestrationEngine {
  private app: App;
  private configManager: ConfigManager;
  private llm: LLMInterface | null = null;
  private promptManager: PromptManager;
  private toolRegistry: ToolRegistry;
  private toolExecutor: ToolExecutor;
  private toolCoordinator: ToolCoordinator;
  private taskPlanner: TaskPlanner | null = null;
  private conversationManager: ConversationManager;
  private shortTermMemory: ShortTermMemoryTool;
  private longTermMemory: LongTermMemoryTool;
  private initialized: boolean = false;

  constructor(app: App, configManager: ConfigManager) {
    this.app = app;
    this.configManager = configManager;
    
    // 初始化核心组件
    this.promptManager = new PromptManager();
    this.toolRegistry = new ToolRegistry();
    this.toolExecutor = new ToolExecutor(this.toolRegistry);
    this.toolCoordinator = new ToolCoordinator(this.toolRegistry);
    this.shortTermMemory = new ShortTermMemoryTool(app);
    this.longTermMemory = new LongTermMemoryTool(app);
    this.conversationManager = new ConversationManager(
      this.shortTermMemory,
      this.longTermMemory
    );
  }

  /**
   * 初始化编排引擎
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // 初始化LLM
      await this.initializeLLM();
      
      // 初始化任务规划器
      if (this.llm) {
        this.taskPlanner = new TaskPlanner(
          this.llm,
          this.promptManager,
          this.toolRegistry
        );
      }

      // 初始化记忆工具
      await this.shortTermMemory.initialize();
      await this.longTermMemory.initialize();

      // 设置提示管理器的工具列表
      this.promptManager.setAvailableTools(this.toolRegistry.list());

      this.initialized = true;
      console.log('OrchestrationEngine initialized successfully');
    } catch (error) {
      console.error('Failed to initialize OrchestrationEngine:', error);
      throw error;
    }
  }

  /**
   * 处理用户输入
   */
  async processUserInput(input: string, context?: ProcessingContext): Promise<ProcessingResult> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.taskPlanner) {
      throw new Error('Task planner not initialized');
    }

    const startTime = Date.now();

    try {
      // 添加用户消息到对话
      await this.conversationManager.addUserMessage(input, context?.messageMetadata);

      // 构建规划上下文
      const planningContext: PlanningContext = {
        conversationHistory: await this.buildConversationHistory(),
        userPreferences: context?.userPreferences,
        availableTools: this.toolRegistry.list().map(tool => tool.name)
      };

      // 执行任务规划和执行
      const executionResult = await this.taskPlanner.planAndExecute(input, planningContext);

      // 添加助手回复到对话
      await this.conversationManager.addAssistantMessage(
        executionResult.result,
        executionResult.executionLog,
        {
          toolsUsed: executionResult.toolsUsed,
          iterations: executionResult.iterations
        }
      );

      return {
        success: executionResult.success,
        response: executionResult.result,
        executionLog: executionResult.executionLog,
        processingTime: Date.now() - startTime,
        toolsUsed: executionResult.toolsUsed || [],
        conversationId: this.conversationManager.getCurrentConversationId(),
        metadata: {
          iterations: executionResult.iterations,
          llmCalls: this.countLLMCalls(executionResult.executionLog),
          complexity: this.assessComplexity(executionResult.executionLog)
        }
      };

    } catch (error) {
      // 处理错误
      const errorMessage = `处理请求时发生错误: ${error.message}`;
      
      await this.conversationManager.addAssistantMessage(
        errorMessage,
        [{
          type: 'error',
          content: error.message,
          timestamp: new Date()
        }]
      );

      return {
        success: false,
        response: errorMessage,
        executionLog: [],
        processingTime: Date.now() - startTime,
        toolsUsed: [],
        conversationId: this.conversationManager.getCurrentConversationId(),
        error: error.message
      };
    }
  }

  /**
   * 开始新对话
   */
  async startNewConversation(title?: string): Promise<string> {
    return await this.conversationManager.startConversation({ title });
  }

  /**
   * 结束当前对话
   */
  async endCurrentConversation(summary?: string): Promise<void> {
    await this.conversationManager.endConversation(summary);
  }

  /**
   * 获取对话状态
   */
  getConversationState(): ConversationState {
    return this.conversationManager.getConversationState();
  }

  /**
   * 注册工具
   */
  async registerTool(tool: any): Promise<void> {
    await this.toolRegistry.register(tool);
    
    // 更新提示管理器的工具列表
    this.promptManager.setAvailableTools(this.toolRegistry.list());
  }

  /**
   * 获取可用工具列表
   */
  getAvailableTools(): any[] {
    return this.toolRegistry.list();
  }

  /**
   * 获取执行统计
   */
  async getExecutionStats(): Promise<ExecutionStats> {
    const conversationStats = await this.conversationManager.getConversationStats();
    const toolStats = this.toolRegistry.getStats();
    const coordinationStats = this.toolCoordinator.getExecutionStats();

    return {
      conversations: conversationStats,
      tools: toolStats,
      coordination: coordinationStats,
      llm: {
        provider: this.llm?.constructor.name || 'Unknown',
        initialized: !!this.llm
      }
    };
  }

  /**
   * 更新配置
   */
  async updateConfig(config: Partial<LLMConfig>): Promise<void> {
    await this.configManager.updateLLMConfig(config);
    
    // 重新初始化LLM
    await this.initializeLLM();
    
    // 重新初始化任务规划器
    if (this.llm) {
      this.taskPlanner = new TaskPlanner(
        this.llm,
        this.promptManager,
        this.toolRegistry
      );
    }
  }

  /**
   * 初始化LLM
   */
  private async initializeLLM(): Promise<void> {
    const llmConfig = this.configManager.getLLMConfig();
    this.llm = await LLMFactory.createLLM(llmConfig);
  }

  /**
   * 构建对话历史
   */
  private async buildConversationHistory(): Promise<string> {
    const recentMessages = await this.conversationManager.getRecentMessages(5);
    
    return recentMessages
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n');
  }

  /**
   * 统计LLM调用次数
   */
  private countLLMCalls(executionLog: any[]): number {
    return executionLog.filter(step => 
      step.type === 'thought' || step.type === 'action' || step.type === 'final_answer'
    ).length;
  }

  /**
   * 评估任务复杂度
   */
  private assessComplexity(executionLog: any[]): 'low' | 'medium' | 'high' {
    const toolCalls = executionLog.filter(step => step.type === 'tool_result').length;
    const iterations = executionLog.filter(step => step.type === 'thought').length;
    
    if (toolCalls === 0 && iterations <= 1) return 'low';
    if (toolCalls <= 2 && iterations <= 3) return 'medium';
    return 'high';
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    await this.conversationManager.endConversation();
    this.initialized = false;
  }
}

/**
 * 处理上下文
 */
export interface ProcessingContext {
  userPreferences?: Record<string, any>;
  messageMetadata?: Record<string, any>;
  priority?: 'low' | 'normal' | 'high';
}

/**
 * 处理结果
 */
export interface ProcessingResult {
  success: boolean;
  response: string;
  executionLog: any[];
  processingTime: number;
  toolsUsed: string[];
  conversationId: string | null;
  error?: string;
  metadata?: {
    iterations?: number;
    llmCalls?: number;
    complexity?: 'low' | 'medium' | 'high';
  };
}

/**
 * 执行统计
 */
export interface ExecutionStats {
  conversations: any;
  tools: any;
  coordination: any;
  llm: {
    provider: string;
    initialized: boolean;
  };
}
