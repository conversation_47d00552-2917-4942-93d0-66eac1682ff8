import { LLMInterface, ModelInfo, LLMError, LLMErrorCodes } from '../LLMInterface';
import { LLMMessage, LLMResponse, LLMGenerateOptions, ToolDefinition } from '@/types/llm';

/**
 * Google Gemini API适配器
 * 支持Gemini系列模型
 */
export class GeminiAdapter extends LLMInterface {
  private readonly defaultBaseUrl = 'https://generativelanguage.googleapis.com/v1beta';

  constructor(apiKey: string, model: string, baseUrl?: string, timeout: number = 30000) {
    super(apiKey, model, baseUrl || 'https://generativelanguage.googleapis.com/v1beta', timeout);
  }

  async generateText(messages: LLMMessage[], options?: LLMGenerateOptions): Promise<LLMResponse> {
    try {
      const requestBody = this.buildRequestBody(messages, options);
      const response = await this.makeRequest('/generateContent', requestBody);
      
      return this.parseResponse(response);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async generateWithTools(
    messages: LLMMessage[], 
    tools: ToolDefinition[], 
    options?: LLMGenerateOptions
  ): Promise<LLMResponse> {
    try {
      const requestBody = this.buildRequestBody(messages, options, tools);
      const response = await this.makeRequest('/generateContent', requestBody);
      
      return this.parseResponse(response);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async generateTextStream(
    messages: LLMMessage[],
    onChunk: (chunk: string) => void,
    options?: LLMGenerateOptions
  ): Promise<void> {
    try {
      const requestBody = this.buildRequestBody(messages, options);
      await this.makeStreamRequest('/streamGenerateContent', requestBody, onChunk);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async validateConnection(): Promise<boolean> {
    try {
      await this.makeRequest('/models');
      return true;
    } catch (error) {
      console.error('Gemini connection validation failed:', error);
      return false;
    }
  }

  async getModelInfo(): Promise<ModelInfo> {
    const modelMap: Record<string, Partial<ModelInfo>> = {
      'gemini-pro': {
        maxTokens: 32768,
        supportsFunctionCalling: true,
        costPer1kTokens: { input: 0.0005, output: 0.0015 }
      },
      'gemini-pro-vision': {
        maxTokens: 16384,
        supportsFunctionCalling: false,
        costPer1kTokens: { input: 0.00025, output: 0.0005 }
      },
      'gemini-1.5-pro': {
        maxTokens: 1048576,
        supportsFunctionCalling: true,
        costPer1kTokens: { input: 0.0035, output: 0.0105 }
      }
    };

    const modelInfo = modelMap[this.model] || {};
    
    return {
      name: this.model,
      provider: 'Google Gemini',
      maxTokens: modelInfo.maxTokens || 32768,
      supportsFunctionCalling: modelInfo.supportsFunctionCalling || false,
      supportsStreaming: true,
      costPer1kTokens: modelInfo.costPer1kTokens
    };
  }

  estimateTokens(text: string): number {
    // Gemini的token计算与OpenAI类似，但稍有不同
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const otherChars = text.length - chineseChars;
    return Math.ceil(chineseChars / 1.3 + otherChars / 3.5);
  }

  async getSupportedModels(): Promise<string[]> {
    try {
      const response = await this.makeRequest('/models');
      return response.models
        .filter((model: any) => model.name.includes('gemini'))
        .map((model: any) => model.name.split('/').pop())
        .sort();
    } catch (error) {
      // 如果无法获取模型列表，返回默认支持的模型
      return ['gemini-pro', 'gemini-pro-vision', 'gemini-1.5-pro'];
    }
  }

  private buildRequestBody(
    messages: LLMMessage[], 
    options?: LLMGenerateOptions,
    tools?: ToolDefinition[]
  ): any {
    const contents = this.convertMessages(messages);
    
    const body: any = {
      contents,
      generationConfig: {
        maxOutputTokens: options?.maxTokens || 2000,
        temperature: options?.temperature || 0.7,
      }
    };

    if (options?.stopSequences?.length) {
      body.generationConfig.stopSequences = options.stopSequences;
    }

    if (tools?.length) {
      body.tools = [{
        functionDeclarations: tools.map(tool => ({
          name: tool.function.name,
          description: tool.function.description,
          parameters: tool.function.parameters
        }))
      }];
    }

    return body;
  }

  private convertMessages(messages: LLMMessage[]): any[] {
    const contents: any[] = [];
    let currentRole = '';
    let currentParts: any[] = [];

    for (const msg of messages) {
      // Gemini API要求相邻的消息不能有相同的role
      if (msg.role === 'system') {
        // 系统消息转换为用户消息
        if (currentRole === 'user') {
          currentParts.push({ text: msg.content });
        } else {
          if (currentParts.length > 0) {
            contents.push({ role: currentRole, parts: currentParts });
          }
          currentRole = 'user';
          currentParts = [{ text: msg.content }];
        }
      } else if (msg.role === 'user') {
        if (currentRole === 'user') {
          currentParts.push({ text: msg.content });
        } else {
          if (currentParts.length > 0) {
            contents.push({ role: currentRole, parts: currentParts });
          }
          currentRole = 'user';
          currentParts = [{ text: msg.content }];
        }
      } else if (msg.role === 'assistant') {
        if (currentRole === 'model') {
          currentParts.push({ text: msg.content });
        } else {
          if (currentParts.length > 0) {
            contents.push({ role: currentRole, parts: currentParts });
          }
          currentRole = 'model';
          currentParts = [{ text: msg.content }];
        }

        // 处理工具调用
        if (msg.tool_calls) {
          for (const toolCall of msg.tool_calls) {
            currentParts.push({
              functionCall: {
                name: toolCall.function.name,
                args: JSON.parse(toolCall.function.arguments)
              }
            });
          }
        }
      } else if (msg.role === 'tool') {
        // 工具响应
        currentParts.push({
          functionResponse: {
            name: msg.name,
            response: { result: msg.content }
          }
        });
      }
    }

    if (currentParts.length > 0) {
      contents.push({ role: currentRole, parts: currentParts });
    }

    return contents;
  }

  private async makeRequest(endpoint: string, body?: any): Promise<any> {
    const url = `${this.baseUrl}/models/${this.model}:${endpoint.replace('/', '')}?key=${this.apiKey}`;
    const headers = {
      'Content-Type': 'application/json',
    };

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(url, {
        method: body ? 'POST' : 'GET',
        headers,
        body: body ? JSON.stringify(body) : undefined,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`HTTP ${response.status}: ${errorData.error?.message || response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  private async makeStreamRequest(
    endpoint: string, 
    body: any, 
    onChunk: (chunk: string) => void
  ): Promise<void> {
    const url = `${this.baseUrl}/models/${this.model}:${endpoint.replace('/', '')}?key=${this.apiKey}&alt=sse`;
    const headers = {
      'Content-Type': 'application/json',
    };

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`HTTP ${response.status}: ${errorData.error?.message || response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Failed to get response reader');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') return;

            try {
              const parsed = JSON.parse(data);
              const content = parsed.candidates?.[0]?.content?.parts?.[0]?.text;
              if (content) {
                onChunk(content);
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  private parseResponse(response: any): LLMResponse {
    const candidate = response.candidates?.[0];
    if (!candidate) {
      throw new Error('No candidates in response');
    }

    const content = candidate.content?.parts?.[0]?.text || '';
    
    const result: LLMResponse = {
      content,
      usage: response.usageMetadata ? {
        prompt_tokens: response.usageMetadata.promptTokenCount || 0,
        completion_tokens: response.usageMetadata.candidatesTokenCount || 0,
        total_tokens: response.usageMetadata.totalTokenCount || 0
      } : undefined
    };

    // 处理工具调用
    const functionCalls = candidate.content?.parts?.filter((part: any) => part.functionCall);
    if (functionCalls?.length) {
      result.tool_calls = functionCalls.map((part: any, index: number) => ({
        id: `call_${index}`,
        type: 'function' as const,
        function: {
          name: part.functionCall.name,
          arguments: JSON.stringify(part.functionCall.args)
        }
      }));
    }

    return result;
  }

  private handleError(error: any): LLMError {
    if (error.name === 'AbortError') {
      return new LLMError('Request timeout', LLMErrorCodes.TIMEOUT);
    }

    if (error.message?.includes('401') || error.message?.includes('403')) {
      return new LLMError('Invalid API key', LLMErrorCodes.INVALID_API_KEY, 401);
    }

    if (error.message?.includes('429')) {
      return new LLMError('Rate limit exceeded', LLMErrorCodes.RATE_LIMIT_EXCEEDED, 429);
    }

    if (error.message?.includes('404')) {
      return new LLMError('Model not found', LLMErrorCodes.MODEL_NOT_FOUND, 404);
    }

    if (error.message?.includes('400')) {
      return new LLMError('Invalid request', LLMErrorCodes.INVALID_REQUEST, 400);
    }

    if (error.message?.includes('500') || error.message?.includes('502') || error.message?.includes('503')) {
      return new LLMError('Server error', LLMErrorCodes.SERVER_ERROR, 500);
    }

    return new LLMError(
      error.message || 'Unknown error',
      LLMErrorCodes.UNKNOWN_ERROR,
      undefined,
      error
    );
  }
}
