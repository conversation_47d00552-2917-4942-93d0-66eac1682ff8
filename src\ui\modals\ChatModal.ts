import { App, Modal, Setting, TextAreaComponent, ButtonComponent } from 'obsidian';
import { OrchestrationEngine } from '@/core/orchestration/OrchestrationEngine';

/**
 * AI助手聊天模态框
 */
export class ChatModal extends Modal {
  private orchestrationEngine: OrchestrationEngine;
  private inputArea: TextAreaComponent;
  private outputArea: HTMLElement;
  private sendButton: ButtonComponent;
  private isProcessing: boolean = false;

  constructor(app: App, orchestrationEngine: OrchestrationEngine) {
    super(app);
    this.orchestrationEngine = orchestrationEngine;
  }

  onOpen() {
    const { contentEl } = this;
    contentEl.empty();

    // 设置模态框标题
    contentEl.createEl('h2', { text: 'AI Coach Advanced' });

    // 创建对话显示区域
    this.createChatArea(contentEl);

    // 创建输入区域
    this.createInputArea(contentEl);

    // 创建控制按钮
    this.createControlButtons(contentEl);

    // 设置焦点到输入框
    setTimeout(() => {
      this.inputArea.inputEl.focus();
    }, 100);
  }

  onClose() {
    const { contentEl } = this;
    contentEl.empty();
  }

  /**
   * 创建聊天显示区域
   */
  private createChatArea(containerEl: HTMLElement): void {
    const chatContainer = containerEl.createDiv({ cls: 'ai-coach-chat-container' });

    this.outputArea = chatContainer.createDiv({
      cls: 'ai-coach-chat-output',
      attr: { style: 'height: 400px; overflow-y: auto; border: 1px solid var(--background-modifier-border); padding: 10px; margin-bottom: 10px; background: var(--background-primary);' }
    });

    // 显示欢迎消息
    this.addMessage('assistant', '你好！我是AI Coach Advanced，你的智能助手。有什么可以帮助你的吗？');
  }

  /**
   * 创建输入区域
   */
  private createInputArea(containerEl: HTMLElement): void {
    const inputContainer = containerEl.createDiv({ cls: 'ai-coach-input-container' });

    new Setting(inputContainer)
      .setName('输入消息')
      .setDesc('输入你的问题或指令')
      .addTextArea(text => {
        this.inputArea = text;
        text.inputEl.style.width = '100%';
        text.inputEl.style.minHeight = '80px';
        text.inputEl.placeholder = '请输入你的问题...';

        // 添加键盘事件
        text.inputEl.addEventListener('keydown', (e) => {
          if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
            e.preventDefault();
            this.sendMessage();
          }
        });
      });
  }

  /**
   * 创建控制按钮
   */
  private createControlButtons(containerEl: HTMLElement): void {
    const buttonContainer = containerEl.createDiv({
      cls: 'ai-coach-button-container',
      attr: { style: 'display: flex; gap: 10px; justify-content: flex-end; margin-top: 10px;' }
    });

    // 发送按钮
    new Setting(buttonContainer)
      .addButton(button => {
        this.sendButton = button;
        button
          .setButtonText('发送 (Ctrl+Enter)')
          .setCta()
          .onClick(() => this.sendMessage());
      });

    // 清空对话按钮
    new Setting(buttonContainer)
      .addButton(button => {
        button
          .setButtonText('清空对话')
          .onClick(() => this.clearConversation());
      });

    // 新对话按钮
    new Setting(buttonContainer)
      .addButton(button => {
        button
          .setButtonText('新对话')
          .onClick(() => this.startNewConversation());
      });
  }

  /**
   * 发送消息
   */
  private async sendMessage(): Promise<void> {
    const message = this.inputArea.getValue().trim();
    if (!message || this.isProcessing) {
      return;
    }

    this.isProcessing = true;
    this.updateSendButton();

    try {
      // 显示用户消息
      this.addMessage('user', message);

      // 清空输入框
      this.inputArea.setValue('');

      // 显示处理中状态
      const processingMessageEl = this.addMessage('assistant', '正在思考中...');

      // 处理用户输入
      const result = await this.orchestrationEngine.processUserInput(message);

      // 移除处理中消息
      if (processingMessageEl) {
        processingMessageEl.remove();
      }

      // 显示AI回复
      if (result.success) {
        this.addMessage('assistant', result.response);

        // 如果有执行日志，显示详细信息
        if (result.executionLog && result.executionLog.length > 0) {
          this.addExecutionDetails(result);
        }
      } else {
        this.addMessage('assistant', `抱歉，处理您的请求时出现了问题：${result.error || '未知错误'}`);
      }

    } catch (error) {
      console.error('Error processing message:', error);
      this.addMessage('assistant', `处理消息时发生错误：${error.message}`);
    } finally {
      this.isProcessing = false;
      this.updateSendButton();
      this.inputArea.inputEl.focus();
    }
  }

  /**
   * 添加消息到对话区域
   */
  private addMessage(role: 'user' | 'assistant', content: string): HTMLElement {
    const messageEl = this.outputArea.createDiv({
      cls: `ai-coach-message ai-coach-message-${role}`,
      attr: {
        style: `margin-bottom: 15px; padding: 10px; border-radius: 8px; ${
          role === 'user'
            ? 'background: var(--interactive-accent); color: var(--text-on-accent); margin-left: 20%; text-align: right;'
            : 'background: var(--background-secondary); margin-right: 20%;'
        }`
      }
    });

    // 添加角色标识
    const roleEl = messageEl.createDiv({
      cls: 'ai-coach-message-role',
      text: role === 'user' ? '你' : 'AI助手',
      attr: { style: 'font-weight: bold; margin-bottom: 5px; font-size: 0.9em; opacity: 0.8;' }
    });

    // 添加消息内容
    const contentEl = messageEl.createDiv({
      cls: 'ai-coach-message-content',
      attr: { style: 'white-space: pre-wrap; line-height: 1.5;' }
    });
    contentEl.textContent = content;

    // 添加时间戳
    const timestamp = new Date();
    const timeEl = messageEl.createDiv({
      cls: 'ai-coach-message-time',
      text: timestamp.toLocaleTimeString(),
      attr: { style: 'font-size: 0.8em; opacity: 0.6; margin-top: 5px;' }
    });

    // 滚动到底部
    this.outputArea.scrollTop = this.outputArea.scrollHeight;

    return messageEl;
  }

  /**
   * 添加执行详情
   */
  private addExecutionDetails(result: any): void {
    if (!result.executionLog || result.executionLog.length === 0) {
      return;
    }

    const detailsEl = this.outputArea.createDiv({
      cls: 'ai-coach-execution-details',
      attr: { style: 'margin-bottom: 15px; padding: 10px; background: var(--background-modifier-border); border-radius: 8px; font-size: 0.9em;' }
    });

    detailsEl.createEl('div', {
      text: '执行详情：',
      attr: { style: 'font-weight: bold; margin-bottom: 5px;' }
    });

    // 显示使用的工具
    if (result.toolsUsed && result.toolsUsed.length > 0) {
      detailsEl.createEl('div', {
        text: `使用工具：${result.toolsUsed.join(', ')}`,
        attr: { style: 'margin-bottom: 3px;' }
      });
    }

    // 显示处理时间
    if (result.processingTime) {
      detailsEl.createEl('div', {
        text: `处理时间：${result.processingTime}ms`,
        attr: { style: 'margin-bottom: 3px;' }
      });
    }

    // 显示复杂度
    if (result.metadata?.complexity) {
      detailsEl.createEl('div', {
        text: `任务复杂度：${result.metadata.complexity}`,
        attr: { style: 'margin-bottom: 3px;' }
      });
    }
  }

  /**
   * 更新发送按钮状态
   */
  private updateSendButton(): void {
    if (this.sendButton) {
      this.sendButton.setButtonText(
        this.isProcessing ? '处理中...' : '发送 (Ctrl+Enter)'
      );
      this.sendButton.setDisabled(this.isProcessing);
    }
  }

  /**
   * 清空对话
   */
  private clearConversation(): void {
    this.outputArea.empty();
    this.addMessage('assistant', '对话已清空。有什么新的问题吗？');
  }

  /**
   * 开始新对话
   */
  private async startNewConversation(): Promise<void> {
    try {
      await this.orchestrationEngine.startNewConversation();
      this.clearConversation();
      this.addMessage('assistant', '已开始新对话。有什么可以帮助你的吗？');
    } catch (error) {
      console.error('Failed to start new conversation:', error);
      this.addMessage('assistant', `开始新对话失败：${error.message}`);
    }
  }
}
