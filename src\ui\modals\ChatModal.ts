import { App, Modal } from 'obsidian';
import { LLMOrchestrator } from '@/core/orchestration/LLMOrchestrator';

export class ChatModal extends Modal {
  private orchestrator: LLMOrchestrator;

  constructor(app: App, orchestrator: LLMOrchestrator) {
    super(app);
    this.orchestrator = orchestrator;
  }

  onOpen() {
    const { contentEl } = this;
    contentEl.empty();
    
    contentEl.createEl('h2', { text: 'AI Coach 助手' });
    
    // 这里将在后续任务中实现完整的聊天界面
    const placeholder = contentEl.createEl('div', {
      text: '聊天界面开发中...',
      cls: 'ai-coach-placeholder'
    });
    
    placeholder.style.padding = '20px';
    placeholder.style.textAlign = 'center';
    placeholder.style.color = 'var(--text-muted)';
  }

  onClose() {
    const { contentEl } = this;
    contentEl.empty();
  }
}
