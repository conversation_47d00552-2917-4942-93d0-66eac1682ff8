/**
 * 简单的工具调用测试
 * 测试TaskPlanner的工具选择逻辑
 */

async function testTaskPlannerLogic() {
  console.log('🧪 开始测试TaskPlanner工具调用逻辑...\n');
  
  try {
    // 模拟LLM响应
    const mockLLM = {
      async generateText(messages) {
        const userMessage = messages[messages.length - 1].content;
        console.log('🤖 LLM收到消息:', userMessage.substring(0, 100) + '...');
        
        // 根据不同的提示返回不同的响应
        if (userMessage.includes('分析以下用户输入')) {
          // 任务分析响应
          return {
            content: `
**任务类型**：查询信息

**主要目标**：用户想要搜索关于项目管理的笔记内容

**所需信息**：需要在Vault中查找项目管理相关的笔记和文档

**建议工具**：vault_query - 用于搜索Vault中的笔记内容

**执行步骤**：
1. 使用vault_query工具搜索"项目管理"关键词
2. 分析搜索结果
3. 为用户提供相关信息总结
            `
          };
        } else if (userMessage.includes('基于当前情况，思考下一步')) {
          // 思考响应
          return {
            content: `
用户想要搜索关于项目管理的笔记。我需要使用vault_query工具来搜索用户的Obsidian Vault中的相关内容。

这是一个明确的信息查询任务，应该调用vault_query工具来获取准确的信息，而不是仅凭记忆回答。
            `
          };
        } else if (userMessage.includes('请以JSON格式返回行动计划')) {
          // 行动计划响应
          return {
            content: JSON.stringify({
              type: 'tool_call',
              description: '搜索Vault中关于项目管理的笔记',
              toolName: 'vault_query',
              parameters: {
                query: '项目管理',
                limit: 5
              }
            })
          };
        } else {
          // 默认响应
          return {
            content: '我理解了您的请求，让我来帮助您。'
          };
        }
      }
    };
    
    // 模拟工具注册表
    const mockToolRegistry = {
      tools: [
        {
          name: 'vault_query',
          description: '搜索和查询Obsidian Vault中的笔记内容',
          parameters: {
            query: { type: 'string', required: true, description: '搜索关键词' },
            limit: { type: 'number', required: false, description: '返回结果数量限制' }
          }
        },
        {
          name: 'web_search',
          description: '在网络上搜索最新信息',
          parameters: {
            query: { type: 'string', required: true, description: '搜索关键词' }
          }
        },
        {
          name: 'javascript_executor',
          description: '执行JavaScript代码进行计算和数据处理',
          parameters: {
            code: { type: 'string', required: true, description: 'JavaScript代码' }
          }
        }
      ],
      
      list() {
        return this.tools;
      },
      
      async execute(toolName, parameters) {
        console.log(`🔧 执行工具: ${toolName}`, parameters);
        
        if (toolName === 'vault_query') {
          return {
            success: true,
            data: [
              {
                file: '项目管理.md',
                content: '# 项目管理\n\n## 敏捷开发\n敏捷开发是一种迭代的软件开发方法...',
                score: 0.95
              },
              {
                file: '工作流程.md',
                content: '# 工作流程\n\n项目管理的核心是建立有效的工作流程...',
                score: 0.87
              }
            ]
          };
        } else if (toolName === 'javascript_executor') {
          return {
            success: true,
            data: {
              result: 338350,
              output: '1到100的平方和是: 338350'
            }
          };
        } else {
          return {
            success: false,
            error: `未知工具: ${toolName}`
          };
        }
      }
    };
    
    // 模拟提示管理器
    const mockPromptManager = {
      generateTaskAnalysisPrompt(userInput, context) {
        return `请分析以下用户输入，确定需要执行的任务：

用户输入：${userInput}

请按以下格式分析：

**任务类型**：[查询信息/创建内容/执行操作/其他]
**主要目标**：[简要描述用户想要达成的目标]
**所需信息**：[列出完成任务需要的信息]
**建议工具**：[根据任务需求推荐使用的工具]
**执行步骤**：
1. [第一步]
2. [第二步]
3. [后续步骤...]`;
      },
      
      generateSystemPrompt() {
        return `你是一个智能的Obsidian助手，名为AI Coach Advanced。你的主要职责是：

1. **理解用户意图**：准确理解用户的自然语言指令，识别用户想要完成的任务。
2. **积极使用工具**：当用户的请求需要获取信息、执行操作或处理数据时，必须主动调用相应的工具。
3. **知识库查询**：当需要查找信息时，优先搜索用户的Obsidian Vault笔记内容。

**重要：工具调用指导**
- 当用户询问笔记内容、查找信息时 → 使用 vault_query 工具
- 当需要最新信息、网络搜索时 → 使用 web_search 工具  
- 当需要计算、数据处理、代码执行时 → 使用 javascript_executor 工具
- 不要仅凭记忆回答，要通过工具获取准确信息`;
      },
      
      generateContentSummaryPrompt(context, results) {
        return `基于以下信息为用户提供回答：

用户问题：${context}

查询结果：
${results.map(r => `**来源**：${r.source}\n**内容**：${r.content}`).join('\n\n')}

请基于这些信息提供一个准确、有用的回答。`;
      }
    };
    
    // 创建简化的TaskPlanner
    class SimpleTaskPlanner {
      constructor(llm, promptManager, toolRegistry) {
        this.llm = llm;
        this.promptManager = promptManager;
        this.toolRegistry = toolRegistry;
        this.maxIterations = 3; // 减少迭代次数用于测试
      }
      
      async planAndExecute(userInput, context) {
        const startTime = Date.now();
        const executionLog = [];
        
        console.log(`📝 开始处理用户输入: "${userInput}"`);
        
        // 1. 任务分析
        const taskAnalysis = await this.analyzeTask(userInput, context);
        executionLog.push({
          type: 'analysis',
          content: taskAnalysis.analysis,
          timestamp: new Date()
        });
        
        console.log('✅ 任务分析完成:', taskAnalysis.executable ? '可执行' : '不可执行');
        
        if (!taskAnalysis.executable) {
          return {
            success: false,
            result: taskAnalysis.analysis,
            executionLog,
            executionTime: Date.now() - startTime
          };
        }
        
        // 2. ReAct循环
        const reactResult = await this.executeReActLoop(userInput, taskAnalysis, executionLog);
        
        return {
          success: reactResult.success,
          result: reactResult.finalAnswer,
          executionLog,
          executionTime: Date.now() - startTime,
          toolsUsed: reactResult.toolsUsed,
          iterations: reactResult.iterations
        };
      }
      
      async analyzeTask(userInput, context) {
        const analysisPrompt = this.promptManager.generateTaskAnalysisPrompt(userInput, context?.conversationHistory);
        const messages = [
          { role: 'system', content: analysisPrompt },
          { role: 'user', content: userInput }
        ];
        
        const response = await this.llm.generateText(messages);
        return this.parseTaskAnalysis(response.content);
      }
      
      parseTaskAnalysis(content) {
        const lowerContent = content.toLowerCase();
        const hasToolKeywords = ['搜索', '查找', '查询', '计算', '执行'].some(word => lowerContent.includes(word));
        const hasRefusal = ['无法', '不能', '无效'].some(word => lowerContent.includes(word));
        
        return {
          analysis: content,
          executable: !hasRefusal || hasToolKeywords,
          complexity: 'medium',
          requiredTools: []
        };
      }
      
      async executeReActLoop(userInput, taskAnalysis, executionLog) {
        const availableTools = this.toolRegistry.list();
        const toolsUsed = [];
        let currentContext = userInput;
        let finalAnswer = '';
        let iterations = 0;
        
        for (let i = 0; i < this.maxIterations; i++) {
          iterations++;
          console.log(`\n🔄 ReAct循环 - 第${iterations}轮:`);
          
          // Thought: 思考
          const thought = await this.generateThought(currentContext, availableTools, executionLog);
          executionLog.push({
            type: 'thought',
            content: thought,
            timestamp: new Date()
          });
          console.log('💭 思考:', thought.substring(0, 100) + '...');
          
          // Action: 行动
          const action = await this.generateAction(thought, availableTools);
          executionLog.push({
            type: 'action',
            content: `计划执行: ${action.type} - ${action.description}`,
            timestamp: new Date()
          });
          console.log('🎯 行动计划:', action.type, '-', action.description);
          
          // 执行行动
          if (action.type === 'tool_call') {
            console.log(`🔧 调用工具: ${action.toolName}`);
            const toolResult = await this.executeTool(action.toolName, action.parameters);
            executionLog.push({
              type: 'tool_result',
              content: `工具 ${action.toolName} 执行结果: ${JSON.stringify(toolResult.data)}`,
              timestamp: new Date(),
              toolName: action.toolName,
              toolResult
            });
            
            if (!toolsUsed.includes(action.toolName)) {
              toolsUsed.push(action.toolName);
            }
            
            currentContext += `\n\n工具执行结果:\n${JSON.stringify(toolResult.data)}`;
            
          } else if (action.type === 'final_answer') {
            finalAnswer = action.answer;
            executionLog.push({
              type: 'final_answer',
              content: finalAnswer,
              timestamp: new Date()
            });
            console.log('✅ 生成最终答案');
            break;
          }
        }
        
        if (!finalAnswer) {
          finalAnswer = await this.generateFinalAnswer(currentContext, executionLog);
        }
        
        return {
          success: !!finalAnswer,
          finalAnswer,
          toolsUsed,
          iterations
        };
      }
      
      async generateThought(context, availableTools, executionLog) {
        const systemPrompt = this.promptManager.generateSystemPrompt();
        const toolDescriptions = availableTools.map(tool => `${tool.name}: ${tool.description}`).join('\n');
        const recentSteps = executionLog.slice(-3).map(step => `${step.type}: ${step.content}`).join('\n');
        
        const prompt = `基于当前情况，思考下一步应该采取什么行动：

当前上下文：${context}

可用工具：${toolDescriptions}

最近的执行步骤：${recentSteps}

请分析当前情况并思考下一步的最佳行动方案。`;
        
        const messages = [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: prompt }
        ];
        
        const response = await this.llm.generateText(messages);
        return response.content;
      }
      
      async generateAction(thought, availableTools) {
        const toolDescriptions = availableTools.map(tool => ({
          name: tool.name,
          description: tool.description,
          parameters: tool.parameters
        }));
        
        const prompt = `基于以下思考内容，决定下一步的具体行动：

思考内容：${thought}

可用工具：${JSON.stringify(toolDescriptions, null, 2)}

**重要指导原则**：
- 如果用户的请求需要搜索信息、执行代码、查询笔记、网络搜索等操作，必须选择 tool_call
- 只有当你已经有足够信息可以直接回答用户问题时，才选择 final_answer

请以JSON格式返回行动计划：
{
  "type": "tool_call|final_answer|need_more_info",
  "description": "行动描述",
  "toolName": "工具名称（仅tool_call时需要）",
  "parameters": {"参数名": "参数值"},
  "answer": "最终答案（仅final_answer时需要）"
}`;
        
        const messages = [{ role: 'user', content: prompt }];
        const response = await this.llm.generateText(messages);
        
        try {
          return JSON.parse(response.content);
        } catch (error) {
          console.warn('⚠️ JSON解析失败，使用默认行动');
          return {
            type: 'final_answer',
            description: '解析行动计划失败，提供基于思考的答案',
            answer: thought
          };
        }
      }
      
      async executeTool(toolName, parameters) {
        return await this.toolRegistry.execute(toolName, parameters);
      }
      
      async generateFinalAnswer(context, executionLog) {
        const prompt = this.promptManager.generateContentSummaryPrompt(
          context,
          executionLog.map(step => ({
            source: step.type,
            content: step.content,
            score: 1.0
          }))
        );
        
        const messages = [{ role: 'user', content: prompt }];
        const response = await this.llm.generateText(messages);
        return response.content;
      }
    }
    
    // 创建TaskPlanner实例并测试
    const taskPlanner = new SimpleTaskPlanner(mockLLM, mockPromptManager, mockToolRegistry);
    
    // 测试用例1: 搜索笔记（应该调用工具）
    console.log('🔧 测试1: 搜索笔记任务...');
    const result1 = await taskPlanner.planAndExecute('帮我搜索关于项目管理的笔记');
    
    console.log('\n📊 测试1结果:');
    console.log('- 成功:', result1.success);
    console.log('- 使用的工具:', result1.toolsUsed || []);
    console.log('- 迭代次数:', result1.iterations);
    console.log('- 执行日志步骤:', result1.executionLog.map(step => step.type));
    
    // 测试用例2: 计算任务（应该调用工具）
    console.log('\n🔧 测试2: 计算任务...');
    const result2 = await taskPlanner.planAndExecute('帮我计算1到100的平方和');
    
    console.log('\n📊 测试2结果:');
    console.log('- 成功:', result2.success);
    console.log('- 使用的工具:', result2.toolsUsed || []);
    console.log('- 迭代次数:', result2.iterations);
    console.log('- 执行日志步骤:', result2.executionLog.map(step => step.type));
    
    // 分析结果
    console.log('\n📈 测试分析:');
    const hasToolCalls = result1.toolsUsed?.length > 0 || result2.toolsUsed?.length > 0;
    
    if (hasToolCalls) {
      console.log('✅ 工具调用功能正常 - AI助手能够正确识别并调用工具');
    } else {
      console.log('❌ 工具调用功能异常 - AI助手没有调用任何工具');
      console.log('可能的原因:');
      console.log('1. LLM提示词不够明确');
      console.log('2. 行动计划解析失败');
      console.log('3. 任务分析逻辑有问题');
    }
    
    console.log('\n🎉 TaskPlanner工具调用逻辑测试完成！');
    return hasToolCalls;
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    console.error('错误堆栈:', error.stack);
    return false;
  }
}

// 运行测试
if (require.main === module) {
  testTaskPlannerLogic().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { testTaskPlannerLogic };
