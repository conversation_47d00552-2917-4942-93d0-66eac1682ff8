import { WebSearchResult } from '@/types/tools';

/**
 * 网络内容处理器
 * 负责搜索结果的清洗、摘要和格式化
 */
export class WebContentProcessor {
  private maxSnippetLength: number = 300;
  private maxTitleLength: number = 100;

  /**
   * 处理搜索结果
   */
  processSearchResults(results: WebSearchResult[]): ProcessedSearchResult[] {
    return results.map(result => this.processSearchResult(result));
  }

  /**
   * 处理单个搜索结果
   */
  private processSearchResult(result: WebSearchResult): ProcessedSearchResult {
    return {
      ...result,
      title: this.cleanTitle(result.title),
      snippet: this.cleanSnippet(result.snippet),
      url: this.normalizeUrl(result.url),
      domain: this.extractDomain(result.url),
      relevanceScore: this.calculateRelevanceScore(result),
      summary: this.generateSummary(result),
      keywords: this.extractKeywords(result.snippet)
    };
  }

  /**
   * 清理标题
   */
  private cleanTitle(title: string): string {
    if (!title) return '';
    
    // 移除HTML标签
    let cleaned = title.replace(/<[^>]*>/g, '');
    
    // 解码HTML实体
    cleaned = this.decodeHtmlEntities(cleaned);
    
    // 截断过长的标题
    if (cleaned.length > this.maxTitleLength) {
      cleaned = cleaned.substring(0, this.maxTitleLength - 3) + '...';
    }
    
    return cleaned.trim();
  }

  /**
   * 清理摘要
   */
  private cleanSnippet(snippet: string): string {
    if (!snippet) return '';
    
    // 移除HTML标签
    let cleaned = snippet.replace(/<[^>]*>/g, '');
    
    // 解码HTML实体
    cleaned = this.decodeHtmlEntities(cleaned);
    
    // 移除多余的空白字符
    cleaned = cleaned.replace(/\s+/g, ' ');
    
    // 截断过长的摘要
    if (cleaned.length > this.maxSnippetLength) {
      // 尝试在句子边界截断
      const sentences = cleaned.split(/[.!?]+/);
      let truncated = '';
      
      for (const sentence of sentences) {
        if ((truncated + sentence).length <= this.maxSnippetLength - 3) {
          truncated += sentence + '. ';
        } else {
          break;
        }
      }
      
      if (truncated.length < this.maxSnippetLength / 2) {
        // 如果句子截断太短，直接截断
        cleaned = cleaned.substring(0, this.maxSnippetLength - 3) + '...';
      } else {
        cleaned = truncated.trim();
      }
    }
    
    return cleaned.trim();
  }

  /**
   * 标准化URL
   */
  private normalizeUrl(url: string): string {
    if (!url) return '';
    
    try {
      const urlObj = new URL(url);
      return urlObj.href;
    } catch (error) {
      return url;
    }
  }

  /**
   * 提取域名
   */
  private extractDomain(url: string): string {
    if (!url) return '';
    
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      return '';
    }
  }

  /**
   * 计算相关性分数
   */
  private calculateRelevanceScore(result: WebSearchResult): number {
    let score = result.score || 0;
    
    // 基于域名权威性调整分数
    const domain = this.extractDomain(result.url);
    const authorityBonus = this.getDomainAuthorityBonus(domain);
    score += authorityBonus;
    
    // 基于内容质量调整分数
    const qualityBonus = this.getContentQualityBonus(result);
    score += qualityBonus;
    
    return Math.min(1.0, Math.max(0.0, score));
  }

  /**
   * 获取域名权威性加分
   */
  private getDomainAuthorityBonus(domain: string): number {
    const authorityDomains = [
      'wikipedia.org', 'github.com', 'stackoverflow.com',
      'medium.com', 'arxiv.org', 'nature.com', 'science.org',
      'ieee.org', 'acm.org', 'springer.com'
    ];
    
    if (authorityDomains.some(authDomain => domain.includes(authDomain))) {
      return 0.1;
    }
    
    return 0;
  }

  /**
   * 获取内容质量加分
   */
  private getContentQualityBonus(result: WebSearchResult): number {
    let bonus = 0;
    
    // 标题长度适中
    if (result.title && result.title.length >= 20 && result.title.length <= 80) {
      bonus += 0.05;
    }
    
    // 摘要长度适中
    if (result.snippet && result.snippet.length >= 50 && result.snippet.length <= 200) {
      bonus += 0.05;
    }
    
    // 包含数字或日期（可能是更新的内容）
    if (/\d{4}|\d+%|\d+\.\d+/.test(result.snippet)) {
      bonus += 0.03;
    }
    
    return bonus;
  }

  /**
   * 生成摘要
   */
  private generateSummary(result: WebSearchResult): string {
    const title = result.title || '';
    const snippet = result.snippet || '';
    
    if (!snippet) return title;
    
    // 提取关键句子
    const sentences = snippet.split(/[.!?]+/).filter(s => s.trim().length > 10);
    
    if (sentences.length === 0) return snippet;
    
    // 选择最有信息量的句子
    const bestSentence = sentences.reduce((best, current) => {
      const currentScore = this.getSentenceScore(current);
      const bestScore = this.getSentenceScore(best);
      return currentScore > bestScore ? current : best;
    });
    
    return bestSentence.trim() + '.';
  }

  /**
   * 计算句子分数
   */
  private getSentenceScore(sentence: string): number {
    let score = 0;
    
    // 长度适中的句子得分更高
    const length = sentence.length;
    if (length >= 20 && length <= 100) {
      score += 1;
    }
    
    // 包含关键词的句子得分更高
    const keywords = ['是', '有', '可以', '能够', '包括', '提供', '支持', '实现'];
    const keywordCount = keywords.filter(keyword => sentence.includes(keyword)).length;
    score += keywordCount * 0.5;
    
    // 包含数字的句子得分更高
    if (/\d+/.test(sentence)) {
      score += 0.5;
    }
    
    return score;
  }

  /**
   * 提取关键词
   */
  private extractKeywords(text: string): string[] {
    if (!text) return [];
    
    // 简单的关键词提取
    const words = text
      .toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2);
    
    // 统计词频
    const wordCount: Record<string, number> = {};
    words.forEach(word => {
      wordCount[word] = (wordCount[word] || 0) + 1;
    });
    
    // 过滤停用词
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'
    ]);
    
    const keywords = Object.entries(wordCount)
      .filter(([word]) => !stopWords.has(word))
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word);
    
    return keywords;
  }

  /**
   * 解码HTML实体
   */
  private decodeHtmlEntities(text: string): string {
    const entities: Record<string, string> = {
      '&amp;': '&',
      '&lt;': '<',
      '&gt;': '>',
      '&quot;': '"',
      '&#39;': "'",
      '&nbsp;': ' ',
      '&hellip;': '...'
    };
    
    return text.replace(/&[a-zA-Z0-9#]+;/g, (entity) => {
      return entities[entity] || entity;
    });
  }

  /**
   * 过滤和排序结果
   */
  filterAndSortResults(
    results: ProcessedSearchResult[],
    options: FilterOptions = {}
  ): ProcessedSearchResult[] {
    let filtered = [...results];
    
    // 过滤低质量结果
    if (options.minRelevanceScore !== undefined) {
      filtered = filtered.filter(result => result.relevanceScore >= options.minRelevanceScore!);
    }
    
    // 过滤重复域名
    if (options.maxResultsPerDomain !== undefined) {
      const domainCounts: Record<string, number> = {};
      filtered = filtered.filter(result => {
        const domain = result.domain;
        domainCounts[domain] = (domainCounts[domain] || 0) + 1;
        return domainCounts[domain] <= options.maxResultsPerDomain!;
      });
    }
    
    // 排序
    const sortBy = options.sortBy || 'relevance';
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'relevance':
          return b.relevanceScore - a.relevanceScore;
        case 'title':
          return a.title.localeCompare(b.title);
        case 'domain':
          return a.domain.localeCompare(b.domain);
        default:
          return 0;
      }
    });
    
    return filtered;
  }

  /**
   * 生成搜索结果摘要
   */
  generateResultsSummary(results: ProcessedSearchResult[]): string {
    if (results.length === 0) {
      return '没有找到相关结果。';
    }
    
    const topResults = results.slice(0, 3);
    const summaries = topResults.map(result => result.summary).filter(s => s);
    
    if (summaries.length === 0) {
      return `找到 ${results.length} 个相关结果。`;
    }
    
    return `找到 ${results.length} 个相关结果。主要信息：${summaries.join(' ')}`;
  }
}

/**
 * 处理后的搜索结果
 */
export interface ProcessedSearchResult extends WebSearchResult {
  domain: string;
  relevanceScore: number;
  summary: string;
  keywords: string[];
}

/**
 * 过滤选项
 */
export interface FilterOptions {
  minRelevanceScore?: number;
  maxResultsPerDomain?: number;
  sortBy?: 'relevance' | 'title' | 'domain';
}
