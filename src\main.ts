import { Plugin, TFile, Notice } from 'obsidian';
import { PluginConfig, DEFAULT_CONFIG } from '@/types/config';
import { ConfigManager } from '@/core/config/ConfigManager';
import { OrchestrationEngine } from '@/core/orchestration/OrchestrationEngine';
import { ChatModal } from '@/ui/modals/ChatModal';
import { SettingsTab } from '@/ui/settings/SettingsTab';

// 导入所有工具
import { PluginManagerTool } from '@/tools/plugin/PluginManagerTool';
import { VaultQueryTool } from '@/tools/vault/VaultQueryTool';
import { WebSearchTool } from '@/tools/web/WebSearchTool';
import { JavaScriptExecutorTool } from '@/tools/javascript/JavaScriptExecutorTool';
import { ShortTermMemoryTool } from '@/tools/memory/ShortTermMemoryTool';
import { LongTermMemoryTool } from '@/tools/memory/LongTermMemoryTool';

export default class AICoachAdvancedPlugin extends Plugin {
  private config: PluginConfig = DEFAULT_CONFIG;
  private configManager: ConfigManager;
  private orchestrationEngine: OrchestrationEngine;
  private chatModal: ChatModal | null = null;
  private initialized: boolean = false;

  async onload() {
    console.log('Loading AI Coach Advanced Plugin...');

    try {
      // 初始化核心组件
      await this.initializeCore();
      
      // 注册命令
      this.registerCommands();
      
      // 注册设置页面
      this.addSettingTab(new SettingsTab(this.app, this));
      
      // 监听文件变化（用于Vault索引更新）
      this.registerVaultEvents();
      
      console.log('AI Coach Advanced Plugin loaded successfully');
      new Notice('AI Coach Advanced 插件已加载');
      
    } catch (error) {
      console.error('Failed to load AI Coach Advanced Plugin:', error);
      new Notice('AI Coach Advanced 插件加载失败: ' + error.message);
    }
  }

  async onunload() {
    console.log('Unloading AI Coach Advanced Plugin...');
    
    // 清理资源
    if (this.chatModal) {
      this.chatModal.close();
    }
    
    // 保存配置
    await this.configManager?.saveConfig();
    
    console.log('AI Coach Advanced Plugin unloaded');
  }

  private async initializeCore() {
    // 初始化配置管理器
    this.configManager = new ConfigManager(this.app, this);
    this.config = await this.configManager.loadConfig();

    // 初始化编排引擎
    this.orchestrationEngine = new OrchestrationEngine(this.app, this.configManager);

    // 注册所有工具
    await this.registerTools();

    // 初始化编排引擎
    await this.orchestrationEngine.initialize();

    this.initialized = true;
  }

  private async registerTools(): Promise<void> {
    try {
      // 插件管理工具
      const pluginTool = new PluginManagerTool(this.app);
      await this.orchestrationEngine.registerTool(pluginTool);

      // Vault查询工具
      const vaultTool = new VaultQueryTool(this.app);
      await this.orchestrationEngine.registerTool(vaultTool);

      // 网络搜索工具
      const webTool = new WebSearchTool();
      await this.orchestrationEngine.registerTool(webTool);

      // JavaScript执行工具
      const jsTool = new JavaScriptExecutorTool();
      await this.orchestrationEngine.registerTool(jsTool);

      // 记忆管理工具
      const shortMemoryTool = new ShortTermMemoryTool(this.app);
      const longMemoryTool = new LongTermMemoryTool(this.app);
      await this.orchestrationEngine.registerTool(shortMemoryTool);
      await this.orchestrationEngine.registerTool(longMemoryTool);

      console.log('All tools registered successfully');
    } catch (error) {
      console.error('Failed to register tools:', error);
      throw error;
    }
  }

  private registerCommands() {
    // 主要聊天命令
    this.addCommand({
      id: 'open-ai-chat',
      name: '打开AI助手对话',
      callback: () => this.openChatModal(),
    });

    // 快速查询命令
    this.addCommand({
      id: 'quick-query',
      name: '快速查询',
      callback: () => this.quickQuery(),
    });

    // Vault索引重建命令
    this.addCommand({
      id: 'rebuild-vault-index',
      name: '重建Vault索引',
      callback: () => this.rebuildVaultIndex(),
    });

    // 清理对话历史命令
    this.addCommand({
      id: 'clear-conversation-history',
      name: '清理对话历史',
      callback: () => this.clearConversationHistory(),
    });
  }

  private registerVaultEvents() {
    // 监听文件创建
    this.registerEvent(
      this.app.vault.on('create', (file) => {
        if (file instanceof TFile && file.extension === 'md') {
          this.onFileChanged(file);
        }
      })
    );

    // 监听文件修改
    this.registerEvent(
      this.app.vault.on('modify', (file) => {
        if (file instanceof TFile && file.extension === 'md') {
          this.onFileChanged(file);
        }
      })
    );

    // 监听文件删除
    this.registerEvent(
      this.app.vault.on('delete', (file) => {
        if (file instanceof TFile && file.extension === 'md') {
          this.onFileDeleted(file);
        }
      })
    );
  }

  private async onFileChanged(file: TFile) {
    if (this.config.tools.vault.indexingEnabled) {
      // 这里将在后续任务中实现Vault索引更新
      console.log(`File changed: ${file.path}`);
    }
  }

  private async onFileDeleted(file: TFile) {
    if (this.config.tools.vault.indexingEnabled) {
      // 这里将在后续任务中实现Vault索引删除
      console.log(`File deleted: ${file.path}`);
    }
  }

  private async openChatModal() {
    if (!this.chatModal) {
      this.chatModal = new ChatModal(this.app, this.orchestrator);
    }
    this.chatModal.open();
  }

  private async quickQuery() {
    // 这里将在后续任务中实现快速查询功能
    new Notice('快速查询功能开发中...');
  }

  private async rebuildVaultIndex() {
    try {
      new Notice('开始重建Vault索引...');
      // 这里将在后续任务中实现索引重建
      new Notice('Vault索引重建完成');
    } catch (error) {
      console.error('Failed to rebuild vault index:', error);
      new Notice('索引重建失败: ' + error.message);
    }
  }

  private async clearConversationHistory() {
    try {
      await this.memoryManager.clearAllConversations();
      new Notice('对话历史已清理');
    } catch (error) {
      console.error('Failed to clear conversation history:', error);
      new Notice('清理对话历史失败: ' + error.message);
    }
  }

  // 公共方法供其他组件使用
  public getConfig(): PluginConfig {
    return this.config;
  }

  public async updateConfig(newConfig: Partial<PluginConfig>) {
    this.config = { ...this.config, ...newConfig };
    await this.configManager.saveConfig();
    
    // 重新初始化相关组件
    if (newConfig.llm) {
      await this.orchestrator.updateLLMConfig(newConfig.llm);
    }
  }

  public getOrchestrator(): LLMOrchestrator {
    return this.orchestrator;
  }

  public getMemoryManager(): MemoryManager {
    return this.memoryManager;
  }

  public getToolRegistry(): ToolRegistry {
    return this.toolRegistry;
  }
}
