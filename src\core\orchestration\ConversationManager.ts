import { ConversationContext, ConversationMessage } from '@/types/memory';
import { ShortTermMemoryTool } from '@/tools/memory/ShortTermMemoryTool';
import { LongTermMemoryTool } from '@/tools/memory/LongTermMemoryTool';
import { ExecutionStep } from './TaskPlanner';

/**
 * 对话管理器
 * 负责对话状态的维护、上下文管理和记忆存储
 */
export class ConversationManager {
  private shortTermMemory: ShortTermMemoryTool;
  private longTermMemory: LongTermMemoryTool;
  private currentConversationId: string | null = null;
  private conversationState: ConversationState = 'idle';
  private contextWindow: number = 10; // 保持的消息数量

  constructor(
    shortTermMemory: ShortTermMemoryTool,
    longTermMemory: LongTermMemoryTool
  ) {
    this.shortTermMemory = shortTermMemory;
    this.longTermMemory = longTermMemory;
  }

  /**
   * 开始新对话
   */
  async startConversation(metadata?: ConversationMetadata): Promise<string> {
    const result = await this.shortTermMemory.execute({
      action: 'create',
      metadata: {
        title: metadata?.title || `对话 ${new Date().toLocaleString()}`,
        tags: metadata?.tags || [],
        summary: metadata?.summary
      }
    });

    if (result.success) {
      this.currentConversationId = result.data.conversationId;
      this.conversationState = 'active';
      return this.currentConversationId;
    }

    throw new Error('创建对话失败');
  }

  /**
   * 添加用户消息
   */
  async addUserMessage(content: string, metadata?: MessageMetadata): Promise<void> {
    if (!this.currentConversationId) {
      await this.startConversation();
    }

    const message: ConversationMessage = {
      id: this.generateMessageId(),
      role: 'user',
      content,
      timestamp: new Date(),
      metadata: metadata || {}
    };

    await this.shortTermMemory.execute({
      action: 'add_message',
      conversationId: this.currentConversationId,
      message
    });

    this.conversationState = 'processing';
  }

  /**
   * 添加助手消息
   */
  async addAssistantMessage(
    content: string, 
    executionLog?: ExecutionStep[],
    metadata?: MessageMetadata
  ): Promise<void> {
    if (!this.currentConversationId) {
      throw new Error('没有活动的对话');
    }

    const message: ConversationMessage = {
      id: this.generateMessageId(),
      role: 'assistant',
      content,
      timestamp: new Date(),
      metadata: {
        ...metadata,
        executionLog: executionLog?.map(step => ({
          type: step.type,
          content: step.content,
          timestamp: step.timestamp,
          toolName: step.toolName
        }))
      }
    };

    await this.shortTermMemory.execute({
      action: 'add_message',
      conversationId: this.currentConversationId,
      message
    });

    this.conversationState = 'active';

    // 更新对话元数据
    await this.updateConversationMetadata(executionLog);
  }

  /**
   * 获取对话上下文
   */
  async getConversationContext(limit?: number): Promise<ConversationContext | null> {
    if (!this.currentConversationId) {
      return null;
    }

    const result = await this.shortTermMemory.execute({
      action: 'get',
      conversationId: this.currentConversationId
    });

    if (result.success) {
      const conversation = result.data.conversation;
      
      // 限制上下文窗口
      const contextLimit = limit || this.contextWindow;
      if (conversation.messages.length > contextLimit) {
        conversation.messages = conversation.messages.slice(-contextLimit);
      }

      return conversation;
    }

    return null;
  }

  /**
   * 获取最近的消息
   */
  async getRecentMessages(count: number = 5): Promise<ConversationMessage[]> {
    const context = await this.getConversationContext(count);
    return context?.messages || [];
  }

  /**
   * 构建LLM上下文
   */
  async buildLLMContext(includeSystemPrompt: boolean = true): Promise<{
    systemPrompt?: string;
    messages: Array<{ role: string; content: string }>;
  }> {
    const context = await this.getConversationContext();
    
    if (!context) {
      return { messages: [] };
    }

    const messages = context.messages.map(msg => ({
      role: msg.role,
      content: msg.content
    }));

    const result: any = { messages };

    if (includeSystemPrompt) {
      result.systemPrompt = this.buildSystemPrompt(context);
    }

    return result;
  }

  /**
   * 更新对话状态
   */
  setConversationState(state: ConversationState): void {
    this.conversationState = state;
  }

  /**
   * 获取对话状态
   */
  getConversationState(): ConversationState {
    return this.conversationState;
  }

  /**
   * 结束对话
   */
  async endConversation(summary?: string): Promise<void> {
    if (!this.currentConversationId) {
      return;
    }

    // 更新对话摘要
    if (summary) {
      await this.shortTermMemory.execute({
        action: 'update',
        conversationId: this.currentConversationId,
        metadata: { summary }
      });
    }

    // 保存重要信息到长期记忆
    await this.saveImportantInformation();

    this.conversationState = 'ended';
    this.currentConversationId = null;
  }

  /**
   * 搜索对话历史
   */
  async searchConversationHistory(query: string, limit: number = 5): Promise<ConversationContext[]> {
    const searchResults = this.shortTermMemory.searchConversations(query, limit);
    return searchResults;
  }

  /**
   * 获取对话统计
   */
  async getConversationStats(): Promise<ConversationStats> {
    const stats = this.shortTermMemory.getStats();
    const currentContext = await this.getConversationContext();

    return {
      totalConversations: stats.totalConversations,
      totalMessages: stats.totalMessages,
      currentConversationLength: currentContext?.messages.length || 0,
      averageMessagesPerConversation: stats.averageMessagesPerConversation,
      conversationState: this.conversationState
    };
  }

  /**
   * 清理旧对话
   */
  async cleanupOldConversations(daysOld: number = 30): Promise<number> {
    // 这里应该实现清理逻辑
    // 目前只是返回0作为占位符
    return 0;
  }

  /**
   * 更新对话元数据
   */
  private async updateConversationMetadata(executionLog?: ExecutionStep[]): Promise<void> {
    if (!this.currentConversationId || !executionLog) {
      return;
    }

    const toolsUsed = executionLog
      .filter(step => step.toolName)
      .map(step => step.toolName!)
      .filter((tool, index, array) => array.indexOf(tool) === index);

    await this.shortTermMemory.execute({
      action: 'update',
      conversationId: this.currentConversationId,
      metadata: {
        toolsUsed: toolsUsed,
        lastActivity: new Date().toISOString()
      }
    });
  }

  /**
   * 构建系统提示
   */
  private buildSystemPrompt(context: ConversationContext): string {
    const recentTools = context.metadata.toolsUsed || [];
    const conversationLength = context.messages.length;

    let systemPrompt = '你是一个智能的Obsidian助手。';

    if (recentTools.length > 0) {
      systemPrompt += `\n\n在这次对话中，你已经使用了以下工具：${recentTools.join(', ')}。`;
    }

    if (conversationLength > 5) {
      systemPrompt += '\n\n这是一个较长的对话，请保持上下文的连贯性。';
    }

    return systemPrompt;
  }

  /**
   * 保存重要信息到长期记忆
   */
  private async saveImportantInformation(): Promise<void> {
    const context = await this.getConversationContext();
    if (!context || context.messages.length < 3) {
      return;
    }

    // 提取重要信息的简单逻辑
    const importantMessages = context.messages.filter(msg => 
      msg.content.length > 50 && 
      (msg.content.includes('重要') || 
       msg.content.includes('记住') || 
       msg.content.includes('偏好'))
    );

    for (const message of importantMessages) {
      try {
        await this.longTermMemory.execute({
          action: 'save_memory',
          content: message.content,
          tags: ['conversation', 'important']
        });
      } catch (error) {
        console.warn('保存重要信息失败:', error);
      }
    }
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取当前对话ID
   */
  getCurrentConversationId(): string | null {
    return this.currentConversationId;
  }

  /**
   * 设置上下文窗口大小
   */
  setContextWindow(size: number): void {
    this.contextWindow = Math.max(1, Math.min(50, size));
  }
}

/**
 * 对话状态
 */
export type ConversationState = 'idle' | 'active' | 'processing' | 'waiting' | 'ended' | 'error';

/**
 * 对话元数据
 */
export interface ConversationMetadata {
  title?: string;
  tags?: string[];
  summary?: string;
}

/**
 * 消息元数据
 */
export interface MessageMetadata {
  [key: string]: any;
}

/**
 * 对话统计
 */
export interface ConversationStats {
  totalConversations: number;
  totalMessages: number;
  currentConversationLength: number;
  averageMessagesPerConversation: number;
  conversationState: ConversationState;
}
