/**
 * 简单的插件启动测试
 * 这个脚本模拟 Obsidian 环境来测试插件是否能正常启动
 */

// 模拟 Obsidian API
const mockObsidian = {
  Plugin: class Plugin {
    constructor(app, manifest) {
      this.app = app;
      this.manifest = manifest;
    }
    
    async loadData() {
      return null; // 模拟没有保存的数据
    }
    
    async saveData(data) {
      console.log('保存数据:', JSON.stringify(data, null, 2));
    }
    
    addCommand(command) {
      console.log('注册命令:', command.id, '-', command.name);
    }
    
    addSettingTab(tab) {
      console.log('添加设置标签');
    }
    
    addStatusBarItem() {
      return {
        createSpan: () => ({ innerHTML: '' }),
        createDiv: () => ({ createDiv: () => ({}) }),
        empty: () => {},
        addClass: () => {},
        addEventListener: () => {},
        removeEventListener: () => {}
      };
    }
    
    registerEvent() {
      console.log('注册事件监听器');
    }
  },
  
  Notice: class Notice {
    constructor(message, timeout) {
      console.log('通知:', message);
    }
  },
  
  Modal: class Modal {
    constructor(app) {
      this.app = app;
    }
    
    open() {
      console.log('打开模态框');
    }
    
    close() {
      console.log('关闭模态框');
    }
  },
  
  Setting: class Setting {
    constructor(containerEl) {
      this.containerEl = containerEl;
    }
    
    setName(name) {
      console.log('设置名称:', name);
      return this;
    }
    
    setDesc(desc) {
      console.log('设置描述:', desc);
      return this;
    }
    
    addText(cb) {
      const text = { setValue: () => text, onChange: () => text };
      if (cb) cb(text);
      return this;
    }
    
    addDropdown(cb) {
      const dropdown = { 
        addOption: () => dropdown, 
        setValue: () => dropdown, 
        onChange: () => dropdown 
      };
      if (cb) cb(dropdown);
      return this;
    }
    
    addToggle(cb) {
      const toggle = { setValue: () => toggle, onChange: () => toggle };
      if (cb) cb(toggle);
      return this;
    }
    
    addButton(cb) {
      const button = { 
        setButtonText: () => button, 
        onClick: () => button,
        setDisabled: () => button
      };
      if (cb) cb(button);
      return this;
    }
  },
  
  PluginSettingTab: class PluginSettingTab {
    constructor(app, plugin) {
      this.app = app;
      this.plugin = plugin;
    }
    
    display() {
      console.log('显示设置页面');
    }
  },
  
  TFile: class TFile {
    constructor(path) {
      this.path = path;
      this.name = path.split('/').pop();
      this.extension = path.split('.').pop();
    }
  }
};

// 模拟 App
const mockApp = {
  vault: {
    getMarkdownFiles: () => [
      new mockObsidian.TFile('note1.md'),
      new mockObsidian.TFile('note2.md')
    ],
    read: async (file) => `# ${file.name}\n\n这是一个测试笔记。`,
    adapter: {
      exists: async () => true,
      read: async () => '{}',
      write: async () => {},
      mkdir: async () => {},
      stat: async () => ({ mtime: Date.now() })
    },
    on: () => {}
  },
  
  metadataCache: {
    getFileCache: () => ({
      headings: [],
      links: [],
      tags: []
    })
  },
  
  plugins: {
    plugins: {
      'calendar': { manifest: { id: 'calendar', name: 'Calendar' } },
      'daily-notes': { manifest: { id: 'daily-notes', name: 'Daily Notes' } }
    },
    enabledPlugins: new Set(['calendar', 'daily-notes'])
  },
  
  commands: {
    commands: {
      'daily-notes': {
        id: 'daily-notes',
        name: '创建今日笔记',
        callback: () => console.log('执行命令: 创建今日笔记')
      }
    }
  }
};

// 模拟插件清单
const mockManifest = {
  id: 'obsidian-ai-coach-advanced',
  name: 'AI Coach Advanced',
  version: '0.2.0',
  minAppVersion: '1.0.0',
  description: 'Advanced AI assistant for Obsidian',
  author: 'AI Coach Team'
};

// 设置全局变量
global.require = () => mockObsidian;

// 测试函数
async function testPluginStartup() {
  console.log('🧪 开始测试插件启动...\n');
  
  try {
    // 加载构建后的插件代码
    const fs = require('fs');
    const path = require('path');
    
    const pluginCode = fs.readFileSync(path.join(__dirname, 'dist', 'main.js'), 'utf8');
    
    // 创建一个安全的执行环境
    const vm = require('vm');
    const context = {
      console,
      require: () => mockObsidian,
      module: { exports: {} },
      exports: {},
      global: {},
      Buffer,
      process: { env: { NODE_ENV: 'test' } },
      setTimeout,
      clearTimeout,
      setInterval,
      clearInterval
    };
    
    // 执行插件代码
    vm.createContext(context);
    vm.runInContext(pluginCode, context);
    
    // 获取插件类
    const PluginClass = context.module.exports.default || context.module.exports;
    
    if (!PluginClass) {
      throw new Error('无法找到插件类');
    }
    
    console.log('✅ 插件代码加载成功');
    
    // 创建插件实例
    const plugin = new PluginClass(mockApp, mockManifest);
    console.log('✅ 插件实例创建成功');
    
    // 测试插件加载
    await plugin.onload();
    console.log('✅ 插件加载成功');
    
    // 测试配置管理
    if (plugin.getConfig) {
      const config = plugin.getConfig();
      console.log('✅ 配置获取成功:', config ? '有配置' : '无配置');
    }
    
    // 测试编排引擎
    if (plugin.getOrchestrationEngine) {
      const engine = plugin.getOrchestrationEngine();
      console.log('✅ 编排引擎获取成功:', engine ? '已初始化' : '未初始化');
    }
    
    console.log('\n🎉 插件启动测试通过！');
    return true;
    
  } catch (error) {
    console.error('\n❌ 插件启动测试失败:');
    console.error('错误类型:', error.constructor.name);
    console.error('错误消息:', error.message);
    console.error('错误堆栈:', error.stack);
    return false;
  }
}

// 运行测试
if (require.main === module) {
  testPluginStartup().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { testPluginStartup };
