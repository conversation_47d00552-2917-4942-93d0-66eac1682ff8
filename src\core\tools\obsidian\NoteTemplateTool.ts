import { App, TFile, moment } from 'obsidian';
import { BaseTool } from '../BaseTool';
import { ToolResult } from '../types/ToolTypes';

/**
 * 笔记模板工具
 * 提供各种预定义的笔记模板，快速创建结构化笔记
 */
export class NoteTemplateTool extends BaseTool {
  constructor(private app: App) {
    super(
      'note_template',
      '笔记模板工具',
      '使用预定义模板快速创建各种类型的笔记',
      'obsidian',
      {
        templateType: {
          type: 'string',
          required: true,
          description: '模板类型: daily, meeting, project, book, idea, todo, journal, research'
        },
        title: {
          type: 'string',
          required: true,
          description: '笔记标题'
        },
        folder: {
          type: 'string',
          required: false,
          description: '目标文件夹路径（可选）'
        },
        tags: {
          type: 'array',
          required: false,
          description: '标签列表（可选）'
        },
        customFields: {
          type: 'object',
          required: false,
          description: '自定义字段（可选）'
        }
      }
    );
  }

  async execute(args: any): Promise<ToolResult> {
    const { templateType, title, folder = '', tags = [], customFields = {} } = args;

    try {
      console.log(`📝 NoteTemplateTool: 创建${templateType}模板笔记: ${title}`);

      const template = this.getTemplate(templateType, title, tags, customFields);
      if (!template) {
        return {
          success: false,
          error: `不支持的模板类型: ${templateType}。支持的类型: daily, meeting, project, book, idea, todo, journal, research`
        };
      }

      // 生成文件路径
      const fileName = this.generateFileName(templateType, title);
      const filePath = folder ? `${folder}/${fileName}` : fileName;

      // 检查文件是否已存在
      const existingFile = this.app.vault.getAbstractFileByPath(filePath);
      if (existingFile) {
        return {
          success: false,
          error: `文件已存在: ${filePath}`
        };
      }

      // 创建文件
      const file = await this.app.vault.create(filePath, template.content);
      
      console.log(`✅ 模板笔记创建成功: ${filePath}`);

      return {
        success: true,
        data: {
          templateType: templateType,
          title: title,
          path: filePath,
          name: file.name,
          size: file.stat.size,
          created: new Date(file.stat.ctime).toISOString(),
          template: template.description
        }
      };
    } catch (error) {
      console.error('NoteTemplateTool执行失败:', error);
      return {
        success: false,
        error: `创建模板笔记失败: ${error.message}`
      };
    }
  }

  /**
   * 获取指定类型的模板
   */
  private getTemplate(type: string, title: string, tags: string[], customFields: any): { content: string; description: string } | null {
    const now = moment();
    const dateStr = now.format('YYYY-MM-DD');
    const timeStr = now.format('HH:mm');
    const tagsStr = tags.length > 0 ? tags.map(tag => `#${tag}`).join(' ') : '';

    switch (type.toLowerCase()) {
      case 'daily':
        return {
          content: this.getDailyTemplate(title, dateStr, tagsStr, customFields),
          description: '日记模板'
        };

      case 'meeting':
        return {
          content: this.getMeetingTemplate(title, dateStr, timeStr, tagsStr, customFields),
          description: '会议记录模板'
        };

      case 'project':
        return {
          content: this.getProjectTemplate(title, dateStr, tagsStr, customFields),
          description: '项目管理模板'
        };

      case 'book':
        return {
          content: this.getBookTemplate(title, dateStr, tagsStr, customFields),
          description: '读书笔记模板'
        };

      case 'idea':
        return {
          content: this.getIdeaTemplate(title, dateStr, timeStr, tagsStr, customFields),
          description: '想法记录模板'
        };

      case 'todo':
        return {
          content: this.getTodoTemplate(title, dateStr, tagsStr, customFields),
          description: '任务清单模板'
        };

      case 'journal':
        return {
          content: this.getJournalTemplate(title, dateStr, timeStr, tagsStr, customFields),
          description: '日志模板'
        };

      case 'research':
        return {
          content: this.getResearchTemplate(title, dateStr, tagsStr, customFields),
          description: '研究笔记模板'
        };

      default:
        return null;
    }
  }

  /**
   * 生成文件名
   */
  private generateFileName(type: string, title: string): string {
    const now = moment();
    const dateStr = now.format('YYYY-MM-DD');
    const cleanTitle = title.replace(/[<>:"/\\|?*]/g, '-');

    switch (type.toLowerCase()) {
      case 'daily':
        return `${dateStr}-${cleanTitle}.md`;
      case 'meeting':
        return `会议-${dateStr}-${cleanTitle}.md`;
      case 'project':
        return `项目-${cleanTitle}.md`;
      default:
        return `${cleanTitle}.md`;
    }
  }

  // 各种模板内容
  private getDailyTemplate(title: string, date: string, tags: string, custom: any): string {
    return `# ${title}

**日期**: ${date}
**天气**: ${custom.weather || ''}
**心情**: ${custom.mood || ''}

${tags}

## 今日计划
- [ ] 
- [ ] 
- [ ] 

## 重要事件
- 

## 学习收获
- 

## 反思总结
- 

## 明日计划
- [ ] 
- [ ] 

---
*创建时间: ${moment().format('YYYY-MM-DD HH:mm:ss')}*`;
  }

  private getMeetingTemplate(title: string, date: string, time: string, tags: string, custom: any): string {
    return `# ${title}

**日期**: ${date}
**时间**: ${time}
**地点**: ${custom.location || ''}
**主持人**: ${custom.host || ''}
**参与者**: ${custom.participants || ''}

${tags}

## 会议议程
1. 
2. 
3. 

## 讨论要点
### 议题一
- 

### 议题二
- 

## 决策事项
- [ ] 
- [ ] 

## 行动计划
| 任务 | 负责人 | 截止日期 | 状态 |
|------|--------|----------|------|
|      |        |          |      |

## 后续跟进
- 

---
*会议记录: ${moment().format('YYYY-MM-DD HH:mm:ss')}*`;
  }

  private getProjectTemplate(title: string, date: string, tags: string, custom: any): string {
    return `# ${title}

**创建日期**: ${date}
**项目状态**: ${custom.status || '计划中'}
**优先级**: ${custom.priority || '中'}
**负责人**: ${custom.owner || ''}
**截止日期**: ${custom.deadline || ''}

${tags}

## 项目概述
${custom.description || ''}

## 项目目标
- 
- 
- 

## 关键里程碑
- [ ] 
- [ ] 
- [ ] 

## 任务分解
### 阶段一
- [ ] 
- [ ] 

### 阶段二
- [ ] 
- [ ] 

## 资源需求
- **人力**: 
- **技术**: 
- **预算**: 

## 风险评估
| 风险 | 影响程度 | 应对措施 |
|------|----------|----------|
|      |          |          |

## 项目日志
### ${date}
- 项目启动

---
*项目创建: ${moment().format('YYYY-MM-DD HH:mm:ss')}*`;
  }

  private getBookTemplate(title: string, date: string, tags: string, custom: any): string {
    return `# ${title}

**作者**: ${custom.author || ''}
**出版社**: ${custom.publisher || ''}
**ISBN**: ${custom.isbn || ''}
**阅读日期**: ${date}
**评分**: ${custom.rating || ''}/5

${tags}

## 基本信息
- **页数**: ${custom.pages || ''}
- **分类**: ${custom.category || ''}
- **推荐人**: ${custom.recommender || ''}

## 内容概要
${custom.summary || ''}

## 章节笔记
### 第一章
- 

### 第二章
- 

## 重要观点
1. 
2. 
3. 

## 金句摘录
> 

## 个人思考
- 

## 行动计划
- [ ] 
- [ ] 

## 相关书籍
- 

---
*读书笔记: ${moment().format('YYYY-MM-DD HH:mm:ss')}*`;
  }

  private getIdeaTemplate(title: string, date: string, time: string, tags: string, custom: any): string {
    return `# ${title}

**记录时间**: ${date} ${time}
**灵感来源**: ${custom.source || ''}
**相关领域**: ${custom.domain || ''}

${tags}

## 核心想法
${custom.core || ''}

## 详细描述
- 

## 可行性分析
### 优势
- 

### 挑战
- 

### 机会
- 

## 实施步骤
1. [ ] 
2. [ ] 
3. [ ] 

## 相关资源
- 

## 后续思考
- 

---
*想法记录: ${moment().format('YYYY-MM-DD HH:mm:ss')}*`;
  }

  private getTodoTemplate(title: string, date: string, tags: string, custom: any): string {
    return `# ${title}

**创建日期**: ${date}
**优先级**: ${custom.priority || '中'}
**分类**: ${custom.category || ''}

${tags}

## 今日任务
- [ ] 
- [ ] 
- [ ] 

## 本周任务
- [ ] 
- [ ] 
- [ ] 

## 长期目标
- [ ] 
- [ ] 

## 已完成
- [x] 

## 备注
- 

---
*任务清单: ${moment().format('YYYY-MM-DD HH:mm:ss')}*`;
  }

  private getJournalTemplate(title: string, date: string, time: string, tags: string, custom: any): string {
    return `# ${title}

**日期**: ${date} ${time}
**地点**: ${custom.location || ''}
**天气**: ${custom.weather || ''}

${tags}

## 今日感悟
${custom.reflection || ''}

## 重要事件
- 

## 情绪记录
**心情**: ${custom.mood || ''}
**能量水平**: ${custom.energy || ''}/10

## 学习与成长
- 

## 感恩记录
1. 
2. 
3. 

## 明日期待
- 

---
*日志记录: ${moment().format('YYYY-MM-DD HH:mm:ss')}*`;
  }

  private getResearchTemplate(title: string, date: string, tags: string, custom: any): string {
    return `# ${title}

**研究日期**: ${date}
**研究领域**: ${custom.field || ''}
**研究方法**: ${custom.method || ''}
**数据来源**: ${custom.source || ''}

${tags}

## 研究问题
${custom.question || ''}

## 研究假设
- 

## 文献综述
### 相关研究
- 

### 理论基础
- 

## 研究方法
### 数据收集
- 

### 分析方法
- 

## 研究发现
### 主要结果
- 

### 数据分析
- 

## 结论与讨论
### 主要结论
- 

### 局限性
- 

### 未来研究方向
- 

## 参考文献
1. 

---
*研究笔记: ${moment().format('YYYY-MM-DD HH:mm:ss')}*`;
  }
}
