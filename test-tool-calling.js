/**
 * 测试工具调用功能
 */

// 模拟 Obsidian API
const mockObsidian = {
  Plugin: class Plugin {
    constructor(app, manifest) {
      this.app = app;
      this.manifest = manifest;
      this.savedData = null;
    }
    
    async loadData() {
      return this.savedData;
    }
    
    async saveData(data) {
      this.savedData = data;
      console.log('✅ 配置已保存');
    }
    
    addCommand() {}
    addSettingTab() {}
    addStatusBarItem() {
      return {
        createSpan: () => ({ innerHTML: '' }),
        createDiv: () => ({ createDiv: () => ({}) }),
        empty: () => {},
        addClass: () => {},
        addEventListener: () => {},
        removeEventListener: () => {}
      };
    }
    registerEvent() {}
  },
  
  Notice: class Notice {
    constructor(message, timeout) {
      console.log('📢 通知:', message);
    }
  },
  
  TFile: class TFile {
    constructor(path) {
      this.path = path;
      this.name = path.split('/').pop();
      this.extension = path.split('.').pop();
    }
  }
};

// 模拟 App
const mockApp = {
  vault: {
    getMarkdownFiles: () => [
      new mockObsidian.TFile('项目管理.md'),
      new mockObsidian.TFile('学习笔记.md'),
      new mockObsidian.TFile('工作总结.md')
    ],
    read: async (file) => {
      const content = {
        '项目管理.md': '# 项目管理\n\n## 敏捷开发\n敏捷开发是一种迭代的软件开发方法...',
        '学习笔记.md': '# 学习笔记\n\n## JavaScript\nJavaScript是一种动态编程语言...',
        '工作总结.md': '# 工作总结\n\n本周完成了以下任务：\n1. 代码重构\n2. 文档更新'
      };
      return content[file.name] || '# 测试笔记\n\n这是一个测试笔记。';
    },
    adapter: {
      exists: async () => true,
      read: async () => '{}',
      write: async () => {},
      mkdir: async () => {},
      stat: async () => ({ mtime: Date.now() })
    },
    on: () => {}
  },
  
  metadataCache: {
    getFileCache: () => ({
      headings: [],
      links: [],
      tags: []
    })
  },
  
  plugins: {
    plugins: {
      'calendar': { manifest: { id: 'calendar', name: 'Calendar' } },
      'daily-notes': { manifest: { id: 'daily-notes', name: 'Daily Notes' } }
    },
    enabledPlugins: new Set(['calendar', 'daily-notes'])
  },
  
  commands: {
    commands: {
      'daily-notes': {
        id: 'daily-notes',
        name: '创建今日笔记',
        callback: () => console.log('执行命令: 创建今日笔记')
      }
    }
  }
};

// 模拟插件清单
const mockManifest = {
  id: 'obsidian-ai-coach-advanced',
  name: 'AI Coach Advanced',
  version: '0.2.1'
};

// 设置全局变量
global.require = () => mockObsidian;

async function testToolCalling() {
  console.log('🧪 开始测试工具调用功能...\n');
  
  try {
    // 加载构建后的插件代码
    const fs = require('fs');
    const path = require('path');
    
    const pluginCode = fs.readFileSync(path.join(__dirname, 'dist', 'main.js'), 'utf8');
    
    // 创建执行环境
    const vm = require('vm');
    const context = {
      console,
      require: () => mockObsidian,
      module: { exports: {} },
      exports: {},
      global: {},
      Buffer,
      process: { env: { NODE_ENV: 'test' } },
      setTimeout,
      clearTimeout,
      setInterval,
      clearInterval,
      btoa: (str) => Buffer.from(str).toString('base64'),
      atob: (str) => Buffer.from(str, 'base64').toString(),
      fetch: async (url, options) => {
        console.log('🌐 模拟网络请求:', url);
        return {
          ok: true,
          json: async () => ({
            results: [
              { title: '测试搜索结果', snippet: '这是一个测试搜索结果' }
            ]
          })
        };
      }
    };
    
    // 执行插件代码
    vm.createContext(context);
    vm.runInContext(pluginCode, context);
    
    const PluginClass = context.module.exports.default || context.module.exports;
    const plugin = new PluginClass(mockApp, mockManifest);
    
    // 初始化插件
    await plugin.onload();
    console.log('✅ 插件初始化成功\n');
    
    // 设置API密钥
    await plugin.updateConfig({
      llm: {
        apiKey: 'test-api-key-12345',
        provider: 'openai',
        model: 'gpt-3.5-turbo'
      }
    });
    console.log('✅ API密钥配置成功\n');
    
    const orchestrationEngine = plugin.getOrchestrationEngine();
    
    // 检查工具注册
    const availableTools = orchestrationEngine.getAvailableTools();
    console.log('📋 已注册的工具:');
    availableTools.forEach(tool => {
      console.log(`  - ${tool.name}: ${tool.description}`);
    });
    console.log('');
    
    // 测试用例1: 搜索笔记
    console.log('🔧 测试1: 搜索笔记内容...');
    try {
      const result1 = await orchestrationEngine.processInput(
        '帮我搜索关于项目管理的笔记',
        { userPreferences: { language: 'zh' } }
      );
      
      console.log('✅ 搜索笔记测试结果:');
      console.log('  - 成功:', result1.success);
      console.log('  - 使用的工具:', result1.toolsUsed);
      console.log('  - 回复:', result1.response.substring(0, 100) + '...');
      console.log('  - 执行日志条数:', result1.executionLog.length);
      console.log('');
    } catch (error) {
      console.error('❌ 搜索笔记测试失败:', error.message);
    }
    
    // 测试用例2: 计算任务
    console.log('🔧 测试2: 执行计算任务...');
    try {
      const result2 = await orchestrationEngine.processInput(
        '帮我计算1到100的平方和',
        { userPreferences: { language: 'zh' } }
      );
      
      console.log('✅ 计算任务测试结果:');
      console.log('  - 成功:', result2.success);
      console.log('  - 使用的工具:', result2.toolsUsed);
      console.log('  - 回复:', result2.response.substring(0, 100) + '...');
      console.log('  - 执行日志条数:', result2.executionLog.length);
      console.log('');
    } catch (error) {
      console.error('❌ 计算任务测试失败:', error.message);
    }
    
    // 测试用例3: 网络搜索
    console.log('🔧 测试3: 网络搜索任务...');
    try {
      const result3 = await orchestrationEngine.processInput(
        '搜索最新的AI发展趋势',
        { userPreferences: { language: 'zh' } }
      );
      
      console.log('✅ 网络搜索测试结果:');
      console.log('  - 成功:', result3.success);
      console.log('  - 使用的工具:', result3.toolsUsed);
      console.log('  - 回复:', result3.response.substring(0, 100) + '...');
      console.log('  - 执行日志条数:', result3.executionLog.length);
      console.log('');
    } catch (error) {
      console.error('❌ 网络搜索测试失败:', error.message);
    }
    
    // 测试用例4: 简单对话（不应该调用工具）
    console.log('🔧 测试4: 简单对话...');
    try {
      const result4 = await orchestrationEngine.processInput(
        '你好，你是谁？',
        { userPreferences: { language: 'zh' } }
      );
      
      console.log('✅ 简单对话测试结果:');
      console.log('  - 成功:', result4.success);
      console.log('  - 使用的工具:', result4.toolsUsed);
      console.log('  - 回复:', result4.response.substring(0, 100) + '...');
      console.log('  - 执行日志条数:', result4.executionLog.length);
      console.log('');
    } catch (error) {
      console.error('❌ 简单对话测试失败:', error.message);
    }
    
    // 分析结果
    console.log('📊 测试结果分析:');
    console.log('- 如果工具调用正常，搜索和计算任务应该使用相应的工具');
    console.log('- 简单对话不应该调用工具');
    console.log('- 执行日志应该包含思考、行动和工具结果等步骤');
    
    console.log('\n🎉 工具调用测试完成！');
    return true;
    
  } catch (error) {
    console.error('\n❌ 工具调用测试失败:');
    console.error('错误类型:', error.constructor.name);
    console.error('错误消息:', error.message);
    console.error('错误堆栈:', error.stack);
    return false;
  }
}

// 运行测试
if (require.main === module) {
  testToolCalling().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { testToolCalling };
