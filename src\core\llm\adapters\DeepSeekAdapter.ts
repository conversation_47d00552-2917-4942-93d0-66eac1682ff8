import { OpenAIAdapter } from './OpenAIAdapter';
import { ModelInfo } from '../LLMInterface';

/**
 * DeepSeek API适配器
 * 基于OpenAI兼容接口实现
 */
export class DeepSeekAdapter extends OpenAIAdapter {
  constructor(apiKey: string, model: string, baseUrl?: string, timeout: number = 30000) {
    super(apiKey, model, baseUrl || 'https://api.deepseek.com/v1', timeout);
  }

  async getModelInfo(): Promise<ModelInfo> {
    const modelMap: Record<string, Partial<ModelInfo>> = {
      'deepseek-chat': {
        maxTokens: 32768,
        supportsFunctionCalling: true,
        costPer1kTokens: { input: 0.0014, output: 0.0028 }
      },
      'deepseek-coder': {
        maxTokens: 16384,
        supportsFunctionCalling: true,
        costPer1kTokens: { input: 0.0014, output: 0.0028 }
      },
      'deepseek-math': {
        maxTokens: 4096,
        supportsFunctionCalling: false,
        costPer1kTokens: { input: 0.0014, output: 0.0028 }
      }
    };

    const modelInfo = modelMap[this.model] || {};
    
    return {
      name: this.model,
      provider: 'DeepSeek',
      maxTokens: modelInfo.maxTokens || 32768,
      supportsFunctionCalling: modelInfo.supportsFunctionCalling || false,
      supportsStreaming: true,
      costPer1kTokens: modelInfo.costPer1kTokens
    };
  }

  async getSupportedModels(): Promise<string[]> {
    try {
      const response = await this.makeRequest('/models');
      return response.data
        .filter((model: any) => model.id.includes('deepseek'))
        .map((model: any) => model.id)
        .sort();
    } catch (error) {
      // 如果无法获取模型列表，返回默认支持的模型
      return ['deepseek-chat', 'deepseek-coder', 'deepseek-math'];
    }
  }

  estimateTokens(text: string): number {
    // DeepSeek的token计算与OpenAI类似
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const otherChars = text.length - chineseChars;
    return Math.ceil(chineseChars / 1.5 + otherChars / 4);
  }

  // 继承OpenAI适配器的其他方法，因为DeepSeek使用OpenAI兼容接口
  private async makeRequest(endpoint: string, body?: any): Promise<any> {
    // 使用父类的makeRequest方法
    return super['makeRequest'](endpoint, body);
  }
}
