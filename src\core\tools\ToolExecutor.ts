import { 
  Tool, 
  ToolResult, 
  ToolExecutionContext, 
  ToolValidationResult,
  ToolCategory 
} from '@/types/tools';
import { ToolRegistry } from './ToolRegistry';

/**
 * 工具执行器
 * 提供安全的工具调用执行机制，包括参数校验、权限检查、错误处理等
 */
export class ToolExecutor {
  private registry: ToolRegistry;
  private executionQueue: ExecutionTask[] = [];
  private isProcessing: boolean = false;
  private maxConcurrentExecutions: number = 3;
  private currentExecutions: Set<string> = new Set();
  private rateLimiter: RateLimiter;

  constructor(registry: ToolRegistry, options?: ToolExecutorOptions) {
    this.registry = registry;
    this.maxConcurrentExecutions = options?.maxConcurrentExecutions || 3;
    this.rateLimiter = new RateLimiter(options?.rateLimit || { maxRequests: 60, windowMs: 60000 });
  }

  /**
   * 执行单个工具
   */
  async executeTool(
    toolName: string,
    args: Record<string, any>,
    context?: ToolExecutionContext
  ): Promise<ToolResult> {
    const executionId = this.generateExecutionId();
    
    try {
      // 检查速率限制
      if (!this.rateLimiter.checkLimit(context?.userId || 'anonymous')) {
        return {
          success: false,
          error: '请求频率过高，请稍后再试',
          metadata: { executionId, rateLimited: true }
        };
      }

      // 获取工具
      const tool = this.registry.get(toolName);
      if (!tool) {
        return {
          success: false,
          error: `工具不存在: ${toolName}`,
          metadata: { executionId }
        };
      }

      // 检查并发限制
      if (this.currentExecutions.size >= this.maxConcurrentExecutions) {
        return {
          success: false,
          error: '系统繁忙，请稍后再试',
          metadata: { executionId, concurrencyLimited: true }
        };
      }

      // 验证参数
      const validation = await this.registry.validate(toolName, args);
      if (!validation.valid) {
        return {
          success: false,
          error: `参数验证失败: ${validation.errors.join(', ')}`,
          metadata: { 
            executionId, 
            validationErrors: validation.errors,
            validationWarnings: validation.warnings
          }
        };
      }

      // 检查权限
      if (context) {
        const permissionCheck = this.checkToolPermissions(tool, context);
        if (!permissionCheck.allowed) {
          return {
            success: false,
            error: `权限不足: ${permissionCheck.reason}`,
            metadata: { executionId, permissionDenied: true }
          };
        }

        // 检查危险操作确认
        if (tool.permissions.requiresConfirmation && !context.config?.confirmed) {
          return {
            success: false,
            error: '此操作需要用户确认',
            metadata: { 
              executionId, 
              requiresConfirmation: true,
              toolName,
              args 
            }
          };
        }
      }

      // 执行工具
      this.currentExecutions.add(executionId);
      
      try {
        const result = await this.registry.execute(toolName, args, {
          ...context,
          requestId: executionId
        });

        // 后处理结果
        return this.postProcessResult(result, tool, args);
      } finally {
        this.currentExecutions.delete(executionId);
      }

    } catch (error) {
      this.currentExecutions.delete(executionId);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        metadata: { executionId, unexpectedError: true }
      };
    }
  }

  /**
   * 批量执行工具
   */
  async executeToolsBatch(
    executions: ToolExecution[],
    context?: ToolExecutionContext
  ): Promise<ToolResult[]> {
    const results: ToolResult[] = [];
    
    for (const execution of executions) {
      const result = await this.executeTool(
        execution.toolName,
        execution.args,
        context
      );
      results.push(result);

      // 如果有工具执行失败且设置了停止策略，则停止后续执行
      if (!result.success && execution.stopOnFailure) {
        break;
      }
    }

    return results;
  }

  /**
   * 异步执行工具（加入队列）
   */
  async queueToolExecution(
    toolName: string,
    args: Record<string, any>,
    context?: ToolExecutionContext,
    priority: ExecutionPriority = 'normal'
  ): Promise<string> {
    const executionId = this.generateExecutionId();
    
    const task: ExecutionTask = {
      id: executionId,
      toolName,
      args,
      context,
      priority,
      createdAt: new Date(),
      status: 'queued'
    };

    this.executionQueue.push(task);
    this.sortQueue();
    
    // 启动队列处理
    if (!this.isProcessing) {
      this.processQueue();
    }

    return executionId;
  }

  /**
   * 获取执行状态
   */
  getExecutionStatus(executionId: string): ExecutionStatus | null {
    // 检查当前执行
    if (this.currentExecutions.has(executionId)) {
      return { status: 'running', executionId };
    }

    // 检查队列
    const queuedTask = this.executionQueue.find(task => task.id === executionId);
    if (queuedTask) {
      return { 
        status: queuedTask.status, 
        executionId,
        queuePosition: this.executionQueue.indexOf(queuedTask) + 1
      };
    }

    return null;
  }

  /**
   * 取消执行
   */
  cancelExecution(executionId: string): boolean {
    const taskIndex = this.executionQueue.findIndex(task => task.id === executionId);
    if (taskIndex !== -1) {
      this.executionQueue.splice(taskIndex, 1);
      return true;
    }
    return false;
  }

  /**
   * 获取队列状态
   */
  getQueueStatus(): QueueStatus {
    return {
      queueLength: this.executionQueue.length,
      currentExecutions: this.currentExecutions.size,
      maxConcurrentExecutions: this.maxConcurrentExecutions,
      isProcessing: this.isProcessing
    };
  }

  /**
   * 检查工具权限
   */
  private checkToolPermissions(tool: Tool, context: ToolExecutionContext): PermissionCheckResult {
    // 检查必需权限
    for (const requiredPermission of tool.permissions.required) {
      if (!context.permissions.includes(requiredPermission)) {
        return {
          allowed: false,
          reason: `缺少必需权限: ${requiredPermission}`
        };
      }
    }

    // 检查危险操作权限
    if (tool.permissions.dangerous && !context.permissions.includes('dangerous_operations')) {
      return {
        allowed: false,
        reason: '此工具执行危险操作，需要特殊权限'
      };
    }

    // 检查分类权限
    const categoryPermission = `tool_${tool.category}`;
    if (!context.permissions.includes(categoryPermission) && !context.permissions.includes('tool_all')) {
      return {
        allowed: false,
        reason: `缺少工具分类权限: ${categoryPermission}`
      };
    }

    return { allowed: true };
  }

  /**
   * 后处理执行结果
   */
  private postProcessResult(result: ToolResult, tool: Tool, args: Record<string, any>): ToolResult {
    // 添加工具信息到元数据
    const enhancedMetadata = {
      ...result.metadata,
      toolName: tool.name,
      toolCategory: tool.category,
      toolVersion: tool.version,
    };

    // 检查结果安全性
    if (tool.permissions.dangerous && result.success) {
      enhancedMetadata.dangerousOperation = true;
    }

    return {
      ...result,
      metadata: enhancedMetadata
    };
  }

  /**
   * 处理执行队列
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing) return;
    
    this.isProcessing = true;

    try {
      while (this.executionQueue.length > 0 && this.currentExecutions.size < this.maxConcurrentExecutions) {
        const task = this.executionQueue.shift();
        if (!task) break;

        task.status = 'running';
        
        // 异步执行任务
        this.executeQueuedTask(task).catch(error => {
          console.error(`Queued task execution failed:`, error);
        });
      }
    } finally {
      this.isProcessing = false;
    }

    // 如果还有任务在队列中，继续处理
    if (this.executionQueue.length > 0) {
      setTimeout(() => this.processQueue(), 100);
    }
  }

  /**
   * 执行队列中的任务
   */
  private async executeQueuedTask(task: ExecutionTask): Promise<void> {
    try {
      const result = await this.executeTool(task.toolName, task.args, task.context);
      task.status = 'completed';
      task.result = result;
    } catch (error) {
      task.status = 'failed';
      task.error = error instanceof Error ? error.message : String(error);
    }
  }

  /**
   * 排序队列（按优先级）
   */
  private sortQueue(): void {
    const priorityOrder = { high: 3, normal: 2, low: 1 };
    this.executionQueue.sort((a, b) => {
      const aPriority = priorityOrder[a.priority];
      const bPriority = priorityOrder[b.priority];
      return bPriority - aPriority;
    });
  }

  /**
   * 生成执行ID
   */
  private generateExecutionId(): string {
    return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 工具执行器选项
 */
export interface ToolExecutorOptions {
  maxConcurrentExecutions?: number;
  rateLimit?: {
    maxRequests: number;
    windowMs: number;
  };
}

/**
 * 工具执行定义
 */
export interface ToolExecution {
  toolName: string;
  args: Record<string, any>;
  stopOnFailure?: boolean;
}

/**
 * 执行任务
 */
export interface ExecutionTask {
  id: string;
  toolName: string;
  args: Record<string, any>;
  context?: ToolExecutionContext;
  priority: ExecutionPriority;
  createdAt: Date;
  status: 'queued' | 'running' | 'completed' | 'failed';
  result?: ToolResult;
  error?: string;
}

/**
 * 执行优先级
 */
export type ExecutionPriority = 'high' | 'normal' | 'low';

/**
 * 执行状态
 */
export interface ExecutionStatus {
  status: 'queued' | 'running' | 'completed' | 'failed';
  executionId: string;
  queuePosition?: number;
  result?: ToolResult;
  error?: string;
}

/**
 * 队列状态
 */
export interface QueueStatus {
  queueLength: number;
  currentExecutions: number;
  maxConcurrentExecutions: number;
  isProcessing: boolean;
}

/**
 * 权限检查结果
 */
export interface PermissionCheckResult {
  allowed: boolean;
  reason?: string;
}

/**
 * 简单的速率限制器
 */
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  private maxRequests: number;
  private windowMs: number;

  constructor(config: { maxRequests: number; windowMs: number }) {
    this.maxRequests = config.maxRequests;
    this.windowMs = config.windowMs;
  }

  checkLimit(userId: string): boolean {
    const now = Date.now();
    const userRequests = this.requests.get(userId) || [];
    
    // 清理过期请求
    const validRequests = userRequests.filter(time => now - time < this.windowMs);
    
    if (validRequests.length >= this.maxRequests) {
      return false;
    }

    validRequests.push(now);
    this.requests.set(userId, validRequests);
    return true;
  }
}
