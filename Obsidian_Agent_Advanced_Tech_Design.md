# Obsidian 智能体插件 (高级版) - 技术设计说明书

### 1. 系统架构概述

*   **1.1 架构风格**：模块化、事件驱动、LLM中心化架构。
*   **1.2 核心组件**：
    *   **用户接口 (UI Layer)**：Obsidian 命令面板、自定义模态框、设置页面，用于接收用户指令和展示结果。
    *   **LLM编排核心 (LLM Orchestration Core)**：插件的“大脑”，负责管理与LLM的交互、任务规划、工具选择与调用、以及响应生成。
    *   **提示工程模块 (Prompt Engineering Module)**：存储和管理与LLM交互的各种提示模板，根据上下文动态生成最终提示。
    *   **大语言模型接口 (LLM Interface)**：抽象层，用于与具体的LLM服务（如OpenAI API、本地模型服务）进行通信。
    *   **工具调用框架 (Tool Invocation Framework)**：提供统一的接口和机制，供LLM编排核心调用各类工具。
        *   **工具集 (Toolbox)**：包含具体的工具实现：
            *   插件管理工具 (PluginManagerTool)
            *   网络查询工具 (WebSearchTool)
            *   JS脚本工具 (JavaScriptTool) + JS执行沙箱 (JavaScriptSandbox)
            *   记忆工具 (MemoryTool)
            *   Vault知识库查询工具 (VaultQueryTool)
    *   **Vault知识库接口 (Vault Knowledge Interface)**：
        *   **索引服务 (IndexingService)**：负责创建和更新Vault内容的索引（如向量嵌入）。
        *   **查询服务 (QueryService)**：根据LLM的请求，从索引中检索相关信息。
    *   **记忆系统 (Memory System)**：管理短期对话历史和长期用户偏好/知识。
    *   **配置管理器 (ConfigurationManager)**：管理插件设置，如LLM API密钥、工具权限等。
*   **1.3 数据流示例 (用户提问Vault内容)**：
    1.  用户通过UI输入：“项目X的最新进展是什么？”
    2.  UI Layer -> LLM Orchestration Core。
    3.  LLM Orchestration Core + Prompt Engineering Module -> 构造初始分析提示 -> LLM Interface -> LLM。
    4.  LLM (分析) -> 识别意图为查询Vault，判断需要使用VaultQueryTool。
    5.  LLM Orchestration Core -> Tool Invocation Framework -> 调用 VaultQueryTool，参数为“项目X 最新进展”。
    6.  VaultQueryTool -> Vault Knowledge Interface (QueryService) -> 查询索引。
    7.  QueryService -> 返回相关笔记片段给 VaultQueryTool。
    8.  VaultQueryTool -> 返回结果给 LLM Orchestration Core。
    9.  LLM Orchestration Core + Prompt Engineering Module -> 构造总结提示 (包含检索到的片段) -> LLM Interface -> LLM。
    10. LLM (总结) -> 生成答案文本。
    11. LLM Orchestration Core -> UI Layer -> 展示答案给用户。
    12. (并行) LLM Orchestration Core -> MemorySystem -> 存储本次交互的上下文。

### 2. 模块详细设计

#### 2.1 LLM编排核心 (LLM Orchestration Core)
*   **职责**：作为插件智能中枢，协调所有操作。
    *   接收和预处理用户输入。
    *   通过提示工程模块与LLM接口交互，进行意图识别、任务规划、工具选择。
    *   管理工具调用流程，包括参数传递、结果处理、错误处理。
    *   维护对话状态和短期记忆。
    *   将LLM生成的最终结果传递给UI层。
*   **关键技术**：状态机或更复杂的规划算法（如ReAct、Plan-and-Execute模式）来管理LLM的多步骤思考和工具使用。

#### 2.2 提示工程模块 (Prompt Engineering Module)
*   **职责**：提供高质量、结构化的提示以引导LLM完成特定任务。
    *   存储基础提示模板（如任务分析、工具选择、内容总结、代码生成等）。
    *   根据当前任务、上下文、可用工具动态组装和填充提示。
    *   管理工具的描述信息，供LLM在工具选择阶段参考。

#### 2.3 大语言模型接口 (LLM Interface)
*   **职责**：解耦LLM编排核心与具体的LLM实现。
    *   提供统一的API调用方法（如 `generateText`, `chatCompletion`）。
    *   处理认证、请求参数构建、响应解析、错误处理。
    *   支持切换不同的LLM后端（如OpenAI GPT系列、Claude、本地部署的Llama等）。

#### 2.4 工具调用框架 (Tool Invocation Framework) 与 工具集
*   **框架职责**：
    *   注册和管理可用工具。
    *   向LLM提供工具列表及其功能描述、参数模式。
    *   接收LLM的工具调用请求（通常是JSON格式，包含工具名和参数）。
    *   安全地执行工具，并将结果（成功或失败信息）返回给LLM编排核心。
*   **各工具设计要点**：
    *   **PluginManagerTool**: 
        *   `list_plugins()`: 返回已安装插件列表及基本信息。
        *   `get_plugin_commands(plugin_id)`: 返回指定插件可执行的命令。
        *   `call_plugin_command(plugin_id, command_name, args)`: 执行插件命令。
    *   **WebSearchTool**: 
        *   `search(query, num_results)`: 使用搜索引擎API（如Google Custom Search, Bing Search, DuckDuckGo）执行查询，返回结果摘要。
    *   **JavaScriptTool & JavaScriptSandbox**:
        *   `JavaScriptTool.execute_script(code_string)`: 接收LLM生成的JS代码字符串。
        *   `JavaScriptSandbox`: 一个高度受限的执行环境 (e.g., using Node.js `vm` module with strict controls, or a web worker based approach if possible, though `vm2` was popular but now has security concerns, so careful selection is needed)。限制文件系统访问、网络访问，只暴露必要的Obsidian API子集。捕获`console.log`输出和错误。
    *   **MemoryTool**:
        *   `save_short_term_memory(key, value)`: 存储对话上下文。
        *   `load_short_term_memory(key)`: 加载对话上下文。
        *   `save_long_term_preference(preference_data)`: 将用户偏好保存到Vault的特定笔记。
        *   `query_long_term_preferences(query)`: 查询用户偏好。
    *   **VaultQueryTool**:
        *   `semantic_search_vault(query_text, top_k)`: 调用Vault知识库接口进行语义搜索。
        *   `keyword_search_vault(keywords, top_k)`: (可选) 提供关键词搜索。

#### 2.5 Vault知识库接口 (Vault Knowledge Interface)
*   **IndexingService**:
    *   文件监控：监听Vault中文件的增删改。
    *   文本处理：从Markdown文件中提取纯文本内容。
    *   嵌入生成：使用句子嵌入模型 (e.g., Sentence-BERT, OpenAI Embeddings API) 将文本块转换为向量。
    *   索引存储：将文本块及其向量存储到向量数据库 (e.g., ChromaDB, FAISS, LanceDB - 优先考虑本地化方案) 或简单的JSON/SQLite文件（对于小型Vault）。
*   **QueryService**:
    *   接收查询文本，生成查询向量。
    *   在向量索引中执行相似度搜索，返回最相关的文本块及其源文件信息。

#### 2.6 记忆系统 (Memory System)
*   **短期记忆 (对话上下文)**：存储在插件运行时内存中，可能包括最近的N轮对话、LLM的中间思考步骤等。随插件关闭而清除或选择性持久化。
*   **长期记忆 (用户偏好、核心知识)**：结构化或非结构化数据，存储在用户Vault中的特定笔记或JSON文件中 (通过Obsidian的`loadData/saveData`或直接文件操作)。LLM可以通过MemoryTool进行读写。

### 3. 技术选型建议
*   **LLM**: OpenAI API (GPT-3.5-turbo, GPT-4), Google genmini，Deepseek和可兼容openai的其他提供商, 或本地模型方案 (Ollama + Llama.cpp/PyTorch, Hugging Face Transformers)。本地模型对隐私友好但对硬件有要求。
*   **文本嵌入模型**: Sentence Transformers (all-MiniLM-L6-v2等，本地运行), OpenAI Ada v2 (API调用)。
*   **向量存储/搜索**: ChromaDB (Python, 本地), FAISS (C++/Python, 本地), LanceDB (Rust/Python, 本地)。对于简单场景，直接计算余弦相似度也可行。
*   **JS沙箱**: Node.js `vm`模块 (需非常谨慎地配置上下文，限制权限)，研究WebAssembly (WASM) 作为更安全执行环境的可能性。避免直接`eval`。
*   **网络搜索API**: free alternatives like DuckDuckGo API (if available and suitable)。
*   **Obsidian API**: 充分利用 `app.vault`, `app.workspace`, `app.metadataCache`, `app.plugins` 等。

### 4. 关键技术挑战与应对
*   **提示工程 (Prompt Engineering)**：
    *   **挑战**：设计出能稳定引导LLM进行正确理解、规划和工具调用的提示非常困难。
    *   **应对**：迭代优化提示；使用结构化提示 (如XML标签)；引入Few-shot示例；允许用户自定义部分提示模板。
*   **LLM的幻觉与不可靠性**：
    *   **挑战**：LLM可能生成错误信息或错误地调用工具。
    *   **应对**：结合Vault知识库进行事实校验；对LLM的工具调用参数进行校验；设计重试和错误恢复机制；明确告知用户LLM输出可能存在不确定性。
*   **JS脚本执行安全**：
    *   **挑战**：允许LLM生成和执行代码存在巨大安全风险。
    *   **应对**：实现一个权限极小化的强沙箱环境；对生成的代码进行静态分析（如果可能）；执行前要求用户明确授权；限制脚本可调用的Obsidian API范围。
*   **Vault索引与查询效率**：
    *   **挑战**：大型Vault的索引创建和更新可能耗时耗资源；查询延迟影响用户体验。
    *   **应对**：优化索引策略（增量索引）；选择高效的本地向量数据库；允许用户配置索引范围和更新频率。
*   **工具调用的健壮性**：
    *   **挑战**：外部API（网络搜索、其他插件）可能失败或返回非预期格式的数据。
    *   **应对**：为工具调用添加超时、重试机制；对返回数据进行严格校验和清洗；LLM应能处理工具执行失败的情况。
*   **成本与性能 (云端LLM)**：
    *   **挑战**：频繁调用云端LLM API会产生费用，并可能受网络延迟影响。
    *   **应对**：优化LLM调用次数；使用更经济的模型进行部分简单任务；引入缓存机制；支持用户配置本地LLM作为替代。
*   **用户隐私**：
    *   **挑战**：将Vault内容或用户指令发送给第三方LLM服务可能引发隐私担忧。
    *   **应对**：优先推广和支持本地LLM方案；对于云端LLM，明确告知数据流向，并提供数据脱敏选项（如果可行）；所有敏感配置（如API密钥）本地加密存储。

### 5. 迭代计划建议 (简化)
*   **阶段1 (核心LLM与Vault查询)**：实现LLM指令理解、Vault语义索引与查询、基本的记忆功能。
*   **阶段2 (基础工具集成)**：集成网络搜索工具、插件信息查询工具。
*   **阶段3 (高级工具与安全性强化)**：集成JS脚本执行工具（带强沙箱）、插件命令调用工具，全面强化安全措施。
*   **阶段4 (优化与用户体验提升)**：持续优化提示工程、性能、错误处理，完善用户交互和配置选项。
