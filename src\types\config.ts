// 配置相关类型定义

export interface PluginConfig {
  llm: LLMConfig;
  tools: ToolsConfig;
  ui: UIConfig;
  security: SecurityConfig;
}

export interface LLMConfig {
  provider: 'openai' | 'gemini' | 'deepseek' | 'custom';
  apiKey: string;
  baseUrl?: string;
  model: string;
  maxTokens: number;
  temperature: number;
  timeout: number;
}

export interface ToolsConfig {
  vault: {
    enabled: boolean;
    indexingEnabled: boolean;
    maxResults: number;
    chunkSize: number;
  };
  web: {
    enabled: boolean;
    searchEngine: 'duckduckgo' | 'google';
    maxResults: number;
    apiKey?: string;
  };
  plugin: {
    enabled: boolean;
    allowedPlugins: string[];
    restrictedCommands: string[];
  };
  javascript: {
    enabled: boolean;
    requireConfirmation: boolean;
    timeout: number;
    memoryLimit: number;
  };
}

export interface UIConfig {
  theme: 'auto' | 'light' | 'dark';
  language: 'zh' | 'en';
  showProgress: boolean;
  autoSave: boolean;
}

export interface SecurityConfig {
  encryptApiKeys: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  maxRequestsPerMinute: number;
  enableAuditLog: boolean;
}

export const DEFAULT_CONFIG: PluginConfig = {
  llm: {
    provider: 'openai',
    apiKey: '',
    model: 'gpt-3.5-turbo',
    maxTokens: 2000,
    temperature: 0.7,
    timeout: 30000,
  },
  tools: {
    vault: {
      enabled: true,
      indexingEnabled: true,
      maxResults: 10,
      chunkSize: 1000,
    },
    web: {
      enabled: true,
      searchEngine: 'duckduckgo',
      maxResults: 5,
    },
    plugin: {
      enabled: false,
      allowedPlugins: [],
      restrictedCommands: [],
    },
    javascript: {
      enabled: false,
      requireConfirmation: true,
      timeout: 5000,
      memoryLimit: 50 * 1024 * 1024, // 50MB
    },
  },
  ui: {
    theme: 'auto',
    language: 'zh',
    showProgress: true,
    autoSave: true,
  },
  security: {
    encryptApiKeys: true,
    logLevel: 'info',
    maxRequestsPerMinute: 60,
    enableAuditLog: true,
  },
};
