# AI Coach Advanced - 工具调用调试指南

## 🐛 调试工具调用问题

如果插件能够分析出应该调用什么工具，但实际上没有调用工具，请按照以下步骤进行调试。

## 🔍 启用调试模式

### 1. 打开浏览器开发者工具

在Obsidian中按 `Ctrl+Shift+I` (Windows/Linux) 或 `Cmd+Option+I` (Mac) 打开开发者工具。

### 2. 查看控制台输出

切换到 "Console" 标签页，现在插件会输出详细的调试信息。

## 📊 调试信息解读

### 工具调用流程调试信息

当你发送消息给AI助手时，应该看到以下调试信息：

```
🚀 OrchestrationEngine.processUserInput 开始处理
📝 用户输入: 帮我搜索关于项目管理的笔记
🔧 可用工具数量: 4
🔧 可用工具列表: ["vault_query", "web_search", "javascript_executor", "plugin_manager"]
📋 规划上下文: {hasConversationHistory: false, availableToolsCount: 4, availableTools: [...]}
🎯 开始任务规划和执行...

🚀 TaskPlanner.planAndExecute 开始执行
📝 用户输入: 帮我搜索关于项目管理的笔记
🔧 可用工具数量: 4
🔧 可用工具列表: ["vault_query", "web_search", "javascript_executor", "plugin_manager"]
📋 开始任务分析...
📋 任务分析结果: {executable: true, complexity: "medium", requiredTools: ["vault_query"]}
📄 分析内容: [任务分析的详细内容]
🔄 开始ReAct循环执行...
🔄 开始ReAct循环，最大迭代次数: 10

🔄 === 第1轮迭代开始 ===
💭 生成思考...
💭 思考结果: 用户想要搜索关于项目管理的笔记...
🎯 生成行动计划...
🤖 发送行动计划请求到LLM...
🤖 LLM行动计划原始响应: {"type": "tool_call", "description": "搜索Vault中关于项目管理的笔记", "toolName": "vault_query", "parameters": {"query": "项目管理"}}
✅ 行动计划解析成功: {type: "tool_call", description: "搜索Vault中关于项目管理的笔记", toolName: "vault_query", parameters: {query: "项目管理"}}
⚡ 执行行动，类型: tool_call
🔧 准备调用工具: vault_query 参数: {query: "项目管理"}
🔧 TaskPlanner.executeTool 开始执行
🔧 工具名称: vault_query
🔧 工具参数: {query: "项目管理"}
🔧 调用 toolRegistry.execute...
🔧 ToolRegistry.execute 开始执行
🔧 工具名称: vault_query
🔧 工具参数: {query: "项目管理"}
🔧 已注册工具: ["vault_query", "web_search", "javascript_executor", "plugin_manager"]
✅ 找到工具: vault_query 类别: vault
🔧 调用工具的execute方法...
🔧 工具执行完成: {success: true, hasData: true, hasError: false, executionTime: 150}
🔧 工具执行完成: {success: true, hasData: true, hasError: false}
📝 添加到已使用工具列表: vault_query
```

## 🚨 常见问题诊断

### 问题1: 没有看到任何调试信息

**可能原因**:
- 插件没有正确加载
- 开发者工具没有正确打开

**解决方案**:
1. 确认插件已启用：设置 → 第三方插件 → AI Coach Advanced
2. 重新加载插件：禁用后重新启用
3. 重启Obsidian

### 问题2: 看到"工具未找到"错误

**调试信息示例**:
```
❌ 工具未找到: vault_query
🔧 已注册工具: []
```

**可能原因**:
- 工具注册失败
- 插件初始化不完整

**解决方案**:
1. 查看插件启动日志
2. 检查API密钥配置
3. 重新安装插件

### 问题3: 任务被判定为不可执行

**调试信息示例**:
```
📋 任务分析结果: {executable: false, complexity: "low", requiredTools: []}
❌ 任务被判定为不可执行，直接返回
```

**可能原因**:
- LLM理解有误
- 提示词不够明确

**解决方案**:
1. 使用更明确的指令
2. 检查API密钥是否有效
3. 尝试不同的表达方式

### 问题4: LLM总是选择final_answer而不是tool_call

**调试信息示例**:
```
🤖 LLM行动计划原始响应: {"type": "final_answer", "answer": "我来帮你搜索..."}
```

**可能原因**:
- LLM模型理解能力不足
- 提示词需要优化
- API配置问题

**解决方案**:
1. 尝试更强的模型（如GPT-4）
2. 使用更明确的指令，如"使用vault_query工具搜索我的笔记"
3. 检查模型配置

### 问题5: JSON解析失败

**调试信息示例**:
```
❌ 行动计划JSON解析失败: Unexpected token
📄 原始响应内容: 我需要搜索你的笔记...
🔄 使用备用行动计划: {type: "final_answer", ...}
```

**可能原因**:
- LLM没有按JSON格式返回
- 模型不支持结构化输出

**解决方案**:
1. 更换支持结构化输出的模型
2. 调整提示词强调JSON格式
3. 检查模型参数设置

### 问题6: 工具执行异常

**调试信息示例**:
```
❌ 工具执行异常: Cannot read property 'vault' of undefined
```

**可能原因**:
- Obsidian API访问问题
- 工具实现有bug
- 权限不足

**解决方案**:
1. 检查Obsidian版本兼容性
2. 查看完整错误堆栈
3. 重启Obsidian

## 🔧 手动测试工具

### 在控制台中手动测试

```javascript
// 获取插件实例
const plugin = app.plugins.plugins['ai-coach-advanced'];

// 检查插件状态
console.log('插件已初始化:', plugin.isInitialized());

// 获取可用工具
const tools = plugin.getOrchestrationEngine().getAvailableTools();
console.log('可用工具:', tools.map(t => t.name));

// 手动测试工具调用
const engine = plugin.getOrchestrationEngine();
engine.processUserInput('搜索我的笔记中关于学习的内容').then(result => {
  console.log('测试结果:', result);
});
```

### 检查工具注册状态

```javascript
// 检查工具注册表
const registry = plugin.getOrchestrationEngine().toolRegistry;
console.log('已注册工具:', Array.from(registry.tools.keys()));

// 检查特定工具
const vaultTool = registry.get('vault_query');
console.log('Vault工具:', vaultTool ? '已注册' : '未注册');
```

## 📝 收集调试信息

如果问题仍然存在，请收集以下信息：

### 1. 基本信息
- Obsidian版本
- 插件版本
- 操作系统
- 使用的LLM提供商和模型

### 2. 配置信息
```javascript
// 在控制台执行
const plugin = app.plugins.plugins['ai-coach-advanced'];
const config = plugin.getConfig();
console.log('LLM配置:', {
  provider: config.llm.provider,
  model: config.llm.model,
  hasApiKey: !!config.llm.apiKey
});
```

### 3. 完整的控制台日志
- 从发送消息开始到结束的完整日志
- 包括所有调试信息和错误信息

### 4. 重现步骤
- 具体的用户输入
- 期望的行为
- 实际的行为

## 🚀 性能调试

### 检查执行时间

```javascript
// 查看执行统计
const stats = await plugin.getOrchestrationEngine().getExecutionStats();
console.log('执行统计:', stats);
```

### 监控工具调用

```javascript
// 查看工具执行历史
const history = plugin.getOrchestrationEngine().toolRegistry.getExecutionHistory(10);
console.log('最近10次工具执行:', history);
```

## 📞 获取帮助

如果以上步骤都无法解决问题，请：

1. **收集完整的调试信息**
2. **创建最小重现案例**
3. **通过GitHub Issues报告问题**
4. **包含所有相关的日志和配置信息**

记住：详细的调试信息是解决问题的关键！🔍
