// Vault相关类型定义

export interface VaultFile {
  path: string;
  name: string;
  extension: string;
  size: number;
  mtime: number;
  content?: string;
}

export interface VaultIndex {
  id: string;
  filePath: string;
  content: string;
  embedding?: number[];
  metadata: VaultIndexMetadata;
  createdAt: Date;
  updatedAt: Date;
}

export interface VaultIndexMetadata {
  title?: string;
  tags: string[];
  links: string[];
  headings: string[];
  wordCount: number;
  chunkIndex: number;
  totalChunks: number;
}

export interface VaultSearchOptions {
  query: string;
  maxResults?: number;
  threshold?: number;
  includeContent?: boolean;
  fileTypes?: string[];
  tags?: string[];
  dateRange?: {
    from?: Date;
    to?: Date;
  };
}

export interface VaultSearchResult {
  file: VaultFile;
  content: string;
  score: number;
  highlights: string[];
  metadata: VaultIndexMetadata;
}

export interface VaultIndexer {
  indexFile(file: VaultFile): Promise<VaultIndex[]>;
  updateIndex(filePath: string): Promise<void>;
  deleteIndex(filePath: string): Promise<void>;
  search(options: VaultSearchOptions): Promise<VaultSearchResult[]>;
  getStats(): Promise<VaultIndexStats>;
}

export interface VaultIndexStats {
  totalFiles: number;
  indexedFiles: number;
  totalChunks: number;
  lastUpdated: Date;
  indexSize: number;
}
