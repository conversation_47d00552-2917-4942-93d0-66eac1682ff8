import { BaseTool, ToolConfig } from '../../core/tools/BaseTool';
import { ToolResult, ToolExecutionContext, WebSearchResult } from '../../types/tools';

/**
 * 网络搜索工具
 * 集成多种搜索引擎API，提供网络信息查询功能
 */
export class WebSearchTool extends BaseTool {
  private searchEngines: Map<string, SearchEngine> = new Map();

  constructor() {
    const config: ToolConfig = {
      name: 'web_search',
      description: '在网络上搜索信息，支持多种搜索引擎',
      category: 'web',
      version: '1.0.0',
      parameters: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: '搜索查询字符串'
          },
          engine: {
            type: 'string',
            description: '搜索引擎',
            enum: ['duckduckgo', 'google', 'bing'],
            default: 'duckduckgo'
          },
          maxResults: {
            type: 'number',
            description: '最大返回结果数',
            default: 5,
            minimum: 1,
            maximum: 20
          },
          language: {
            type: 'string',
            description: '搜索语言',
            default: 'zh-CN'
          },
          safeSearch: {
            type: 'boolean',
            description: '启用安全搜索',
            default: true
          },
          region: {
            type: 'string',
            description: '搜索地区',
            default: 'CN'
          }
        },
        required: ['query']
      },
      permissions: {
        required: ['web_access'],
        optional: ['web_api_key'],
        dangerous: false,
        requiresConfirmation: false
      },
      metadata: {
        author: 'AI Coach Team',
        tags: ['web', 'search', 'information'],
        documentation: '在网络上搜索信息，获取最新的外部知识',
        examples: [
          {
            name: '基础搜索',
            description: '搜索特定主题的信息',
            input: { query: 'artificial intelligence latest news' },
            expectedOutput: { results: [] }
          },
          {
            name: '指定搜索引擎',
            description: '使用特定搜索引擎进行搜索',
            input: { query: 'machine learning', engine: 'google', maxResults: 10 },
            expectedOutput: { results: [] }
          }
        ]
      }
    };

    super(config);
    this.initializeSearchEngines();
  }

  protected async executeInternal(
    args: Record<string, any>,
    context?: ToolExecutionContext
  ): Promise<ToolResult> {
    try {
      const {
        query,
        engine = 'duckduckgo',
        maxResults = 5,
        language = 'zh-CN',
        safeSearch = true,
        region = 'CN'
      } = args;

      const searchEngine = this.searchEngines.get(engine);
      if (!searchEngine) {
        return {
          success: false,
          error: `不支持的搜索引擎: ${engine}`
        };
      }

      const searchOptions: SearchOptions = {
        query,
        maxResults,
        language,
        safeSearch,
        region
      };

      const results = await searchEngine.search(searchOptions);

      return {
        success: true,
        data: {
          query,
          engine,
          results,
          totalResults: results.length,
          searchOptions
        },
        metadata: {
          source: engine,
          timestamp: new Date().toISOString(),
          searchTime: Date.now()
        }
      };

    } catch (error) {
      return {
        success: false,
        error: `网络搜索失败: ${error.message}`,
        metadata: {
          errorType: 'web_search_error'
        }
      };
    }
  }

  /**
   * 初始化搜索引擎
   */
  private initializeSearchEngines(): void {
    this.searchEngines.set('duckduckgo', new DuckDuckGoEngine());
    this.searchEngines.set('google', new GoogleSearchEngine());
    this.searchEngines.set('bing', new BingSearchEngine());
  }

  /**
   * 添加自定义搜索引擎
   */
  addSearchEngine(name: string, engine: SearchEngine): void {
    this.searchEngines.set(name, engine);
  }

  /**
   * 获取支持的搜索引擎列表
   */
  getSupportedEngines(): string[] {
    return Array.from(this.searchEngines.keys());
  }
}

/**
 * 搜索引擎接口
 */
export interface SearchEngine {
  search(options: SearchOptions): Promise<WebSearchResult[]>;
  isConfigured(): boolean;
  configure(config: Record<string, any>): void;
}

/**
 * 搜索选项
 */
export interface SearchOptions {
  query: string;
  maxResults: number;
  language: string;
  safeSearch: boolean;
  region: string;
}

/**
 * DuckDuckGo搜索引擎实现
 */
export class DuckDuckGoEngine implements SearchEngine {
  private baseUrl = 'https://api.duckduckgo.com/';

  async search(options: SearchOptions): Promise<WebSearchResult[]> {
    try {
      const { query, maxResults } = options;
      
      // DuckDuckGo Instant Answer API
      const url = `${this.baseUrl}?q=${encodeURIComponent(query)}&format=json&no_html=1&skip_disambig=1`;
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`DuckDuckGo API error: ${response.status}`);
      }

      const data = await response.json();
      const results: WebSearchResult[] = [];

      // 处理即时答案
      if (data.Abstract) {
        results.push({
          title: data.Heading || 'DuckDuckGo Instant Answer',
          url: data.AbstractURL || '',
          snippet: data.Abstract,
          score: 1.0
        });
      }

      // 处理相关主题
      if (data.RelatedTopics && Array.isArray(data.RelatedTopics)) {
        for (const topic of data.RelatedTopics.slice(0, maxResults - results.length)) {
          if (topic.Text && topic.FirstURL) {
            results.push({
              title: topic.Text.split(' - ')[0] || 'Related Topic',
              url: topic.FirstURL,
              snippet: topic.Text,
              score: 0.8
            });
          }
        }
      }

      return results.slice(0, maxResults);
    } catch (error) {
      console.error('DuckDuckGo search error:', error);
      throw error;
    }
  }

  isConfigured(): boolean {
    return true; // DuckDuckGo不需要API密钥
  }

  configure(config: Record<string, any>): void {
    // DuckDuckGo不需要配置
  }
}

/**
 * Google Custom Search引擎实现
 */
export class GoogleSearchEngine implements SearchEngine {
  private apiKey: string = '';
  private searchEngineId: string = '';
  private baseUrl = 'https://www.googleapis.com/customsearch/v1';

  async search(options: SearchOptions): Promise<WebSearchResult[]> {
    if (!this.isConfigured()) {
      throw new Error('Google Search API not configured');
    }

    try {
      const { query, maxResults, language, safeSearch } = options;
      
      const params = new URLSearchParams({
        key: this.apiKey,
        cx: this.searchEngineId,
        q: query,
        num: Math.min(maxResults, 10).toString(),
        hl: language,
        safe: safeSearch ? 'active' : 'off'
      });

      const url = `${this.baseUrl}?${params}`;
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`Google Search API error: ${response.status}`);
      }

      const data = await response.json();
      const results: WebSearchResult[] = [];

      if (data.items && Array.isArray(data.items)) {
        for (const item of data.items) {
          results.push({
            title: item.title || '',
            url: item.link || '',
            snippet: item.snippet || '',
            score: 0.9
          });
        }
      }

      return results;
    } catch (error) {
      console.error('Google search error:', error);
      throw error;
    }
  }

  isConfigured(): boolean {
    return !!(this.apiKey && this.searchEngineId);
  }

  configure(config: Record<string, any>): void {
    this.apiKey = config.apiKey || '';
    this.searchEngineId = config.searchEngineId || '';
  }
}

/**
 * Bing搜索引擎实现
 */
export class BingSearchEngine implements SearchEngine {
  private apiKey: string = '';
  private baseUrl = 'https://api.bing.microsoft.com/v7.0/search';

  async search(options: SearchOptions): Promise<WebSearchResult[]> {
    if (!this.isConfigured()) {
      throw new Error('Bing Search API not configured');
    }

    try {
      const { query, maxResults, language, safeSearch } = options;
      
      const params = new URLSearchParams({
        q: query,
        count: Math.min(maxResults, 50).toString(),
        mkt: language,
        safeSearch: safeSearch ? 'Strict' : 'Off'
      });

      const url = `${this.baseUrl}?${params}`;
      const response = await fetch(url, {
        headers: {
          'Ocp-Apim-Subscription-Key': this.apiKey
        }
      });
      
      if (!response.ok) {
        throw new Error(`Bing Search API error: ${response.status}`);
      }

      const data = await response.json();
      const results: WebSearchResult[] = [];

      if (data.webPages && data.webPages.value) {
        for (const item of data.webPages.value) {
          results.push({
            title: item.name || '',
            url: item.url || '',
            snippet: item.snippet || '',
            score: 0.85
          });
        }
      }

      return results;
    } catch (error) {
      console.error('Bing search error:', error);
      throw error;
    }
  }

  isConfigured(): boolean {
    return !!this.apiKey;
  }

  configure(config: Record<string, any>): void {
    this.apiKey = config.apiKey || '';
  }
}
