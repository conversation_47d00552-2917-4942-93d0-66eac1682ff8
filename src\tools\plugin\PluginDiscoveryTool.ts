import { App, Plugin } from 'obsidian';
import { BaseTool, ToolConfig } from '@/core/tools/BaseTool';
import { ToolResult, ToolExecutionContext, PluginInfo } from '@/types/tools';

/**
 * 插件发现工具
 * 用于发现和列出已安装的Obsidian插件
 */
export class PluginDiscoveryTool extends BaseTool {
  private app: App;

  constructor(app: App) {
    const config: ToolConfig = {
      name: 'plugin_discovery',
      description: '发现和列出已安装的Obsidian插件',
      category: 'plugin',
      version: '1.0.0',
      parameters: {
        type: 'object',
        properties: {
          includeDisabled: {
            type: 'boolean',
            description: '是否包含已禁用的插件',
            default: false
          },
          filterByName: {
            type: 'string',
            description: '按插件名称过滤（支持部分匹配）',
          },
          sortBy: {
            type: 'string',
            description: '排序方式',
            enum: ['name', 'id', 'enabled', 'version'],
            default: 'name'
          }
        },
        required: []
      },
      permissions: {
        required: ['plugin_read'],
        optional: [],
        dangerous: false,
        requiresConfirmation: false
      },
      metadata: {
        author: 'AI Coach Team',
        tags: ['plugin', 'discovery', 'management'],
        documentation: '用于发现和列出Obsidian中已安装的插件',
        examples: [
          {
            name: '列出所有启用的插件',
            description: '获取所有当前启用的插件列表',
            input: {},
            expectedOutput: { plugins: [] }
          },
          {
            name: '搜索特定插件',
            description: '搜索名称包含特定关键词的插件',
            input: { filterByName: 'calendar' },
            expectedOutput: { plugins: [] }
          }
        ]
      }
    };

    super(config);
    this.app = app;
  }

  protected async executeInternal(
    args: Record<string, any>,
    context?: ToolExecutionContext
  ): Promise<ToolResult> {
    try {
      const {
        includeDisabled = false,
        filterByName = '',
        sortBy = 'name'
      } = args;

      // 获取插件管理器
      const pluginManager = this.app.plugins;
      const plugins: PluginInfo[] = [];

      // 遍历已安装的插件
      for (const [pluginId, plugin] of Object.entries(pluginManager.plugins)) {
        const manifest = plugin.manifest;
        const isEnabled = pluginManager.enabledPlugins.has(pluginId);

        // 过滤禁用的插件
        if (!includeDisabled && !isEnabled) {
          continue;
        }

        // 按名称过滤
        if (filterByName && !manifest.name.toLowerCase().includes(filterByName.toLowerCase())) {
          continue;
        }

        const pluginInfo: PluginInfo = {
          id: pluginId,
          name: manifest.name,
          version: manifest.version,
          enabled: isEnabled,
          description: manifest.description,
          author: manifest.author,
          authorUrl: manifest.authorUrl,
          minAppVersion: manifest.minAppVersion,
          commands: this.getPluginCommands(pluginId)
        };

        plugins.push(pluginInfo);
      }

      // 排序
      this.sortPlugins(plugins, sortBy);

      return {
        success: true,
        data: {
          plugins,
          totalCount: plugins.length,
          enabledCount: plugins.filter(p => p.enabled).length,
          disabledCount: plugins.filter(p => !p.enabled).length
        },
        metadata: {
          source: 'obsidian_plugin_manager',
          timestamp: new Date().toISOString()
        }
      };

    } catch (error) {
      return {
        success: false,
        error: `插件发现失败: ${error.message}`,
        metadata: {
          errorType: 'plugin_discovery_error'
        }
      };
    }
  }

  /**
   * 获取插件的命令列表
   */
  private getPluginCommands(pluginId: string): Array<{ id: string; name: string }> {
    const commands: Array<{ id: string; name: string }> = [];
    
    try {
      // 获取应用的命令管理器
      const commandManager = this.app.commands;
      
      // 遍历所有命令，找到属于该插件的命令
      for (const [commandId, command] of Object.entries(commandManager.commands)) {
        // 检查命令ID是否以插件ID开头（Obsidian的命令命名约定）
        if (commandId.startsWith(`${pluginId}:`)) {
          commands.push({
            id: commandId,
            name: command.name || commandId
          });
        }
      }
    } catch (error) {
      console.warn(`Failed to get commands for plugin ${pluginId}:`, error);
    }

    return commands;
  }

  /**
   * 排序插件列表
   */
  private sortPlugins(plugins: PluginInfo[], sortBy: string): void {
    plugins.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'id':
          return a.id.localeCompare(b.id);
        case 'enabled':
          return Number(b.enabled) - Number(a.enabled);
        case 'version':
          return this.compareVersions(a.version, b.version);
        default:
          return a.name.localeCompare(b.name);
      }
    });
  }

  /**
   * 比较版本号
   */
  private compareVersions(version1: string, version2: string): number {
    const v1Parts = version1.split('.').map(Number);
    const v2Parts = version2.split('.').map(Number);
    
    const maxLength = Math.max(v1Parts.length, v2Parts.length);
    
    for (let i = 0; i < maxLength; i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;
      
      if (v1Part !== v2Part) {
        return v2Part - v1Part; // 降序排列
      }
    }
    
    return 0;
  }

  /**
   * 获取插件详细信息
   */
  async getPluginDetails(pluginId: string): Promise<PluginInfo | null> {
    try {
      const plugin = this.app.plugins.plugins[pluginId];
      if (!plugin) {
        return null;
      }

      const manifest = plugin.manifest;
      const isEnabled = this.app.plugins.enabledPlugins.has(pluginId);

      return {
        id: pluginId,
        name: manifest.name,
        version: manifest.version,
        enabled: isEnabled,
        description: manifest.description,
        author: manifest.author,
        authorUrl: manifest.authorUrl,
        minAppVersion: manifest.minAppVersion,
        commands: this.getPluginCommands(pluginId)
      };
    } catch (error) {
      console.error(`Failed to get plugin details for ${pluginId}:`, error);
      return null;
    }
  }

  /**
   * 检查插件是否存在
   */
  pluginExists(pluginId: string): boolean {
    return pluginId in this.app.plugins.plugins;
  }

  /**
   * 检查插件是否启用
   */
  isPluginEnabled(pluginId: string): boolean {
    return this.app.plugins.enabledPlugins.has(pluginId);
  }

  /**
   * 获取插件统计信息
   */
  getPluginStats(): {
    totalPlugins: number;
    enabledPlugins: number;
    disabledPlugins: number;
    corePlugins: number;
    communityPlugins: number;
  } {
    const allPlugins = Object.keys(this.app.plugins.plugins);
    const enabledPlugins = Array.from(this.app.plugins.enabledPlugins);
    
    // 核心插件通常以 'obsidian-' 开头或在特定列表中
    const corePluginIds = new Set([
      'file-explorer',
      'global-search',
      'switcher',
      'graph',
      'backlink',
      'canvas',
      'outgoing-link',
      'tag-pane',
      'page-preview',
      'daily-notes',
      'templates',
      'note-composer',
      'command-palette',
      'slash-command',
      'editor-status',
      'starred',
      'markdown-importer',
      'zk-prefixer',
      'random-note',
      'outline',
      'word-count',
      'slides',
      'audio-recorder',
      'workspaces',
      'file-recovery',
      'publish',
      'sync'
    ]);

    const corePlugins = allPlugins.filter(id => corePluginIds.has(id));
    const communityPlugins = allPlugins.filter(id => !corePluginIds.has(id));

    return {
      totalPlugins: allPlugins.length,
      enabledPlugins: enabledPlugins.length,
      disabledPlugins: allPlugins.length - enabledPlugins.length,
      corePlugins: corePlugins.length,
      communityPlugins: communityPlugins.length
    };
  }
}
