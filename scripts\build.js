const esbuild = require('esbuild');
const fs = require('fs');
const path = require('path');

const production = process.argv.includes('--production');

async function build() {
  try {
    console.log('🚀 开始构建 AI Coach Advanced...');
    
    // 清理输出目录
    const outDir = 'dist';
    if (fs.existsSync(outDir)) {
      fs.rmSync(outDir, { recursive: true });
    }
    fs.mkdirSync(outDir, { recursive: true });

    // 构建主文件
    await esbuild.build({
      entryPoints: ['src/main.ts'],
      bundle: true,
      external: ['obsidian'],
      format: 'cjs',
      target: 'es2018',
      outfile: `${outDir}/main.js`,
      minify: production,
      sourcemap: !production,
      define: {
        'process.env.NODE_ENV': production ? '"production"' : '"development"'
      },
      plugins: [
        {
          name: 'alias',
          setup(build) {
            build.onResolve({ filter: /^@\// }, args => {
              const relativePath = args.path.slice(2);
              const resolvedPath = path.resolve(__dirname, '../src', relativePath);

              // 尝试添加.ts扩展名
              if (fs.existsSync(resolvedPath + '.ts')) {
                return { path: resolvedPath + '.ts' };
              }

              // 尝试添加/index.ts
              if (fs.existsSync(path.join(resolvedPath, 'index.ts'))) {
                return { path: path.join(resolvedPath, 'index.ts') };
              }

              return { path: resolvedPath };
            });
          }
        }
      ]
    });

    // 复制必要文件
    const filesToCopy = [
      { src: 'manifest.json', dest: `${outDir}/manifest.json` },
      { src: 'styles.css', dest: `${outDir}/styles.css` },
      { src: 'README.md', dest: `${outDir}/README.md` }
    ];

    for (const file of filesToCopy) {
      if (fs.existsSync(file.src)) {
        fs.copyFileSync(file.src, file.dest);
        console.log(`📄 复制文件: ${file.src} -> ${file.dest}`);
      }
    }

    // 生成版本信息
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const versionInfo = {
      version: packageJson.version,
      buildTime: new Date().toISOString(),
      production
    };
    
    fs.writeFileSync(
      `${outDir}/version.json`, 
      JSON.stringify(versionInfo, null, 2)
    );

    console.log('✅ 构建完成!');
    console.log(`📦 输出目录: ${outDir}`);
    console.log(`🏷️  版本: ${packageJson.version}`);
    console.log(`🔧 模式: ${production ? 'production' : 'development'}`);

  } catch (error) {
    console.error('❌ 构建失败:', error);
    process.exit(1);
  }
}

build();
