import { ConfigManager } from '../../../src/core/config/ConfigManager';
import { DEFAULT_CONFIG } from '../../../src/types/config';
import { mockApp, mockPlugin } from '../../setup';

describe('ConfigManager', () => {
  let configManager: ConfigManager;

  beforeEach(() => {
    configManager = new ConfigManager(mockApp as any, mockPlugin as any);
  });

  describe('loadConfig', () => {
    it('should load default config when no saved config exists', async () => {
      mockPlugin.loadData.mockResolvedValue(null);

      const config = await configManager.loadConfig();

      expect(config).toEqual(DEFAULT_CONFIG);
      expect(mockPlugin.loadData).toHaveBeenCalled();
    });

    it('should merge saved config with default config', async () => {
      const savedConfig = {
        llm: {
          provider: 'openai',
          apiKey: 'test-key'
        }
      };
      mockPlugin.loadData.mockResolvedValue(savedConfig);

      const config = await configManager.loadConfig();

      expect(config.llm.provider).toBe('openai');
      expect(config.llm.apiKey).toBe('test-key');
      expect(config.tools).toEqual(DEFAULT_CONFIG.tools);
    });

    it('should handle loading errors gracefully', async () => {
      mockPlugin.loadData.mockRejectedValue(new Error('Load failed'));

      const config = await configManager.loadConfig();

      expect(config).toEqual(DEFAULT_CONFIG);
    });
  });

  describe('saveConfig', () => {
    it('should save config successfully', async () => {
      mockPlugin.saveData.mockResolvedValue(undefined);

      await configManager.saveConfig();

      expect(mockPlugin.saveData).toHaveBeenCalled();
    });

    it('should handle save errors', async () => {
      mockPlugin.saveData.mockRejectedValue(new Error('Save failed'));

      await expect(configManager.saveConfig()).rejects.toThrow('Save failed');
    });
  });

  describe('updateLLMConfig', () => {
    it('should update LLM configuration', async () => {
      const newLLMConfig = {
        provider: 'google' as const,
        apiKey: 'new-key',
        model: 'gemini-pro'
      };

      await configManager.updateLLMConfig(newLLMConfig);
      const config = configManager.getLLMConfig();

      expect(config.provider).toBe('google');
      expect(config.apiKey).toBe('new-key');
      expect(config.model).toBe('gemini-pro');
    });
  });

  describe('updateToolConfig', () => {
    it('should update tool configuration', async () => {
      const newToolConfig = {
        vault: {
          indexingEnabled: false,
          embeddingModel: 'text-embedding-3-small'
        }
      };

      await configManager.updateToolConfig(newToolConfig);
      const config = configManager.getToolConfig();

      expect(config.vault.indexingEnabled).toBe(false);
      expect(config.vault.embeddingModel).toBe('text-embedding-3-small');
    });
  });

  describe('validateConfig', () => {
    it('should validate valid config', () => {
      const validConfig = { ...DEFAULT_CONFIG };
      
      const result = configManager.validateConfig(validConfig);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect invalid LLM provider', () => {
      const invalidConfig = {
        ...DEFAULT_CONFIG,
        llm: {
          ...DEFAULT_CONFIG.llm,
          provider: 'invalid' as any
        }
      };
      
      const result = configManager.validateConfig(invalidConfig);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Invalid LLM provider: invalid');
    });

    it('should detect missing API key', () => {
      const invalidConfig = {
        ...DEFAULT_CONFIG,
        llm: {
          ...DEFAULT_CONFIG.llm,
          provider: 'openai' as const,
          apiKey: ''
        }
      };
      
      const result = configManager.validateConfig(invalidConfig);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('API key is required for openai provider');
    });
  });

  describe('resetToDefaults', () => {
    it('should reset configuration to defaults', async () => {
      // First modify the config
      await configManager.updateLLMConfig({
        provider: 'google',
        apiKey: 'test-key'
      });

      // Then reset
      await configManager.resetToDefaults();
      const config = configManager.getLLMConfig();

      expect(config).toEqual(DEFAULT_CONFIG.llm);
    });
  });

  describe('exportConfig', () => {
    it('should export configuration without sensitive data', () => {
      const exported = configManager.exportConfig();

      expect(exported.llm.apiKey).toBe('[REDACTED]');
      expect(exported.llm.provider).toBe(DEFAULT_CONFIG.llm.provider);
    });
  });

  describe('importConfig', () => {
    it('should import valid configuration', async () => {
      const importConfig = {
        llm: {
          provider: 'openai' as const,
          model: 'gpt-4',
          temperature: 0.8
        },
        tools: {
          vault: {
            indexingEnabled: false
          }
        }
      };

      const result = await configManager.importConfig(importConfig);

      expect(result.success).toBe(true);
      expect(configManager.getLLMConfig().model).toBe('gpt-4');
      expect(configManager.getToolConfig().vault.indexingEnabled).toBe(false);
    });

    it('should reject invalid configuration', async () => {
      const invalidConfig = {
        llm: {
          provider: 'invalid'
        }
      };

      const result = await configManager.importConfig(invalidConfig);

      expect(result.success).toBe(false);
      expect(result.errors).toBeDefined();
    });
  });
});
