{"name": "archiver", "version": "6.0.2", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 12.0.0"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^4.0.1", "async": "^3.2.4", "buffer-crc32": "^0.2.1", "readable-stream": "^3.6.0", "readdir-glob": "^1.1.2", "tar-stream": "^3.0.0", "zip-stream": "^5.0.1"}, "devDependencies": {"archiver-jsdoc-theme": "1.1.3", "chai": "4.4.1", "jsdoc": "4.0.2", "mkdirp": "3.0.1", "mocha": "9.2.2", "rimraf": "4.4.1", "stream-bench": "0.1.2", "tar": "6.2.0", "yauzl": "2.10.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}}