import { OrchestrationEngine } from '../../src/core/orchestration/OrchestrationEngine';
import { ConfigManager } from '../../src/core/config/ConfigManager';
import { mockApp, mockPlugin } from '../setup';

// Mock tools for integration testing
class MockVaultTool {
  name = 'vault_query';
  description = 'Query vault content';
  
  async execute(args: any) {
    return {
      success: true,
      data: {
        results: [
          {
            file: 'test.md',
            content: 'Test content',
            score: 0.9
          }
        ]
      }
    };
  }
}

class MockWebTool {
  name = 'web_search';
  description = 'Search the web';
  
  async execute(args: any) {
    return {
      success: true,
      data: {
        results: [
          {
            title: 'Test Result',
            url: 'https://example.com',
            snippet: 'Test snippet'
          }
        ]
      }
    };
  }
}

describe('OrchestrationEngine Integration Tests', () => {
  let orchestrationEngine: OrchestrationEngine;
  let configManager: ConfigManager;

  beforeEach(async () => {
    // Setup config manager with test configuration
    configManager = new ConfigManager(mockApp as any, mockPlugin as any);
    
    // Mock config loading
    mockPlugin.loadData.mockResolvedValue({
      llm: {
        provider: 'openai',
        apiKey: 'test-key',
        model: 'gpt-3.5-turbo'
      }
    });

    orchestrationEngine = new OrchestrationEngine(mockApp as any, configManager);
    
    // Register mock tools
    await orchestrationEngine.registerTool(new MockVaultTool());
    await orchestrationEngine.registerTool(new MockWebTool());
  });

  describe('End-to-End Workflow', () => {
    it('should handle simple query workflow', async () => {
      // Mock LLM responses for a simple query
      global.fetch = jest.fn()
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: 'This is a simple query that can be answered directly.'
              }
            }],
            usage: { prompt_tokens: 10, completion_tokens: 20, total_tokens: 30 }
          })
        });

      const result = await orchestrationEngine.processUserInput('What is the weather today?');

      expect(result.success).toBe(true);
      expect(result.response).toBeDefined();
      expect(result.conversationId).toBeDefined();
    });

    it('should handle vault search workflow', async () => {
      // Mock LLM responses for vault search
      global.fetch = jest.fn()
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: 'I need to search the vault for information about this topic.'
              }
            }],
            usage: { prompt_tokens: 10, completion_tokens: 20, total_tokens: 30 }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: JSON.stringify({
                  type: 'tool_call',
                  toolName: 'vault_query',
                  parameters: { query: 'test topic' }
                })
              }
            }],
            usage: { prompt_tokens: 15, completion_tokens: 25, total_tokens: 40 }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: JSON.stringify({
                  type: 'final_answer',
                  answer: 'Based on the vault search, I found relevant information about the topic.'
                })
              }
            }],
            usage: { prompt_tokens: 20, completion_tokens: 30, total_tokens: 50 }
          })
        });

      const result = await orchestrationEngine.processUserInput('Search my notes for information about AI');

      expect(result.success).toBe(true);
      expect(result.toolsUsed).toContain('vault_query');
      expect(result.response).toContain('vault search');
    });

    it('should handle multi-tool workflow', async () => {
      // Mock LLM responses for multi-tool workflow
      global.fetch = jest.fn()
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: 'I need to search both the vault and the web for comprehensive information.'
              }
            }],
            usage: { prompt_tokens: 10, completion_tokens: 20, total_tokens: 30 }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: JSON.stringify({
                  type: 'tool_call',
                  toolName: 'vault_query',
                  parameters: { query: 'research topic' }
                })
              }
            }],
            usage: { prompt_tokens: 15, completion_tokens: 25, total_tokens: 40 }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: 'Now I need to search the web for additional information.'
              }
            }],
            usage: { prompt_tokens: 20, completion_tokens: 30, total_tokens: 50 }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: JSON.stringify({
                  type: 'tool_call',
                  toolName: 'web_search',
                  parameters: { query: 'latest research' }
                })
              }
            }],
            usage: { prompt_tokens: 25, completion_tokens: 35, total_tokens: 60 }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: JSON.stringify({
                  type: 'final_answer',
                  answer: 'Based on both vault and web search, here is the comprehensive information.'
                })
              }
            }],
            usage: { prompt_tokens: 30, completion_tokens: 40, total_tokens: 70 }
          })
        });

      const result = await orchestrationEngine.processUserInput('Research the latest developments in AI and compare with my notes');

      expect(result.success).toBe(true);
      expect(result.toolsUsed).toContain('vault_query');
      expect(result.toolsUsed).toContain('web_search');
      expect(result.response).toContain('comprehensive information');
    });

    it('should handle conversation context', async () => {
      // Start a conversation
      await orchestrationEngine.startNewConversation('Test Conversation');

      // First message
      global.fetch = jest.fn()
        .mockResolvedValue({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: 'Hello! I understand you want to discuss AI topics.'
              }
            }],
            usage: { prompt_tokens: 10, completion_tokens: 20, total_tokens: 30 }
          })
        });

      const result1 = await orchestrationEngine.processUserInput('Tell me about AI');
      expect(result1.success).toBe(true);

      // Second message with context
      global.fetch = jest.fn()
        .mockResolvedValue({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: 'Based on our previous discussion about AI, here are more specific details about machine learning.'
              }
            }],
            usage: { prompt_tokens: 15, completion_tokens: 25, total_tokens: 40 }
          })
        });

      const result2 = await orchestrationEngine.processUserInput('Can you be more specific about machine learning?');
      expect(result2.success).toBe(true);
      expect(result2.response).toContain('previous discussion');
    });

    it('should handle errors gracefully', async () => {
      // Mock LLM error
      global.fetch = jest.fn()
        .mockRejectedValue(new Error('Network error'));

      const result = await orchestrationEngine.processUserInput('Test query');

      expect(result.success).toBe(false);
      expect(result.error).toContain('error');
    });
  });

  describe('Performance Tests', () => {
    it('should complete simple queries within reasonable time', async () => {
      global.fetch = jest.fn()
        .mockResolvedValue({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: 'Quick response'
              }
            }],
            usage: { prompt_tokens: 5, completion_tokens: 10, total_tokens: 15 }
          })
        });

      const startTime = Date.now();
      const result = await orchestrationEngine.processUserInput('Quick test');
      const endTime = Date.now();

      expect(result.success).toBe(true);
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle concurrent requests', async () => {
      global.fetch = jest.fn()
        .mockResolvedValue({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: 'Concurrent response'
              }
            }],
            usage: { prompt_tokens: 5, completion_tokens: 10, total_tokens: 15 }
          })
        });

      const promises = [
        orchestrationEngine.processUserInput('Query 1'),
        orchestrationEngine.processUserInput('Query 2'),
        orchestrationEngine.processUserInput('Query 3')
      ];

      const results = await Promise.all(promises);

      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });
  });

  describe('Memory and State Management', () => {
    it('should maintain conversation state across multiple interactions', async () => {
      const conversationId = await orchestrationEngine.startNewConversation();
      
      global.fetch = jest.fn()
        .mockResolvedValue({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: 'Response with memory'
              }
            }],
            usage: { prompt_tokens: 10, completion_tokens: 20, total_tokens: 30 }
          })
        });

      // Multiple interactions
      await orchestrationEngine.processUserInput('First message');
      await orchestrationEngine.processUserInput('Second message');
      const result = await orchestrationEngine.processUserInput('Third message');

      expect(result.conversationId).toBe(conversationId);
      
      const stats = await orchestrationEngine.getExecutionStats();
      expect(stats.conversations.currentConversationLength).toBeGreaterThan(0);
    });

    it('should clean up resources properly', async () => {
      await orchestrationEngine.startNewConversation();
      
      // Process some messages
      global.fetch = jest.fn()
        .mockResolvedValue({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: 'Test response'
              }
            }],
            usage: { prompt_tokens: 10, completion_tokens: 20, total_tokens: 30 }
          })
        });

      await orchestrationEngine.processUserInput('Test message');
      
      // Cleanup
      await orchestrationEngine.cleanup();
      
      // Should handle cleanup gracefully
      expect(orchestrationEngine.getConversationState()).toBe('ended');
    });
  });
});
