import { HTMLElement, Notice } from 'obsidian';
import { OrchestrationEngine } from '@/core/orchestration/OrchestrationEngine';

/**
 * 状态栏管理器
 * 负责在Obsidian状态栏中显示插件状态和进度信息
 */
export class StatusBarManager {
  private statusBarItem: HTMLElement;
  private orchestrationEngine: OrchestrationEngine;
  private updateInterval: number | null = null;
  private isActive: boolean = false;

  constructor(statusBarItem: HTMLElement, orchestrationEngine: OrchestrationEngine) {
    this.statusBarItem = statusBarItem;
    this.orchestrationEngine = orchestrationEngine;
  }

  /**
   * 初始化状态栏
   */
  initialize(): void {
    this.setupStatusBar();
    this.startPeriodicUpdate();
  }

  /**
   * 设置状态栏
   */
  private setupStatusBar(): void {
    this.statusBarItem.addClass('ai-coach-status-bar');
    this.statusBarItem.style.cursor = 'pointer';
    
    // 添加点击事件
    this.statusBarItem.addEventListener('click', () => {
      this.showDetailedStatus();
    });

    // 初始状态
    this.updateStatus('idle', 'AI Coach Ready');
  }

  /**
   * 更新状态
   */
  updateStatus(status: StatusType, message: string, progress?: number): void {
    this.statusBarItem.empty();
    
    // 创建状态图标
    const icon = this.statusBarItem.createSpan({ cls: 'ai-coach-status-icon' });
    icon.innerHTML = this.getStatusIcon(status);
    
    // 创建状态文本
    const text = this.statusBarItem.createSpan({ 
      cls: 'ai-coach-status-text',
      text: message
    });

    // 如果有进度，显示进度条
    if (progress !== undefined) {
      const progressBar = this.statusBarItem.createDiv({ cls: 'ai-coach-progress-bar' });
      const progressFill = progressBar.createDiv({ 
        cls: 'ai-coach-progress-fill',
        attr: { style: `width: ${Math.max(0, Math.min(100, progress))}%` }
      });
    }

    // 设置状态样式
    this.statusBarItem.className = `ai-coach-status-bar ai-coach-status-${status}`;
  }

  /**
   * 获取状态图标
   */
  private getStatusIcon(status: StatusType): string {
    const icons = {
      idle: '🤖',
      processing: '⚡',
      success: '✅',
      error: '❌',
      warning: '⚠️'
    };
    return icons[status] || '🤖';
  }

  /**
   * 显示处理状态
   */
  showProcessing(message: string = '处理中...', progress?: number): void {
    this.isActive = true;
    this.updateStatus('processing', message, progress);
  }

  /**
   * 显示成功状态
   */
  showSuccess(message: string = '完成', duration: number = 3000): void {
    this.updateStatus('success', message);
    this.isActive = false;
    
    // 自动恢复到空闲状态
    setTimeout(() => {
      if (!this.isActive) {
        this.showIdle();
      }
    }, duration);
  }

  /**
   * 显示错误状态
   */
  showError(message: string = '错误', duration: number = 5000): void {
    this.updateStatus('error', message);
    this.isActive = false;
    
    // 自动恢复到空闲状态
    setTimeout(() => {
      if (!this.isActive) {
        this.showIdle();
      }
    }, duration);
  }

  /**
   * 显示警告状态
   */
  showWarning(message: string = '警告', duration: number = 4000): void {
    this.updateStatus('warning', message);
    
    // 自动恢复到空闲状态
    setTimeout(() => {
      if (!this.isActive) {
        this.showIdle();
      }
    }, duration);
  }

  /**
   * 显示空闲状态
   */
  showIdle(): void {
    this.isActive = false;
    this.updateStatus('idle', 'AI Coach Ready');
  }

  /**
   * 开始周期性更新
   */
  private startPeriodicUpdate(): void {
    // 每30秒更新一次状态信息
    this.updateInterval = window.setInterval(async () => {
      if (!this.isActive) {
        await this.updateIdleStatus();
      }
    }, 30000);
  }

  /**
   * 更新空闲状态信息
   */
  private async updateIdleStatus(): Promise<void> {
    try {
      const stats = await this.orchestrationEngine.getExecutionStats();
      const conversationState = this.orchestrationEngine.getConversationState();
      
      let message = 'AI Coach Ready';
      
      if (conversationState === 'active') {
        message = `对话中 (${stats.conversations.currentConversationLength} 消息)`;
      } else if (stats.conversations.totalConversations > 0) {
        message = `就绪 (${stats.conversations.totalConversations} 对话)`;
      }
      
      this.updateStatus('idle', message);
    } catch (error) {
      console.warn('Failed to update idle status:', error);
    }
  }

  /**
   * 显示详细状态
   */
  private async showDetailedStatus(): Promise<void> {
    try {
      const stats = await this.orchestrationEngine.getExecutionStats();
      
      const statusInfo = `
AI Coach Advanced 状态:

对话统计:
- 总对话数: ${stats.conversations.totalConversations}
- 总消息数: ${stats.conversations.totalMessages}
- 当前对话长度: ${stats.conversations.currentConversationLength}
- 对话状态: ${this.getConversationStateText(stats.conversations.conversationState)}

工具统计:
- 可用工具数: ${stats.tools.totalTools || 0}
- 工具调用次数: ${stats.coordination?.totalExecutions || 0}
- 成功率: ${stats.coordination ? Math.round((stats.coordination.successfulExecutions / stats.coordination.totalExecutions) * 100) : 0}%

LLM状态:
- 提供商: ${stats.llm.provider}
- 状态: ${stats.llm.initialized ? '已连接' : '未连接'}
      `.trim();

      // 创建临时通知显示详细信息
      const notice = new Notice(statusInfo, 8000);
      
    } catch (error) {
      new Notice(`获取状态信息失败: ${error.message}`, 3000);
    }
  }

  /**
   * 获取对话状态文本
   */
  private getConversationStateText(state: string): string {
    const stateTexts = {
      idle: '空闲',
      active: '活跃',
      processing: '处理中',
      waiting: '等待中',
      ended: '已结束',
      error: '错误'
    };
    return stateTexts[state as keyof typeof stateTexts] || state;
  }

  /**
   * 显示工具执行进度
   */
  showToolProgress(toolName: string, step: number, totalSteps: number): void {
    const progress = (step / totalSteps) * 100;
    const message = `执行 ${toolName} (${step}/${totalSteps})`;
    this.showProcessing(message, progress);
  }

  /**
   * 显示LLM思考状态
   */
  showThinking(iteration: number = 1): void {
    const dots = '.'.repeat((iteration % 3) + 1);
    this.showProcessing(`AI思考中${dots}`);
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    
    this.statusBarItem.empty();
    this.statusBarItem.removeEventListener('click', this.showDetailedStatus);
  }
}

/**
 * 状态类型
 */
export type StatusType = 'idle' | 'processing' | 'success' | 'error' | 'warning';

// 添加CSS样式（这些样式应该在插件的CSS文件中定义）
const CSS_STYLES = `
.ai-coach-status-bar {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  transition: all 0.2s ease;
}

.ai-coach-status-bar:hover {
  background-color: var(--background-modifier-hover);
}

.ai-coach-status-icon {
  font-size: 14px;
}

.ai-coach-status-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.ai-coach-progress-bar {
  width: 60px;
  height: 3px;
  background-color: var(--background-modifier-border);
  border-radius: 2px;
  overflow: hidden;
}

.ai-coach-progress-fill {
  height: 100%;
  background-color: var(--interactive-accent);
  transition: width 0.3s ease;
}

.ai-coach-status-processing {
  color: var(--text-accent);
}

.ai-coach-status-success {
  color: var(--text-success);
}

.ai-coach-status-error {
  color: var(--text-error);
}

.ai-coach-status-warning {
  color: var(--text-warning);
}
`;
