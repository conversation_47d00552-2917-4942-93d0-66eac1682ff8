/**
 * 测试环境设置
 */

// Mock Obsidian API
global.require = jest.fn();

// Mock App
export const mockApp = {
  vault: {
    getMarkdownFiles: jest.fn(() => []),
    read: jest.fn(),
    adapter: {
      exists: jest.fn(),
      read: jest.fn(),
      write: jest.fn(),
      mkdir: jest.fn(),
      stat: jest.fn()
    },
    on: jest.fn()
  },
  metadataCache: {
    getFileCache: jest.fn()
  }
};

// Mock Plugin
export const mockPlugin = {
  app: mockApp,
  addCommand: jest.fn(),
  addSettingTab: jest.fn(),
  addStatusBarItem: jest.fn(() => ({
    createSpan: jest.fn(() => ({ innerHTML: '' })),
    createDiv: jest.fn(() => ({ createDiv: jest.fn() })),
    empty: jest.fn(),
    addClass: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn()
  })),
  registerEvent: jest.fn(),
  loadData: jest.fn(),
  saveData: jest.fn()
};

// Mock TFile
export const mockTFile = {
  path: 'test.md',
  name: 'test.md',
  extension: 'md',
  stat: {
    size: 1000,
    mtime: Date.now()
  }
};

// Mock Notice
global.Notice = jest.fn();

// Setup test environment
beforeEach(() => {
  jest.clearAllMocks();
});
