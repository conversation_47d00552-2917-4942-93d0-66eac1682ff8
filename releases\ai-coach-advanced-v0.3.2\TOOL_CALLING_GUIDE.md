# AI Coach Advanced - 工具调用指南

## 🎯 概述

AI Coach Advanced 使用 ReAct（推理和行动）模式来智能地选择和调用工具。本指南将帮助你理解和测试工具调用功能。

## 🛠️ 可用工具

### 1. Vault查询工具 (vault_query)
**用途**: 搜索和查询Obsidian Vault中的笔记内容

**触发关键词**:
- "搜索我的笔记"
- "查找关于...的文档"
- "我写过关于...的内容吗"
- "在我的笔记中找..."

**示例对话**:
```
用户: 帮我搜索关于项目管理的笔记
AI: [调用vault_query工具] → 找到相关笔记并总结内容
```

### 2. 网络搜索工具 (web_search)
**用途**: 在网络上搜索最新信息

**触发关键词**:
- "搜索最新的..."
- "网上查找..."
- "最新消息"
- "实时信息"

**示例对话**:
```
用户: 搜索最新的AI发展趋势
AI: [调用web_search工具] → 获取网络搜索结果并分析
```

### 3. JavaScript执行工具 (javascript_executor)
**用途**: 执行JavaScript代码进行计算和数据处理

**触发关键词**:
- "计算..."
- "帮我算一下..."
- "数学问题"
- "编程"、"代码"

**示例对话**:
```
用户: 帮我计算1到100的平方和
AI: [调用javascript_executor工具] → 生成并执行计算代码
```

### 4. 插件管理工具 (plugin_manager)
**用途**: 管理Obsidian插件和执行命令

**触发关键词**:
- "创建日记"
- "插件"
- "执行命令"
- "Obsidian功能"

**示例对话**:
```
用户: 帮我创建今天的日记页面
AI: [调用plugin_manager工具] → 执行Daily Notes插件命令
```

## 🧪 测试工具调用

### 基本测试用例

#### 1. 测试Vault查询
```
输入: "搜索我关于学习方法的笔记"
期望: 调用vault_query工具，搜索相关笔记
```

#### 2. 测试计算功能
```
输入: "计算斐波那契数列的前10项"
期望: 调用javascript_executor工具，生成并执行代码
```

#### 3. 测试网络搜索
```
输入: "查找今天的天气预报"
期望: 调用web_search工具，搜索天气信息
```

#### 4. 测试插件功能
```
输入: "显示我安装了哪些插件"
期望: 调用plugin_manager工具，列出已安装插件
```

### 高级测试用例

#### 1. 多工具协作
```
输入: "基于我的学习笔记，搜索相关的在线课程"
期望: 
1. 先调用vault_query搜索学习笔记
2. 再调用web_search搜索在线课程
3. 综合分析并提供建议
```

#### 2. 复杂计算
```
输入: "分析我笔记中提到的数据，计算平均值和标准差"
期望:
1. 调用vault_query提取数据
2. 调用javascript_executor进行统计计算
```

## 🔍 故障排除

### 问题1: AI不调用工具，只是对话
**可能原因**:
- LLM API密钥未配置或无效
- 提示词不够明确
- 工具未正确注册

**解决方案**:
1. 检查API密钥配置
2. 使用更明确的关键词
3. 重启插件

**测试命令**:
```
明确的测试: "使用vault_query工具搜索我的笔记"
```

### 问题2: 调用了错误的工具
**可能原因**:
- 用户请求不够明确
- LLM理解有误

**解决方案**:
1. 使用更具体的描述
2. 明确指定工具名称

**示例**:
```
不明确: "帮我找一些信息"
明确: "在我的笔记中搜索关于Python的内容"
```

### 问题3: 工具执行失败
**可能原因**:
- 工具配置错误
- 权限不足
- 网络问题

**解决方案**:
1. 检查工具配置
2. 查看控制台错误信息
3. 重新配置相关设置

## 📊 工具调用流程

```mermaid
graph TD
    A[用户输入] --> B[任务分析]
    B --> C{是否可执行?}
    C -->|否| D[返回说明]
    C -->|是| E[ReAct循环开始]
    E --> F[思考阶段]
    F --> G[行动规划]
    G --> H{行动类型?}
    H -->|工具调用| I[执行工具]
    H -->|最终答案| J[生成回答]
    H -->|需要更多信息| K[询问用户]
    I --> L[处理工具结果]
    L --> M{是否继续?}
    M -->|是| F
    M -->|否| J
    J --> N[返回结果]
    K --> N
    D --> N
```

## 🎯 最佳实践

### 1. 明确的请求
- ✅ "计算1+1等于多少"
- ❌ "帮我算点东西"

### 2. 具体的上下文
- ✅ "搜索我关于机器学习的笔记"
- ❌ "找一些学习资料"

### 3. 合理的期望
- ✅ 简单的计算和搜索
- ❌ 复杂的多步骤操作

### 4. 耐心等待
- 工具调用需要时间
- 复杂任务可能需要多次迭代

## 🔧 开发者调试

### 启用调试模式
在浏览器控制台中执行：
```javascript
app.plugins.plugins['ai-coach-advanced'].enableDebug();
```

### 查看执行日志
```javascript
// 获取最近的执行统计
const stats = await app.plugins.plugins['ai-coach-advanced']
  .getOrchestrationEngine().getExecutionStats();
console.log(stats);
```

### 手动测试工具
```javascript
// 直接测试工具调用
const engine = app.plugins.plugins['ai-coach-advanced'].getOrchestrationEngine();
const tools = engine.getAvailableTools();
console.log('可用工具:', tools.map(t => t.name));
```

## 📝 反馈和改进

如果工具调用功能不符合预期，请：

1. **记录具体情况**:
   - 输入的确切内容
   - 期望的行为
   - 实际的行为

2. **检查配置**:
   - API密钥是否正确
   - 工具是否启用
   - 网络连接是否正常

3. **提供反馈**:
   - 通过GitHub Issues报告问题
   - 包含详细的重现步骤
   - 附上控制台错误信息

## 🚀 高级功能

### 自定义工具提示
你可以通过明确指定工具来强制调用特定工具：

```
"使用javascript_executor工具计算..."
"用vault_query搜索..."
"通过web_search查找..."
```

### 工具链调用
AI可以自动组合多个工具来完成复杂任务：

```
输入: "分析我的项目笔记，然后搜索相关的最佳实践"
流程: vault_query → web_search → 综合分析
```

---

通过遵循这个指南，你应该能够充分利用AI Coach Advanced的工具调用功能，获得更智能、更准确的AI助手体验！
