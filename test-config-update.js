/**
 * 测试配置更新功能
 */

// 模拟 Obsidian API
const mockObsidian = {
  Plugin: class Plugin {
    constructor(app, manifest) {
      this.app = app;
      this.manifest = manifest;
      this.savedData = null;
    }
    
    async loadData() {
      return this.savedData;
    }
    
    async saveData(data) {
      this.savedData = data;
      console.log('✅ 配置已保存:', JSON.stringify(data.llm, null, 2));
    }
    
    addCommand() {}
    addSettingTab() {}
    addStatusBarItem() {
      return {
        createSpan: () => ({ innerHTML: '' }),
        createDiv: () => ({ createDiv: () => ({}) }),
        empty: () => {},
        addClass: () => {},
        addEventListener: () => {},
        removeEventListener: () => {}
      };
    }
    registerEvent() {}
  },
  
  Notice: class Notice {
    constructor(message, timeout) {
      console.log('📢 通知:', message);
    }
  }
};

// 模拟 App
const mockApp = {
  vault: {
    getMarkdownFiles: () => [],
    read: async () => '',
    adapter: {
      exists: async () => true,
      read: async () => '{}',
      write: async () => {},
      mkdir: async () => {},
      stat: async () => ({ mtime: Date.now() })
    },
    on: () => {}
  },
  metadataCache: { getFileCache: () => ({}) },
  plugins: { plugins: {}, enabledPlugins: new Set() },
  commands: { commands: {} }
};

// 模拟插件清单
const mockManifest = {
  id: 'obsidian-ai-coach-advanced',
  name: 'AI Coach Advanced',
  version: '0.2.0'
};

// 设置全局变量
global.require = () => mockObsidian;

async function testConfigUpdate() {
  console.log('🧪 开始测试配置更新功能...\n');
  
  try {
    // 加载构建后的插件代码
    const fs = require('fs');
    const path = require('path');
    
    const pluginCode = fs.readFileSync(path.join(__dirname, 'dist', 'main.js'), 'utf8');
    
    // 创建执行环境
    const vm = require('vm');
    const context = {
      console,
      require: () => mockObsidian,
      module: { exports: {} },
      exports: {},
      global: {},
      Buffer,
      process: { env: { NODE_ENV: 'test' } },
      setTimeout,
      clearTimeout,
      setInterval,
      clearInterval,
      btoa: (str) => Buffer.from(str).toString('base64'),
      atob: (str) => Buffer.from(str, 'base64').toString()
    };
    
    // 执行插件代码
    vm.createContext(context);
    vm.runInContext(pluginCode, context);
    
    const PluginClass = context.module.exports.default || context.module.exports;
    const plugin = new PluginClass(mockApp, mockManifest);
    
    // 初始化插件
    await plugin.onload();
    console.log('✅ 插件初始化成功\n');
    
    // 获取初始配置
    const initialConfig = plugin.getConfig();
    console.log('📋 初始配置:');
    console.log('  - 提供商:', initialConfig.llm.provider);
    console.log('  - API密钥:', initialConfig.llm.apiKey || '(空)');
    console.log('  - 模型:', initialConfig.llm.model);
    console.log('');
    
    // 测试1: 更新API密钥
    console.log('🔧 测试1: 更新API密钥...');
    await plugin.updateConfig({
      llm: {
        apiKey: 'test-api-key-12345'
      }
    });
    
    const configAfterApiKey = plugin.getConfig();
    console.log('✅ API密钥更新成功:', configAfterApiKey.llm.apiKey);
    console.log('');
    
    // 测试2: 更新提供商
    console.log('🔧 测试2: 更新提供商...');
    await plugin.updateConfig({
      llm: {
        provider: 'google',
        model: 'gemini-pro'
      }
    });
    
    const configAfterProvider = plugin.getConfig();
    console.log('✅ 提供商更新成功:', configAfterProvider.llm.provider);
    console.log('✅ 模型更新成功:', configAfterProvider.llm.model);
    console.log('✅ API密钥保持不变:', configAfterProvider.llm.apiKey);
    console.log('');
    
    // 测试3: 更新工具配置
    console.log('🔧 测试3: 更新工具配置...');
    await plugin.updateConfig({
      tools: {
        vault: {
          enabled: false
        },
        web: {
          enabled: true
        }
      }
    });
    
    const configAfterTools = plugin.getConfig();
    console.log('✅ Vault工具状态:', configAfterTools.tools.vault.enabled);
    console.log('✅ Web工具状态:', configAfterTools.tools.web.enabled);
    console.log('✅ LLM配置保持不变:', configAfterTools.llm.provider, configAfterTools.llm.apiKey);
    console.log('');
    
    // 测试4: 验证配置持久化
    console.log('🔧 测试4: 验证配置持久化...');
    const configManager = plugin.getConfigManager();
    const savedConfig = configManager.getConfig();
    
    console.log('✅ 配置管理器中的配置:');
    console.log('  - 提供商:', savedConfig.llm.provider);
    console.log('  - API密钥:', savedConfig.llm.apiKey);
    console.log('  - 模型:', savedConfig.llm.model);
    console.log('  - Vault工具:', savedConfig.tools.vault.enabled);
    console.log('  - Web工具:', savedConfig.tools.web.enabled);
    console.log('');
    
    // 测试5: 验证LLM配置验证
    console.log('🔧 测试5: 验证LLM配置验证...');
    const validation = await configManager.validateLLMConfig();
    console.log('✅ 配置验证结果:', validation.valid ? '通过' : '失败');
    if (!validation.valid) {
      console.log('❌ 验证错误:', validation.errors);
    }
    
    console.log('\n🎉 配置更新测试全部通过！');
    return true;
    
  } catch (error) {
    console.error('\n❌ 配置更新测试失败:');
    console.error('错误类型:', error.constructor.name);
    console.error('错误消息:', error.message);
    console.error('错误堆栈:', error.stack);
    return false;
  }
}

// 运行测试
if (require.main === module) {
  testConfigUpdate().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { testConfigUpdate };
