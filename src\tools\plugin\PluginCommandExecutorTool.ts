import { App, Notice } from 'obsidian';
import { BaseTool, ToolConfig } from '@/core/tools/BaseTool';
import { ToolResult, ToolExecutionContext, ToolValidationResult } from '@/types/tools';

/**
 * 插件命令执行工具
 * 用于安全地执行Obsidian插件命令
 */
export class PluginCommandExecutorTool extends BaseTool {
  private app: App;
  private executionHistory: CommandExecutionRecord[] = [];
  private maxHistorySize: number = 100;

  constructor(app: App) {
    const config: ToolConfig = {
      name: 'plugin_command_executor',
      description: '执行Obsidian插件命令',
      category: 'plugin',
      version: '1.0.0',
      parameters: {
        type: 'object',
        properties: {
          commandId: {
            type: 'string',
            description: '要执行的命令ID'
          },
          confirmExecution: {
            type: 'boolean',
            description: '是否需要用户确认执行',
            default: false
          },
          timeout: {
            type: 'number',
            description: '命令执行超时时间（毫秒）',
            default: 5000,
            minimum: 1000,
            maximum: 30000
          }
        },
        required: ['commandId']
      },
      permissions: {
        required: ['plugin_execute'],
        optional: ['plugin_dangerous'],
        dangerous: true,
        requiresConfirmation: true
      },
      metadata: {
        author: 'AI Coach Team',
        tags: ['plugin', 'command', 'execution'],
        documentation: '安全地执行Obsidian插件命令',
        examples: [
          {
            name: '执行简单命令',
            description: '执行一个不需要参数的插件命令',
            input: { commandId: 'daily-notes:open-today' },
            expectedOutput: { success: true }
          }
        ]
      }
    };

    super(config);
    this.app = app;
  }

  protected async executeInternal(
    args: Record<string, any>,
    context?: ToolExecutionContext
  ): Promise<ToolResult> {
    const startTime = Date.now();
    const { commandId, confirmExecution = false, timeout = 5000 } = args;

    try {
      // 检查命令是否存在
      const command = this.app.commands.commands[commandId];
      if (!command) {
        return {
          success: false,
          error: `命令不存在: ${commandId}`,
          metadata: {
            commandId,
            executionTime: Date.now() - startTime,
            errorType: 'command_not_found'
          }
        };
      }

      // 检查命令是否可执行
      if (!command.callback) {
        return {
          success: false,
          error: `命令不可执行: ${commandId}`,
          metadata: {
            commandId,
            executionTime: Date.now() - startTime,
            errorType: 'command_not_executable'
          }
        };
      }

      // 检查是否需要用户确认
      if (confirmExecution && context && !context.config?.confirmed) {
        return {
          success: false,
          error: '需要用户确认才能执行此命令',
          metadata: {
            commandId,
            commandName: command.name,
            requiresConfirmation: true,
            executionTime: Date.now() - startTime
          }
        };
      }

      // 执行命令
      const executionResult = await this.executeCommand(commandId, timeout);
      
      // 记录执行历史
      this.recordExecution({
        commandId,
        commandName: command.name,
        success: executionResult.success,
        executionTime: Date.now() - startTime,
        timestamp: new Date(),
        error: executionResult.error,
        context: context?.userId || 'unknown'
      });

      return {
        success: executionResult.success,
        data: {
          commandId,
          commandName: command.name,
          executed: true,
          result: executionResult.result
        },
        error: executionResult.error,
        metadata: {
          commandId,
          commandName: command.name,
          executionTime: Date.now() - startTime,
          timestamp: new Date().toISOString()
        }
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      // 记录失败的执行
      this.recordExecution({
        commandId,
        commandName: 'unknown',
        success: false,
        executionTime,
        timestamp: new Date(),
        error: error.message,
        context: context?.userId || 'unknown'
      });

      return {
        success: false,
        error: `命令执行失败: ${error.message}`,
        metadata: {
          commandId,
          executionTime,
          errorType: 'execution_error'
        }
      };
    }
  }

  protected async validateInternal(args: Record<string, any>): Promise<ToolValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const { commandId, timeout } = args;

    // 验证命令ID格式
    if (!commandId || typeof commandId !== 'string') {
      errors.push('命令ID必须是非空字符串');
    } else {
      // 检查命令是否存在
      if (!this.app.commands.commands[commandId]) {
        errors.push(`命令不存在: ${commandId}`);
      } else {
        const command = this.app.commands.commands[commandId];
        
        // 检查命令是否可执行
        if (!command.callback) {
          errors.push(`命令不可执行: ${commandId}`);
        }

        // 检查是否是危险命令
        if (this.isDangerousCommand(commandId)) {
          warnings.push(`这是一个潜在危险的命令: ${commandId}`);
        }
      }
    }

    // 验证超时时间
    if (timeout && (timeout < 1000 || timeout > 30000)) {
      errors.push('超时时间必须在1000-30000毫秒之间');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 执行命令
   */
  private async executeCommand(commandId: string, timeout: number): Promise<{
    success: boolean;
    result?: any;
    error?: string;
  }> {
    return new Promise((resolve) => {
      const timeoutId = setTimeout(() => {
        resolve({
          success: false,
          error: '命令执行超时'
        });
      }, timeout);

      try {
        const command = this.app.commands.commands[commandId];
        
        // 创建一个包装的回调来捕获结果
        const originalCallback = command.callback;
        let callbackResult: any;

        // 执行命令
        if (typeof originalCallback === 'function') {
          callbackResult = originalCallback.call(command);
        }

        clearTimeout(timeoutId);

        // 如果回调返回Promise，等待它完成
        if (callbackResult && typeof callbackResult.then === 'function') {
          callbackResult
            .then((result: any) => {
              resolve({
                success: true,
                result
              });
            })
            .catch((error: any) => {
              resolve({
                success: false,
                error: error.message || '命令执行失败'
              });
            });
        } else {
          resolve({
            success: true,
            result: callbackResult
          });
        }

      } catch (error) {
        clearTimeout(timeoutId);
        resolve({
          success: false,
          error: error.message || '命令执行异常'
        });
      }
    });
  }

  /**
   * 检查是否是危险命令
   */
  private isDangerousCommand(commandId: string): boolean {
    const dangerousPatterns = [
      'delete',
      'remove',
      'clear',
      'reset',
      'destroy',
      'wipe',
      'format',
      'erase'
    ];

    const commandLower = commandId.toLowerCase();
    return dangerousPatterns.some(pattern => commandLower.includes(pattern));
  }

  /**
   * 记录执行历史
   */
  private recordExecution(record: CommandExecutionRecord): void {
    this.executionHistory.push(record);
    
    // 限制历史记录大小
    if (this.executionHistory.length > this.maxHistorySize) {
      this.executionHistory = this.executionHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * 获取执行历史
   */
  getExecutionHistory(limit?: number): CommandExecutionRecord[] {
    const history = [...this.executionHistory].reverse();
    return limit ? history.slice(0, limit) : history;
  }

  /**
   * 获取执行统计
   */
  getExecutionStats(): {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageExecutionTime: number;
    mostExecutedCommands: Array<{ commandId: string; count: number }>;
  } {
    const total = this.executionHistory.length;
    const successful = this.executionHistory.filter(r => r.success).length;
    const failed = total - successful;
    
    const avgTime = total > 0 
      ? this.executionHistory.reduce((sum, r) => sum + r.executionTime, 0) / total 
      : 0;

    // 统计最常执行的命令
    const commandCounts: Record<string, number> = {};
    this.executionHistory.forEach(record => {
      commandCounts[record.commandId] = (commandCounts[record.commandId] || 0) + 1;
    });

    const mostExecuted = Object.entries(commandCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([commandId, count]) => ({ commandId, count }));

    return {
      totalExecutions: total,
      successfulExecutions: successful,
      failedExecutions: failed,
      averageExecutionTime: Math.round(avgTime),
      mostExecutedCommands: mostExecuted
    };
  }

  /**
   * 清理执行历史
   */
  clearExecutionHistory(): void {
    this.executionHistory = [];
  }

  /**
   * 批量执行命令
   */
  async executeCommandsBatch(
    commandIds: string[],
    options?: {
      stopOnFailure?: boolean;
      timeout?: number;
      confirmEach?: boolean;
    }
  ): Promise<Array<{ commandId: string; result: ToolResult }>> {
    const results: Array<{ commandId: string; result: ToolResult }> = [];
    const { stopOnFailure = false, timeout = 5000, confirmEach = false } = options || {};

    for (const commandId of commandIds) {
      const result = await this.executeInternal({
        commandId,
        timeout,
        confirmExecution: confirmEach
      });

      results.push({ commandId, result });

      if (!result.success && stopOnFailure) {
        break;
      }
    }

    return results;
  }
}

/**
 * 命令执行记录
 */
export interface CommandExecutionRecord {
  commandId: string;
  commandName: string;
  success: boolean;
  executionTime: number;
  timestamp: Date;
  error?: string;
  context: string;
}
