// 记忆系统相关类型定义

export interface ConversationContext {
  id: string;
  messages: ConversationMessage[];
  metadata: ConversationMetadata;
  createdAt: Date;
  updatedAt: Date;
}

export interface ConversationMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface ConversationMetadata {
  title?: string;
  tags: string[];
  summary?: string;
  toolsUsed: string[];
  filesReferenced: string[];
}

export interface UserPreference {
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description?: string;
  updatedAt: Date;
}

export interface MemorySystem {
  // 短期记忆 - 对话上下文
  saveConversation(context: ConversationContext): Promise<void>;
  loadConversation(id: string): Promise<ConversationContext | null>;
  listConversations(limit?: number): Promise<ConversationContext[]>;
  deleteConversation(id: string): Promise<void>;
  
  // 长期记忆 - 用户偏好
  setPreference(key: string, value: any): Promise<void>;
  getPreference<T>(key: string, defaultValue?: T): Promise<T>;
  listPreferences(): Promise<UserPreference[]>;
  deletePreference(key: string): Promise<void>;
  
  // 记忆查询
  searchConversations(query: string, limit?: number): Promise<ConversationContext[]>;
  getRecentContext(limit?: number): Promise<ConversationMessage[]>;
}
