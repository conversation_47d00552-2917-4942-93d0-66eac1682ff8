/**
 * 测试JSON解析修复
 */

function testJSONParsing() {
  console.log('🧪 开始测试JSON解析修复...\n');
  
  // 模拟不同格式的LLM响应
  const testCases = [
    {
      name: '正常JSON格式',
      response: '{"type": "tool_call", "toolName": "web_search", "parameters": {"query": "test"}}',
      shouldParse: true
    },
    {
      name: 'Markdown代码块包裹的JSON',
      response: '```json\n{"type": "tool_call", "toolName": "web_search", "parameters": {"query": "test"}}\n```',
      shouldParse: true
    },
    {
      name: '通用代码块包裹的JSON',
      response: '```\n{"type": "tool_call", "toolName": "web_search", "parameters": {"query": "test"}}\n```',
      shouldParse: true
    },
    {
      name: '带额外空格的Markdown代码块',
      response: '  ```json  \n  {"type": "tool_call", "toolName": "web_search", "parameters": {"query": "test"}}  \n  ```  ',
      shouldParse: true
    },
    {
      name: '包含中文的复杂JSON',
      response: '```json\n{\n  "type": "tool_call",\n  "description": "执行网络搜索以获取量子学原理的相关信息",\n  "toolName": "web_search",\n  "parameters": {\n    "query": "量子学原理 quantum principles",\n    "engine": "google",\n    "maxResults": 5,\n    "language": "zh-CN",\n    "safeSearch": true,\n    "region": "CN"\n  }\n}\n```',
      shouldParse: true
    },
    {
      name: '无效JSON格式',
      response: '这不是JSON格式的内容',
      shouldParse: false
    }
  ];
  
  // 模拟JSON解析逻辑
  function parseActionResponse(responseContent) {
    try {
      // 清理响应内容，移除markdown代码块标记
      let cleanedContent = responseContent.trim();
      
      // 移除markdown代码块标记
      if (cleanedContent.startsWith('```json')) {
        cleanedContent = cleanedContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        console.log('🧹 移除了markdown代码块标记');
      } else if (cleanedContent.startsWith('```')) {
        cleanedContent = cleanedContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
        console.log('🧹 移除了通用代码块标记');
      }
      
      console.log('🧹 清理后的内容:', cleanedContent.substring(0, 100) + '...');
      
      const parsedAction = JSON.parse(cleanedContent);
      console.log('✅ JSON解析成功');
      
      // 验证必要字段
      if (!parsedAction.type) {
        throw new Error('缺少必要字段: type');
      }
      
      return { success: true, data: parsedAction };
    } catch (error) {
      console.log('❌ JSON解析失败:', error.message);
      return { success: false, error: error.message };
    }
  }
  
  // 模拟文本提取逻辑
  function extractActionFromText(text) {
    console.log('🔍 尝试从文本中提取行动计划...');
    
    try {
      // 尝试匹配工具调用模式
      const toolCallPatterns = [
        /使用\s*(\w+)\s*工具/,
        /调用\s*(\w+)\s*工具/,
        /执行\s*(\w+)\s*工具/,
        /toolName["\s]*:\s*["\s]*(\w+)/,
        /web_search|vault_query|javascript_executor|plugin_manager/
      ];
      
      for (const pattern of toolCallPatterns) {
        const match = text.match(pattern);
        if (match) {
          let toolName = match[1] || match[0];
          
          // 标准化工具名称
          if (toolName.includes('web_search') || text.includes('网络搜索') || text.includes('搜索')) {
            toolName = 'web_search';
          } else if (toolName.includes('vault_query') || text.includes('笔记') || text.includes('vault')) {
            toolName = 'vault_query';
          } else if (toolName.includes('javascript_executor') || text.includes('计算') || text.includes('代码')) {
            toolName = 'javascript_executor';
          } else if (toolName.includes('plugin_manager') || text.includes('插件')) {
            toolName = 'plugin_manager';
          }
          
          // 提取查询参数
          let query = '';
          const queryPatterns = [
            /"query"\s*:\s*"([^"]+)"/,
            /搜索["\s]*([^"]+)["\s]*/,
            /查询["\s]*([^"]+)["\s]*/
          ];
          
          for (const qPattern of queryPatterns) {
            const qMatch = text.match(qPattern);
            if (qMatch) {
              query = qMatch[1];
              break;
            }
          }
          
          const extractedAction = {
            type: 'tool_call',
            description: `从文本中提取的工具调用: ${toolName}`,
            toolName: toolName,
            parameters: query ? { query } : {}
          };
          
          console.log('✅ 成功提取行动计划:', extractedAction);
          return extractedAction;
        }
      }
      
      console.log('❌ 无法从文本中提取工具调用信息');
      return null;
    } catch (error) {
      console.log('❌ 提取行动计划时出错:', error.message);
      return null;
    }
  }
  
  // 运行测试用例
  let passedTests = 0;
  let totalTests = testCases.length;
  
  testCases.forEach((testCase, index) => {
    console.log(`\n🔧 测试 ${index + 1}: ${testCase.name}`);
    console.log('📄 原始响应:', testCase.response.substring(0, 50) + '...');
    
    const result = parseActionResponse(testCase.response);
    
    if (result.success === testCase.shouldParse) {
      console.log('✅ 测试通过');
      passedTests++;
      
      if (result.success) {
        console.log('📋 解析结果:', {
          type: result.data.type,
          toolName: result.data.toolName,
          hasParameters: !!result.data.parameters
        });
      }
    } else {
      console.log('❌ 测试失败');
      console.log('期望:', testCase.shouldParse ? '解析成功' : '解析失败');
      console.log('实际:', result.success ? '解析成功' : '解析失败');
      
      // 如果JSON解析失败，尝试文本提取
      if (!result.success && testCase.shouldParse) {
        console.log('🔄 尝试文本提取...');
        const extracted = extractActionFromText(testCase.response);
        if (extracted) {
          console.log('✅ 文本提取成功，测试通过');
          passedTests++;
        }
      }
    }
  });
  
  console.log('\n📊 测试结果总结:');
  console.log(`✅ 通过: ${passedTests}/${totalTests}`);
  console.log(`❌ 失败: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！JSON解析修复有效');
    return true;
  } else {
    console.log('⚠️ 部分测试失败，需要进一步优化');
    return false;
  }
}

// 运行测试
if (require.main === module) {
  const success = testJSONParsing();
  process.exit(success ? 0 : 1);
}

module.exports = { testJSONParsing };
