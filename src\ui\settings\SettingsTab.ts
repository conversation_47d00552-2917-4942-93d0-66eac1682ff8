import { App, PluginSettingTab, Setting, Notice, ButtonComponent } from 'obsidian';
import AICoachAdvancedPlugin from '@/main';
import { LLMFactory } from '@/core/llm/LLMFactory';

export class SettingsTab extends PluginSettingTab {
  plugin: AICoachAdvancedPlugin;

  constructor(app: App, plugin: AICoachAdvancedPlugin) {
    super(app, plugin);
    this.plugin = plugin;
  }

  display(): void {
    const { containerEl } = this;
    containerEl.empty();

    containerEl.createEl('h1', { text: 'AI Coach Advanced 设置' });

    // 添加导入/导出按钮
    this.addImportExportSection(containerEl);

    // LLM配置部分
    this.addLLMSection(containerEl);

    // 工具配置部分
    this.addToolsSection(containerEl);

    // UI配置部分
    this.addUISection(containerEl);

    // 安全配置部分
    this.addSecuritySection(containerEl);

    // 高级配置部分
    this.addAdvancedSection(containerEl);
  }

  private addImportExportSection(containerEl: HTMLElement): void {
    containerEl.createEl('h2', { text: '配置管理' });

    const importExportDiv = containerEl.createDiv({ cls: 'ai-coach-import-export' });

    new Setting(importExportDiv)
      .setName('导出配置')
      .setDesc('导出当前配置到剪贴板（不包含敏感信息）')
      .addButton(button => button
        .setButtonText('导出')
        .onClick(async () => {
          try {
            const configManager = this.plugin['configManager'];
            const exportData = configManager.exportConfig();
            await navigator.clipboard.writeText(exportData);
            new Notice('配置已导出到剪贴板');
          } catch (error) {
            new Notice('导出失败: ' + error.message);
          }
        }));

    new Setting(importExportDiv)
      .setName('导入配置')
      .setDesc('从剪贴板导入配置')
      .addButton(button => button
        .setButtonText('导入')
        .onClick(async () => {
          try {
            const configData = await navigator.clipboard.readText();
            const configManager = this.plugin['configManager'];
            await configManager.importConfig(configData);
            this.display(); // 刷新设置页面
          } catch (error) {
            new Notice('导入失败: ' + error.message);
          }
        }));

    new Setting(importExportDiv)
      .setName('重置配置')
      .setDesc('将所有设置重置为默认值')
      .addButton(button => button
        .setButtonText('重置')
        .setWarning()
        .onClick(async () => {
          const confirmed = confirm('确定要重置所有配置吗？此操作不可撤销。');
          if (confirmed) {
            const configManager = this.plugin['configManager'];
            await configManager.resetConfig();
            this.display(); // 刷新设置页面
          }
        }));
  }

  private addLLMSection(containerEl: HTMLElement): void {
    containerEl.createEl('h2', { text: 'LLM配置' });

    const config = this.plugin.getConfig();
    const providers = LLMFactory.getSupportedProviders();

    // 提供商选择
    new Setting(containerEl)
      .setName('LLM提供商')
      .setDesc('选择要使用的LLM服务提供商')
      .addDropdown(dropdown => {
        providers.forEach(provider => {
          dropdown.addOption(provider.id, provider.name);
        });
        dropdown.setValue(config.llm.provider)
          .onChange(async (value: any) => {
            await this.plugin.updateConfig({
              llm: { ...config.llm, provider: value }
            });
            this.display(); // 刷新页面以显示相应的配置选项
          });
      });

    // API密钥
    new Setting(containerEl)
      .setName('API密钥')
      .setDesc('输入LLM服务的API密钥')
      .addText(text => text
        .setPlaceholder('输入API密钥')
        .setValue(config.llm.apiKey)
        .onChange(async (value) => {
          await this.plugin.updateConfig({
            llm: { ...config.llm, apiKey: value }
          });
        }));

    // 自定义BaseURL（仅在选择自定义提供商时显示）
    if (config.llm.provider === 'custom') {
      new Setting(containerEl)
        .setName('API基础URL')
        .setDesc('自定义API的基础URL')
        .addText(text => text
          .setPlaceholder('https://api.example.com/v1')
          .setValue(config.llm.baseUrl || '')
          .onChange(async (value) => {
            await this.plugin.updateConfig({
              llm: { ...config.llm, baseUrl: value }
            });
          }));
    }

    // 模型选择
    new Setting(containerEl)
      .setName('模型')
      .setDesc('选择要使用的具体模型')
      .addText(text => text
        .setPlaceholder('例如: gpt-3.5-turbo')
        .setValue(config.llm.model)
        .onChange(async (value) => {
          await this.plugin.updateConfig({
            llm: { ...config.llm, model: value }
          });
        }));

    // 最大Token数
    new Setting(containerEl)
      .setName('最大Token数')
      .setDesc('单次请求的最大Token数量')
      .addSlider(slider => slider
        .setLimits(100, 8000, 100)
        .setValue(config.llm.maxTokens)
        .setDynamicTooltip()
        .onChange(async (value) => {
          await this.plugin.updateConfig({
            llm: { ...config.llm, maxTokens: value }
          });
        }));

    // 温度设置
    new Setting(containerEl)
      .setName('温度')
      .setDesc('控制回答的随机性，0-2之间，值越高越随机')
      .addSlider(slider => slider
        .setLimits(0, 2, 0.1)
        .setValue(config.llm.temperature)
        .setDynamicTooltip()
        .onChange(async (value) => {
          await this.plugin.updateConfig({
            llm: { ...config.llm, temperature: value }
          });
        }));

    // 连接测试
    new Setting(containerEl)
      .setName('测试连接')
      .setDesc('测试LLM API连接是否正常')
      .addButton(button => button
        .setButtonText('测试')
        .onClick(async () => {
          button.setButtonText('测试中...');
          button.setDisabled(true);

          try {
            const validation = await this.plugin['configManager'].validateLLMConfig();
            if (!validation.valid) {
              new Notice('配置验证失败: ' + validation.errors.join(', '));
              return;
            }

            // 这里应该调用LLM接口进行实际测试
            // 目前只是模拟测试
            await new Promise(resolve => setTimeout(resolve, 1000));
            new Notice('连接测试成功！');
          } catch (error) {
            new Notice('连接测试失败: ' + error.message);
          } finally {
            button.setButtonText('测试');
            button.setDisabled(false);
          }
        }));
  }

  private addToolsSection(containerEl: HTMLElement): void {
    containerEl.createEl('h2', { text: '工具配置' });

    const config = this.plugin.getConfig();

    // Vault查询工具
    containerEl.createEl('h3', { text: 'Vault知识库' });

    new Setting(containerEl)
      .setName('启用Vault查询')
      .setDesc('允许AI助手搜索和查询你的笔记内容')
      .addToggle(toggle => toggle
        .setValue(config.tools.vault.enabled)
        .onChange(async (value) => {
          await this.plugin.updateConfig({
            tools: {
              ...config.tools,
              vault: { ...config.tools.vault, enabled: value }
            }
          });
        }));

    new Setting(containerEl)
      .setName('启用自动索引')
      .setDesc('自动为Vault内容创建搜索索引')
      .addToggle(toggle => toggle
        .setValue(config.tools.vault.indexingEnabled)
        .onChange(async (value) => {
          await this.plugin.updateConfig({
            tools: {
              ...config.tools,
              vault: { ...config.tools.vault, indexingEnabled: value }
            }
          });
        }));

    // 网络搜索工具
    containerEl.createEl('h3', { text: '网络搜索' });

    new Setting(containerEl)
      .setName('启用网络搜索')
      .setDesc('允许AI助手进行网络搜索获取最新信息')
      .addToggle(toggle => toggle
        .setValue(config.tools.web.enabled)
        .onChange(async (value) => {
          await this.plugin.updateConfig({
            tools: {
              ...config.tools,
              web: { ...config.tools.web, enabled: value }
            }
          });
        }));

    // JavaScript执行工具
    containerEl.createEl('h3', { text: 'JavaScript执行' });

    new Setting(containerEl)
      .setName('启用JS执行')
      .setDesc('允许AI助手生成和执行JavaScript代码（高风险功能）')
      .addToggle(toggle => toggle
        .setValue(config.tools.javascript.enabled)
        .onChange(async (value) => {
          await this.plugin.updateConfig({
            tools: {
              ...config.tools,
              javascript: { ...config.tools.javascript, enabled: value }
            }
          });
        }));
  }

  private addUISection(containerEl: HTMLElement): void {
    containerEl.createEl('h2', { text: 'UI配置' });

    const config = this.plugin.getConfig();

    new Setting(containerEl)
      .setName('语言')
      .setDesc('选择界面语言')
      .addDropdown(dropdown => dropdown
        .addOption('zh', '中文')
        .addOption('en', 'English')
        .setValue(config.ui.language)
        .onChange(async (value: any) => {
          await this.plugin.updateConfig({
            ui: { ...config.ui, language: value }
          });
        }));

    new Setting(containerEl)
      .setName('显示进度')
      .setDesc('在执行任务时显示进度提示')
      .addToggle(toggle => toggle
        .setValue(config.ui.showProgress)
        .onChange(async (value) => {
          await this.plugin.updateConfig({
            ui: { ...config.ui, showProgress: value }
          });
        }));
  }

  private addSecuritySection(containerEl: HTMLElement): void {
    containerEl.createEl('h2', { text: '安全配置' });

    const config = this.plugin.getConfig();

    new Setting(containerEl)
      .setName('加密API密钥')
      .setDesc('在本地存储中加密API密钥')
      .addToggle(toggle => toggle
        .setValue(config.security.encryptApiKeys)
        .onChange(async (value) => {
          await this.plugin.updateConfig({
            security: { ...config.security, encryptApiKeys: value }
          });
        }));

    new Setting(containerEl)
      .setName('请求频率限制')
      .setDesc('每分钟最大请求数量')
      .addSlider(slider => slider
        .setLimits(10, 300, 10)
        .setValue(config.security.maxRequestsPerMinute)
        .setDynamicTooltip()
        .onChange(async (value) => {
          await this.plugin.updateConfig({
            security: { ...config.security, maxRequestsPerMinute: value }
          });
        }));
  }

  private addAdvancedSection(containerEl: HTMLElement): void {
    containerEl.createEl('h2', { text: '高级配置' });

    new Setting(containerEl)
      .setName('重建Vault索引')
      .setDesc('重新为所有笔记创建搜索索引')
      .addButton(button => button
        .setButtonText('重建索引')
        .onClick(async () => {
          button.setButtonText('重建中...');
          button.setDisabled(true);

          try {
            await new Promise(resolve => setTimeout(resolve, 2000));
            new Notice('索引重建完成');
          } catch (error) {
            new Notice('索引重建失败: ' + error.message);
          } finally {
            button.setButtonText('重建索引');
            button.setDisabled(false);
          }
        }));

    new Setting(containerEl)
      .setName('清理数据')
      .setDesc('清理所有对话历史和缓存数据')
      .addButton(button => button
        .setButtonText('清理')
        .setWarning()
        .onClick(async () => {
          const confirmed = confirm('确定要清理所有数据吗？此操作不可撤销。');
          if (confirmed) {
            try {
              await this.plugin.getMemoryManager().clearAllConversations();
              new Notice('数据清理完成');
            } catch (error) {
              new Notice('数据清理失败: ' + error.message);
            }
          }
        }));
  }
}