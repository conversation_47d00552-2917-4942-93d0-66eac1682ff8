import { App, PluginSettingTab, Setting } from 'obsidian';
import AICoachAdvancedPlugin from '@/main';

export class SettingsTab extends PluginSettingTab {
  plugin: AICoachAdvancedPlugin;

  constructor(app: App, plugin: AICoachAdvancedPlugin) {
    super(app, plugin);
    this.plugin = plugin;
  }

  display(): void {
    const { containerEl } = this;
    containerEl.empty();

    containerEl.createEl('h2', { text: 'AI Coach Advanced 设置' });

    // LLM配置部分
    containerEl.createEl('h3', { text: 'LLM配置' });
    
    const config = this.plugin.getConfig();

    new Setting(containerEl)
      .setName('LLM提供商')
      .setDesc('选择要使用的LLM服务提供商')
      .addDropdown(dropdown => dropdown
        .addOption('openai', 'OpenAI')
        .addOption('gemini', 'Google Gemini')
        .addOption('deepseek', 'DeepSeek')
        .addOption('custom', '自定义')
        .setValue(config.llm.provider)
        .onChange(async (value: any) => {
          await this.plugin.updateConfig({
            llm: { ...config.llm, provider: value }
          });
        }));

    new Setting(containerEl)
      .setName('API密钥')
      .setDesc('输入LLM服务的API密钥')
      .addText(text => text
        .setPlaceholder('输入API密钥')
        .setValue(config.llm.apiKey)
        .onChange(async (value) => {
          await this.plugin.updateConfig({
            llm: { ...config.llm, apiKey: value }
          });
        }));

    new Setting(containerEl)
      .setName('模型')
      .setDesc('选择要使用的具体模型')
      .addText(text => text
        .setPlaceholder('例如: gpt-3.5-turbo')
        .setValue(config.llm.model)
        .onChange(async (value) => {
          await this.plugin.updateConfig({
            llm: { ...config.llm, model: value }
          });
        }));

    // 工具配置部分
    containerEl.createEl('h3', { text: '工具配置' });

    new Setting(containerEl)
      .setName('启用Vault查询')
      .setDesc('允许AI助手搜索和查询你的笔记内容')
      .addToggle(toggle => toggle
        .setValue(config.tools.vault.enabled)
        .onChange(async (value) => {
          await this.plugin.updateConfig({
            tools: {
              ...config.tools,
              vault: { ...config.tools.vault, enabled: value }
            }
          });
        }));

    new Setting(containerEl)
      .setName('启用网络搜索')
      .setDesc('允许AI助手进行网络搜索获取最新信息')
      .addToggle(toggle => toggle
        .setValue(config.tools.web.enabled)
        .onChange(async (value) => {
          await this.plugin.updateConfig({
            tools: {
              ...config.tools,
              web: { ...config.tools.web, enabled: value }
            }
          });
        }));

    // 这里将在后续任务中添加更多设置选项
  }
}
