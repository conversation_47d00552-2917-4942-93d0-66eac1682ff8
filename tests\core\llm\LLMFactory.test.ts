import { LLMFactory } from '../../../src/core/llm/LLMFactory';
import { LLMConfig } from '../../../src/types/config';

// Mock fetch for API calls
global.fetch = jest.fn();

describe('LLMFactory', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createLLM', () => {
    it('should create OpenAI LLM instance', async () => {
      const config: LLMConfig = {
        provider: 'openai',
        apiKey: 'test-key',
        model: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 1000
      };

      const llm = await LLMFactory.createLLM(config);

      expect(llm).toBeDefined();
      expect(llm.constructor.name).toBe('OpenAILLM');
    });

    it('should create Google LLM instance', async () => {
      const config: LLMConfig = {
        provider: 'google',
        apiKey: 'test-key',
        model: 'gemini-pro',
        temperature: 0.7,
        maxTokens: 1000
      };

      const llm = await LLMFactory.createLLM(config);

      expect(llm).toBeDefined();
      expect(llm.constructor.name).toBe('GoogleLLM');
    });

    it('should create DeepSeek LLM instance', async () => {
      const config: LLMConfig = {
        provider: 'deepseek',
        apiKey: 'test-key',
        model: 'deepseek-chat',
        temperature: 0.7,
        maxTokens: 1000
      };

      const llm = await LLMFactory.createLLM(config);

      expect(llm).toBeDefined();
      expect(llm.constructor.name).toBe('DeepSeekLLM');
    });

    it('should throw error for unsupported provider', async () => {
      const config: LLMConfig = {
        provider: 'unsupported' as any,
        apiKey: 'test-key',
        model: 'test-model',
        temperature: 0.7,
        maxTokens: 1000
      };

      await expect(LLMFactory.createLLM(config)).rejects.toThrow(
        'Unsupported LLM provider: unsupported'
      );
    });

    it('should throw error for missing API key', async () => {
      const config: LLMConfig = {
        provider: 'openai',
        apiKey: '',
        model: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 1000
      };

      await expect(LLMFactory.createLLM(config)).rejects.toThrow(
        'API key is required'
      );
    });
  });

  describe('getSupportedProviders', () => {
    it('should return list of supported providers', () => {
      const providers = LLMFactory.getSupportedProviders();

      expect(providers).toContain('openai');
      expect(providers).toContain('google');
      expect(providers).toContain('deepseek');
      expect(providers).toContain('anthropic');
    });
  });

  describe('validateConfig', () => {
    it('should validate valid config', () => {
      const config: LLMConfig = {
        provider: 'openai',
        apiKey: 'test-key',
        model: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 1000
      };

      const result = LLMFactory.validateConfig(config);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect invalid provider', () => {
      const config: LLMConfig = {
        provider: 'invalid' as any,
        apiKey: 'test-key',
        model: 'test-model',
        temperature: 0.7,
        maxTokens: 1000
      };

      const result = LLMFactory.validateConfig(config);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Unsupported provider: invalid');
    });

    it('should detect missing API key', () => {
      const config: LLMConfig = {
        provider: 'openai',
        apiKey: '',
        model: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 1000
      };

      const result = LLMFactory.validateConfig(config);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('API key is required');
    });

    it('should detect invalid temperature', () => {
      const config: LLMConfig = {
        provider: 'openai',
        apiKey: 'test-key',
        model: 'gpt-3.5-turbo',
        temperature: 2.5, // Invalid: should be 0-2
        maxTokens: 1000
      };

      const result = LLMFactory.validateConfig(config);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Temperature must be between 0 and 2');
    });

    it('should detect invalid max tokens', () => {
      const config: LLMConfig = {
        provider: 'openai',
        apiKey: 'test-key',
        model: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: -100 // Invalid: should be positive
      };

      const result = LLMFactory.validateConfig(config);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Max tokens must be positive');
    });
  });

  describe('getDefaultConfig', () => {
    it('should return default config for OpenAI', () => {
      const config = LLMFactory.getDefaultConfig('openai');

      expect(config.provider).toBe('openai');
      expect(config.model).toBe('gpt-3.5-turbo');
      expect(config.temperature).toBe(0.7);
      expect(config.maxTokens).toBe(2000);
    });

    it('should return default config for Google', () => {
      const config = LLMFactory.getDefaultConfig('google');

      expect(config.provider).toBe('google');
      expect(config.model).toBe('gemini-pro');
      expect(config.temperature).toBe(0.7);
      expect(config.maxTokens).toBe(2000);
    });

    it('should throw error for unsupported provider', () => {
      expect(() => LLMFactory.getDefaultConfig('unsupported' as any)).toThrow(
        'Unsupported provider: unsupported'
      );
    });
  });
});
