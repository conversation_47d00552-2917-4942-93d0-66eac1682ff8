"use strict";var W=Object.defineProperty;var Pe=Object.getOwnPropertyDescriptor;var Ie=Object.getOwnPropertyNames;var ke=Object.prototype.hasOwnProperty;var Le=(m,s,e)=>s in m?W(m,s,{enumerable:!0,configurable:!0,writable:!0,value:e}):m[s]=e;var $e=(m,s)=>{for(var e in s)W(m,e,{get:s[e],enumerable:!0})},Ae=(m,s,e,t)=>{if(s&&typeof s=="object"||typeof s=="function")for(let r of Ie(s))!ke.call(m,r)&&r!==e&&W(m,r,{get:()=>s[r],enumerable:!(t=Pe(s,r))||t.enumerable});return m};var De=m=>Ae(W({},"__esModule",{value:!0}),m);var d=(m,s,e)=>(Le(m,typeof s!="symbol"?s+"":s,e),e);var _e={};$e(_e,{default:()=>he});module.exports=De(_e);var w=require("obsidian");var I={llm:{provider:"openai",apiKey:"",model:"gpt-3.5-turbo",maxTokens:2e3,temperature:.7,timeout:3e4},tools:{vault:{enabled:!0,indexingEnabled:!0,maxResults:10,chunkSize:1e3},web:{enabled:!0,searchEngine:"duckduckgo",maxResults:5},plugin:{enabled:!1,allowedPlugins:[],restrictedCommands:[]},javascript:{enabled:!1,requireConfirmation:!0,timeout:5e3,memoryLimit:52428800}},ui:{theme:"auto",language:"zh",showProgress:!0,autoSave:!0},security:{encryptApiKeys:!0,logLevel:"info",maxRequestsPerMinute:60,enableAuditLog:!0}};var $=require("obsidian");var _=class{constructor(s,e,t,r=3e4){this.apiKey=s,this.model=e,this.baseUrl=t,this.timeout=r}updateConfig(s){s.apiKey!==void 0&&(this.apiKey=s.apiKey),s.model!==void 0&&(this.model=s.model),s.baseUrl!==void 0&&(this.baseUrl=s.baseUrl),s.timeout!==void 0&&(this.timeout=s.timeout)}getConfig(){return{apiKey:this.apiKey,model:this.model,baseUrl:this.baseUrl,timeout:this.timeout}}},y=class extends Error{constructor(s,e,t,r){super(s),this.name="LLMError",this.code=e,this.statusCode=t,this.details=r}},v={INVALID_API_KEY:"INVALID_API_KEY",RATE_LIMIT_EXCEEDED:"RATE_LIMIT_EXCEEDED",MODEL_NOT_FOUND:"MODEL_NOT_FOUND",INSUFFICIENT_QUOTA:"INSUFFICIENT_QUOTA",NETWORK_ERROR:"NETWORK_ERROR",TIMEOUT:"TIMEOUT",INVALID_REQUEST:"INVALID_REQUEST",SERVER_ERROR:"SERVER_ERROR",UNKNOWN_ERROR:"UNKNOWN_ERROR"};var L=class extends _{constructor(e,t,r,o=3e4){super(e,t,r||"https://api.openai.com/v1",o);this.defaultBaseUrl="https://api.openai.com/v1"}async generateText(e,t){try{let r=this.buildRequestBody(e,t),o=await this.makeRequest("/chat/completions",r);return this.parseResponse(o)}catch(r){throw this.handleError(r)}}async generateWithTools(e,t,r){try{let o=this.buildRequestBody(e,r,t),n=await this.makeRequest("/chat/completions",o);return this.parseResponse(n)}catch(o){throw this.handleError(o)}}async generateTextStream(e,t,r){try{let o=this.buildRequestBody(e,{...r,stream:!0});await this.makeStreamRequest("/chat/completions",o,t)}catch(o){throw this.handleError(o)}}async validateConnection(){try{return await this.makeRequest("/models"),!0}catch(e){return console.error("OpenAI connection validation failed:",e),!1}}async getModelInfo(){let t={"gpt-3.5-turbo":{maxTokens:4096,supportsFunctionCalling:!0,costPer1kTokens:{input:.0015,output:.002}},"gpt-4":{maxTokens:8192,supportsFunctionCalling:!0,costPer1kTokens:{input:.03,output:.06}},"gpt-4-turbo-preview":{maxTokens:128e3,supportsFunctionCalling:!0,costPer1kTokens:{input:.01,output:.03}}}[this.model]||{};return{name:this.model,provider:"OpenAI",maxTokens:t.maxTokens||4096,supportsFunctionCalling:t.supportsFunctionCalling||!1,supportsStreaming:!0,costPer1kTokens:t.costPer1kTokens}}estimateTokens(e){let t=(e.match(/[\u4e00-\u9fff]/g)||[]).length,r=e.length-t;return Math.ceil(t/1.5+r/4)}async getSupportedModels(){try{return(await this.makeRequest("/models")).data.filter(t=>t.id.includes("gpt")).map(t=>t.id).sort()}catch(e){return["gpt-3.5-turbo","gpt-4","gpt-4-turbo-preview"]}}buildRequestBody(e,t,r){var n;let o={model:this.model,messages:this.convertMessages(e),max_tokens:(t==null?void 0:t.maxTokens)||2e3,temperature:(t==null?void 0:t.temperature)||.7,stream:(t==null?void 0:t.stream)||!1};return(n=t==null?void 0:t.stopSequences)!=null&&n.length&&(o.stop=t.stopSequences),r!=null&&r.length&&(o.tools=r,o.tool_choice="auto"),o}convertMessages(e){return e.map(t=>{let r={role:t.role,content:t.content};return t.name&&(r.name=t.name),t.tool_calls&&(r.tool_calls=t.tool_calls),t.tool_call_id&&(r.tool_call_id=t.tool_call_id),r})}async makeRequest(e,t){var a;let r=`${this.baseUrl}${e}`,o={Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json"},n=new AbortController,i=setTimeout(()=>n.abort(),this.timeout);try{let c=await fetch(r,{method:t?"POST":"GET",headers:o,body:t?JSON.stringify(t):void 0,signal:n.signal});if(clearTimeout(i),!c.ok){let l=await c.json().catch(()=>({}));throw new Error(`HTTP ${c.status}: ${((a=l.error)==null?void 0:a.message)||c.statusText}`)}return await c.json()}catch(c){throw clearTimeout(i),c}}async makeStreamRequest(e,t,r){var c,l,u,p,f;let o=`${this.baseUrl}${e}`,n={Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json"},i=new AbortController,a=setTimeout(()=>i.abort(),this.timeout);try{let C=await fetch(o,{method:"POST",headers:n,body:JSON.stringify(t),signal:i.signal});if(clearTimeout(a),!C.ok){let F=await C.json().catch(()=>({}));throw new Error(`HTTP ${C.status}: ${((c=F.error)==null?void 0:c.message)||C.statusText}`)}let k=(l=C.body)==null?void 0:l.getReader();if(!k)throw new Error("Failed to get response reader");let P=new TextDecoder,A="";for(;;){let{done:F,value:U}=await k.read();if(F)break;A+=P.decode(U,{stream:!0});let D=A.split(`
`);A=D.pop()||"";for(let j of D)if(j.startsWith("data: ")){let V=j.slice(6);if(V==="[DONE]")return;try{let z=(f=(p=(u=JSON.parse(V).choices)==null?void 0:u[0])==null?void 0:p.delta)==null?void 0:f.content;z&&r(z)}catch(K){}}}}catch(C){throw clearTimeout(a),C}}parseResponse(e){var o,n,i;let t=(o=e.choices)==null?void 0:o[0];if(!t)throw new Error("No choices in response");let r={content:((n=t.message)==null?void 0:n.content)||"",usage:e.usage?{prompt_tokens:e.usage.prompt_tokens,completion_tokens:e.usage.completion_tokens,total_tokens:e.usage.total_tokens}:void 0};return(i=t.message)!=null&&i.tool_calls&&(r.tool_calls=t.message.tool_calls),r}handleError(e){var t,r,o,n,i,a,c,l;return e.name==="AbortError"?new y("Request timeout",v.TIMEOUT):(t=e.message)!=null&&t.includes("401")?new y("Invalid API key",v.INVALID_API_KEY,401):(r=e.message)!=null&&r.includes("429")?new y("Rate limit exceeded",v.RATE_LIMIT_EXCEEDED,429):(o=e.message)!=null&&o.includes("404")?new y("Model not found",v.MODEL_NOT_FOUND,404):(n=e.message)!=null&&n.includes("insufficient_quota")?new y("Insufficient quota",v.INSUFFICIENT_QUOTA,403):(i=e.message)!=null&&i.includes("400")?new y("Invalid request",v.INVALID_REQUEST,400):(a=e.message)!=null&&a.includes("500")||(c=e.message)!=null&&c.includes("502")||(l=e.message)!=null&&l.includes("503")?new y("Server error",v.SERVER_ERROR,500):new y(e.message||"Unknown error",v.UNKNOWN_ERROR,void 0,e)}};var Y=class extends _{constructor(e,t,r,o=3e4){super(e,t,r||"https://generativelanguage.googleapis.com/v1beta",o);this.defaultBaseUrl="https://generativelanguage.googleapis.com/v1beta"}async generateText(e,t){try{let r=this.buildRequestBody(e,t),o=await this.makeRequest("/generateContent",r);return this.parseResponse(o)}catch(r){throw this.handleError(r)}}async generateWithTools(e,t,r){try{let o=this.buildRequestBody(e,r,t),n=await this.makeRequest("/generateContent",o);return this.parseResponse(n)}catch(o){throw this.handleError(o)}}async generateTextStream(e,t,r){try{let o=this.buildRequestBody(e,r);await this.makeStreamRequest("/streamGenerateContent",o,t)}catch(o){throw this.handleError(o)}}async validateConnection(){try{return await this.makeRequest("/models"),!0}catch(e){return console.error("Gemini connection validation failed:",e),!1}}async getModelInfo(){let t={"gemini-pro":{maxTokens:32768,supportsFunctionCalling:!0,costPer1kTokens:{input:5e-4,output:.0015}},"gemini-pro-vision":{maxTokens:16384,supportsFunctionCalling:!1,costPer1kTokens:{input:25e-5,output:5e-4}},"gemini-1.5-pro":{maxTokens:1048576,supportsFunctionCalling:!0,costPer1kTokens:{input:.0035,output:.0105}}}[this.model]||{};return{name:this.model,provider:"Google Gemini",maxTokens:t.maxTokens||32768,supportsFunctionCalling:t.supportsFunctionCalling||!1,supportsStreaming:!0,costPer1kTokens:t.costPer1kTokens}}estimateTokens(e){let t=(e.match(/[\u4e00-\u9fff]/g)||[]).length,r=e.length-t;return Math.ceil(t/1.3+r/3.5)}async getSupportedModels(){try{return(await this.makeRequest("/models")).models.filter(t=>t.name.includes("gemini")).map(t=>t.name.split("/").pop()).sort()}catch(e){return["gemini-pro","gemini-pro-vision","gemini-1.5-pro"]}}buildRequestBody(e,t,r){var i;let n={contents:this.convertMessages(e),generationConfig:{maxOutputTokens:(t==null?void 0:t.maxTokens)||2e3,temperature:(t==null?void 0:t.temperature)||.7}};return(i=t==null?void 0:t.stopSequences)!=null&&i.length&&(n.generationConfig.stopSequences=t.stopSequences),r!=null&&r.length&&(n.tools=[{functionDeclarations:r.map(a=>({name:a.function.name,description:a.function.description,parameters:a.function.parameters}))}]),n}convertMessages(e){let t=[],r="",o=[];for(let n of e)if(n.role==="system")r==="user"?o.push({text:n.content}):(o.length>0&&t.push({role:r,parts:o}),r="user",o=[{text:n.content}]);else if(n.role==="user")r==="user"?o.push({text:n.content}):(o.length>0&&t.push({role:r,parts:o}),r="user",o=[{text:n.content}]);else if(n.role==="assistant"){if(r==="model"?o.push({text:n.content}):(o.length>0&&t.push({role:r,parts:o}),r="model",o=[{text:n.content}]),n.tool_calls)for(let i of n.tool_calls)o.push({functionCall:{name:i.function.name,args:JSON.parse(i.function.arguments)}})}else n.role==="tool"&&o.push({functionResponse:{name:n.name,response:{result:n.content}}});return o.length>0&&t.push({role:r,parts:o}),t}async makeRequest(e,t){var a;let r=`${this.baseUrl}/models/${this.model}:${e.replace("/","")}?key=${this.apiKey}`,o={"Content-Type":"application/json"},n=new AbortController,i=setTimeout(()=>n.abort(),this.timeout);try{let c=await fetch(r,{method:t?"POST":"GET",headers:o,body:t?JSON.stringify(t):void 0,signal:n.signal});if(clearTimeout(i),!c.ok){let l=await c.json().catch(()=>({}));throw new Error(`HTTP ${c.status}: ${((a=l.error)==null?void 0:a.message)||c.statusText}`)}return await c.json()}catch(c){throw clearTimeout(i),c}}async makeStreamRequest(e,t,r){var c,l,u,p,f,C,k;let o=`${this.baseUrl}/models/${this.model}:${e.replace("/","")}?key=${this.apiKey}&alt=sse`,n={"Content-Type":"application/json"},i=new AbortController,a=setTimeout(()=>i.abort(),this.timeout);try{let P=await fetch(o,{method:"POST",headers:n,body:JSON.stringify(t),signal:i.signal});if(clearTimeout(a),!P.ok){let D=await P.json().catch(()=>({}));throw new Error(`HTTP ${P.status}: ${((c=D.error)==null?void 0:c.message)||P.statusText}`)}let A=(l=P.body)==null?void 0:l.getReader();if(!A)throw new Error("Failed to get response reader");let F=new TextDecoder,U="";for(;;){let{done:D,value:j}=await A.read();if(D)break;U+=F.decode(j,{stream:!0});let V=U.split(`
`);U=V.pop()||"";for(let K of V)if(K.startsWith("data: ")){let z=K.slice(6);if(z==="[DONE]")return;try{let Se=(k=(C=(f=(p=(u=JSON.parse(z).candidates)==null?void 0:u[0])==null?void 0:p.content)==null?void 0:f.parts)==null?void 0:C[0])==null?void 0:k.text;Se&&r(Se)}catch(Re){}}}}catch(P){throw clearTimeout(a),P}}parseResponse(e){var i,a,c,l,u,p;let t=(i=e.candidates)==null?void 0:i[0];if(!t)throw new Error("No candidates in response");let o={content:((l=(c=(a=t.content)==null?void 0:a.parts)==null?void 0:c[0])==null?void 0:l.text)||"",usage:e.usageMetadata?{prompt_tokens:e.usageMetadata.promptTokenCount||0,completion_tokens:e.usageMetadata.candidatesTokenCount||0,total_tokens:e.usageMetadata.totalTokenCount||0}:void 0},n=(p=(u=t.content)==null?void 0:u.parts)==null?void 0:p.filter(f=>f.functionCall);return n!=null&&n.length&&(o.tool_calls=n.map((f,C)=>({id:`call_${C}`,type:"function",function:{name:f.functionCall.name,arguments:JSON.stringify(f.functionCall.args)}}))),o}handleError(e){var t,r,o,n,i,a,c,l;return e.name==="AbortError"?new y("Request timeout",v.TIMEOUT):(t=e.message)!=null&&t.includes("401")||(r=e.message)!=null&&r.includes("403")?new y("Invalid API key",v.INVALID_API_KEY,401):(o=e.message)!=null&&o.includes("429")?new y("Rate limit exceeded",v.RATE_LIMIT_EXCEEDED,429):(n=e.message)!=null&&n.includes("404")?new y("Model not found",v.MODEL_NOT_FOUND,404):(i=e.message)!=null&&i.includes("400")?new y("Invalid request",v.INVALID_REQUEST,400):(a=e.message)!=null&&a.includes("500")||(c=e.message)!=null&&c.includes("502")||(l=e.message)!=null&&l.includes("503")?new y("Server error",v.SERVER_ERROR,500):new y(e.message||"Unknown error",v.UNKNOWN_ERROR,void 0,e)}};var Q=class extends L{constructor(s,e,t,r=3e4){super(s,e,t||"https://api.deepseek.com/v1",r)}async getModelInfo(){let e={"deepseek-chat":{maxTokens:32768,supportsFunctionCalling:!0,costPer1kTokens:{input:.0014,output:.0028}},"deepseek-coder":{maxTokens:16384,supportsFunctionCalling:!0,costPer1kTokens:{input:.0014,output:.0028}},"deepseek-math":{maxTokens:4096,supportsFunctionCalling:!1,costPer1kTokens:{input:.0014,output:.0028}}}[this.model]||{};return{name:this.model,provider:"DeepSeek",maxTokens:e.maxTokens||32768,supportsFunctionCalling:e.supportsFunctionCalling||!1,supportsStreaming:!0,costPer1kTokens:e.costPer1kTokens}}async getSupportedModels(){try{return(await this.makeRequest("/models")).data.filter(e=>e.id.includes("deepseek")).map(e=>e.id).sort()}catch(s){return["deepseek-chat","deepseek-coder","deepseek-math"]}}estimateTokens(s){let e=(s.match(/[\u4e00-\u9fff]/g)||[]).length,t=s.length-e;return Math.ceil(e/1.5+t/4)}async makeRequest(s,e){return super.makeRequest(s,e)}};var R=class{static createLLM(s){let e=this.generateKey(s);if(this.instances.has(e)){let r=this.instances.get(e);return r.updateConfig({apiKey:s.apiKey,model:s.model,baseUrl:s.baseUrl,timeout:s.timeout}),r}let t;switch(s.provider){case"openai":t=new L(s.apiKey,s.model,s.baseUrl,s.timeout);break;case"gemini":t=new Y(s.apiKey,s.model,s.baseUrl,s.timeout);break;case"deepseek":t=new Q(s.apiKey,s.model,s.baseUrl||"https://api.deepseek.com",s.timeout);break;case"custom":if(!s.baseUrl)throw new y("Custom provider requires baseUrl",v.INVALID_REQUEST);t=new L(s.apiKey,s.model,s.baseUrl,s.timeout);break;default:throw new y(`Unsupported LLM provider: ${s.provider}`,v.INVALID_REQUEST)}return this.instances.set(e,t),t}static getLLM(s){let e=this.generateKey(s);return this.instances.get(e)}static removeLLM(s){let e=this.generateKey(s);this.instances.delete(e)}static clearAll(){this.instances.clear()}static getSupportedProviders(){return[{id:"openai",name:"OpenAI",description:"OpenAI GPT\u7CFB\u5217\u6A21\u578B",requiresApiKey:!0,requiresBaseUrl:!1,defaultModels:["gpt-3.5-turbo","gpt-4","gpt-4-turbo-preview"]},{id:"gemini",name:"Google Gemini",description:"Google Gemini\u7CFB\u5217\u6A21\u578B",requiresApiKey:!0,requiresBaseUrl:!1,defaultModels:["gemini-pro","gemini-pro-vision"]},{id:"deepseek",name:"DeepSeek",description:"DeepSeek\u7CFB\u5217\u6A21\u578B",requiresApiKey:!0,requiresBaseUrl:!1,defaultModels:["deepseek-chat","deepseek-coder"]},{id:"custom",name:"\u81EA\u5B9A\u4E49",description:"OpenAI\u517C\u5BB9\u7684\u81EA\u5B9A\u4E49API",requiresApiKey:!0,requiresBaseUrl:!0,defaultModels:[]}]}static validateConfig(s){var t,r,o;let e=[];return(t=s.apiKey)!=null&&t.trim()||e.push("API\u5BC6\u94A5\u4E0D\u80FD\u4E3A\u7A7A"),(r=s.model)!=null&&r.trim()||e.push("\u6A21\u578B\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A"),s.provider==="custom"&&!((o=s.baseUrl)!=null&&o.trim())&&e.push("\u81EA\u5B9A\u4E49\u63D0\u4F9B\u5546\u9700\u8981\u6307\u5B9AbaseUrl"),s.timeout&&s.timeout<1e3&&e.push("\u8D85\u65F6\u65F6\u95F4\u4E0D\u80FD\u5C11\u4E8E1\u79D2"),s.maxTokens&&s.maxTokens<1&&e.push("\u6700\u5927token\u6570\u5FC5\u987B\u5927\u4E8E0"),s.temperature&&(s.temperature<0||s.temperature>2)&&e.push("\u6E29\u5EA6\u503C\u5FC5\u987B\u57280-2\u4E4B\u95F4"),{valid:e.length===0,errors:e}}static generateKey(s){return`${s.provider}:${s.model}:${s.baseUrl||"default"}`}};d(R,"instances",new Map);var G=class{constructor(s){d(this,"plugin");d(this,"config");d(this,"configVersion","1.0.0");d(this,"encryptionKey",null);this.plugin=s,this.config=this.deepClone(I)}async loadConfig(){try{let s=await this.plugin.loadData();s&&(s.version&&s.version!==this.configVersion?await this.migrateConfig(s):this.config=this.mergeConfig(I,s),this.config.security.encryptApiKeys&&await this.decryptSensitiveData(),this.validateConfig())}catch(s){console.error("Failed to load config:",s),new $.Notice("\u914D\u7F6E\u52A0\u8F7D\u5931\u8D25\uFF0C\u4F7F\u7528\u9ED8\u8BA4\u914D\u7F6E"),this.config=this.deepClone(I)}return this.config}async saveConfig(){try{this.validateConfig();let s=this.deepClone(this.config);s.version=this.configVersion,s.lastUpdated=new Date().toISOString(),this.config.security.encryptApiKeys&&await this.encryptSensitiveData(s),await this.plugin.saveData(s)}catch(s){throw console.error("Failed to save config:",s),new $.Notice("\u914D\u7F6E\u4FDD\u5B58\u5931\u8D25: "+s.message),s}}getConfig(){return this.deepClone(this.config)}updateConfig(s){this.config=this.mergeConfig(this.config,s)}updateLLMConfig(s){this.config.llm={...this.config.llm,...s}}getLLMConfig(){return this.deepClone(this.config.llm)}async validateLLMConfig(){return R.validateConfig(this.config.llm)}updateToolsConfig(s){this.config.tools=this.mergeConfig(this.config.tools,s)}getToolsConfig(){return this.deepClone(this.config.tools)}isToolEnabled(s){var e;return((e=this.config.tools[s])==null?void 0:e.enabled)||!1}updateUIConfig(s){this.config.ui={...this.config.ui,...s}}getUIConfig(){return this.deepClone(this.config.ui)}updateSecurityConfig(s){this.config.security={...this.config.security,...s}}getSecurityConfig(){return this.deepClone(this.config.security)}async resetConfig(){this.config=this.deepClone(I),await this.saveConfig(),new $.Notice("\u914D\u7F6E\u5DF2\u91CD\u7F6E\u4E3A\u9ED8\u8BA4\u503C")}exportConfig(){let s=this.deepClone(this.config);return s.llm.apiKey="",s.tools.web.apiKey="",JSON.stringify(s,null,2)}async importConfig(s){try{let e=JSON.parse(s);if(!this.isValidConfigStructure(e))throw new Error("\u65E0\u6548\u7684\u914D\u7F6E\u683C\u5F0F");let t=this.config.llm.apiKey,r=this.config.tools.web.apiKey;this.config=this.mergeConfig(I,e),this.config.llm.apiKey||(this.config.llm.apiKey=t),this.config.tools.web.apiKey||(this.config.tools.web.apiKey=r),await this.saveConfig(),new $.Notice("\u914D\u7F6E\u5BFC\u5165\u6210\u529F")}catch(e){throw console.error("Failed to import config:",e),new $.Notice("\u914D\u7F6E\u5BFC\u5165\u5931\u8D25: "+e.message),e}}validateConfig(){if(this.config.llm.apiKey&&this.config.llm.apiKey.trim()){let s=R.validateConfig(this.config.llm);s.valid||console.warn("LLM\u914D\u7F6E\u9A8C\u8BC1\u5931\u8D25:",s.errors)}this.config.tools.vault.chunkSize<100&&(this.config.tools.vault.chunkSize=1e3),this.config.tools.javascript.timeout<1e3&&(this.config.tools.javascript.timeout=5e3),this.config.security.maxRequestsPerMinute<1&&(this.config.security.maxRequestsPerMinute=60)}async migrateConfig(s){console.log("Migrating config from version",s.version,"to",this.configVersion),this.config=this.mergeConfig(I,s),new $.Notice("\u914D\u7F6E\u5DF2\u5347\u7EA7\u5230\u65B0\u7248\u672C")}async encryptSensitiveData(s){s.llm.apiKey&&(s.llm.apiKey=btoa(s.llm.apiKey)),s.tools.web.apiKey&&(s.tools.web.apiKey=btoa(s.tools.web.apiKey))}async decryptSensitiveData(){try{this.config.llm.apiKey&&(this.config.llm.apiKey=atob(this.config.llm.apiKey)),this.config.tools.web.apiKey&&(this.config.tools.web.apiKey=atob(this.config.tools.web.apiKey))}catch(s){console.error("Failed to decrypt sensitive data:",s)}}isValidConfigStructure(s){return s&&typeof s=="object"&&s.llm&&s.tools&&s.ui&&s.security}deepClone(s){return JSON.parse(JSON.stringify(s))}mergeConfig(s,e){let t={...s};for(let r in e)e[r]!==void 0&&(typeof e[r]=="object"&&e[r]!==null&&!Array.isArray(e[r])?t[r]=this.mergeConfig(t[r]||{},e[r]):t[r]=e[r]);return t}};var J=class{constructor(){this.templates=new Map}registerTemplate(s){this.templates.set(s.id,s)}getTemplate(s){return this.templates.get(s)}listTemplates(s){let e=Array.from(this.templates.values());return s?e.filter(t=>t.category===s):e}render(s,e){let t=this.templates.get(s);if(!t)throw new Error(`Template not found: ${s}`);return this.validateContext(t,e),this.renderTemplate(t.template,e)}renderMultiple(s,e){return s.map(t=>this.render(t,e))}validateContext(s,e){for(let t of s.variables){if(t.required&&!(t.name in e))throw new Error(`Required variable missing: ${t.name}`);if(t.name in e){let r=e[t.name];if(!this.validateVariableType(r,t.type))throw new Error(`Invalid type for variable ${t.name}: expected ${t.type}`)}}}validateVariableType(s,e){switch(e){case"string":return typeof s=="string";case"number":return typeof s=="number";case"boolean":return typeof s=="boolean";case"array":return Array.isArray(s);case"object":return typeof s=="object"&&s!==null&&!Array.isArray(s);default:return!0}}renderTemplate(s,e){let t=s;return t=this.processConditionals(t,e),t=this.processLoops(t,e),t=this.processVariables(t,e),t.trim()}processConditionals(s,e){let t=/\{\{#if\s+(\w+)\}\}([\s\S]*?)\{\{\/if\}\}/g;return s.replace(t,(r,o,n)=>e[o]?n:"")}processLoops(s,e){let t=/\{\{#each\s+(\w+)\}\}([\s\S]*?)\{\{\/each\}\}/g;return s.replace(t,(r,o,n)=>{let i=e[o];return Array.isArray(i)?i.map((a,c)=>{let l=n;if(l=l.replace(/\{\{this\}\}/g,String(a)),l=l.replace(/\{\{@index\}\}/g,String(c)),typeof a=="object"&&a!==null)for(let[u,p]of Object.entries(a)){let f=new RegExp(`\\{\\{${u}\\}\\}`,"g");l=l.replace(f,String(p))}return l}).join(""):""})}processVariables(s,e){let t=/\{\{(\w+)\}\}/g;return s.replace(t,(r,o)=>{let n=e[o];return n!==void 0?String(n):r})}static createBuilder(){return new b}},b=class{constructor(){this.template={variables:[],version:"1.0.0",createdAt:new Date,updatedAt:new Date}}id(s){return this.template.id=s,this}name(s){return this.template.name=s,this}description(s){return this.template.description=s,this}category(s){return this.template.category=s,this}content(s){return this.template.template=s,this}variable(s){return this.template.variables.push(s),this}version(s){return this.template.version=s,this}build(){if(!this.template.id||!this.template.name||!this.template.template)throw new Error("Template id, name, and content are required");return this.template}},M={USER_INPUT:{name:"userInput",type:"string",required:!0,description:"\u7528\u6237\u8F93\u5165\u7684\u5185\u5BB9"},CONTEXT:{name:"context",type:"string",required:!1,description:"\u4E0A\u4E0B\u6587\u4FE1\u606F"},TOOLS:{name:"tools",type:"array",required:!1,description:"\u53EF\u7528\u5DE5\u5177\u5217\u8868"},RESULTS:{name:"results",type:"array",required:!1,description:"\u5DE5\u5177\u6267\u884C\u7ED3\u679C"},ERROR:{name:"error",type:"string",required:!0,description:"\u9519\u8BEF\u4FE1\u606F"}};var S=class{static getAllTemplates(){return[this.SYSTEM_PROMPT,this.TASK_ANALYSIS,this.TOOL_SELECTION,this.VAULT_QUERY,this.CONTENT_SUMMARY,this.ERROR_HANDLING,this.TOOL_RESULT_PROCESSING]}};S.SYSTEM_PROMPT=new b().id("system_prompt").name("\u7CFB\u7EDF\u63D0\u793A").description("\u5B9A\u4E49AI\u52A9\u624B\u7684\u89D2\u8272\u548C\u57FA\u672C\u884C\u4E3A\u89C4\u8303").category("system").content(`\u4F60\u662F\u4E00\u4E2A\u667A\u80FD\u7684Obsidian\u52A9\u624B\uFF0C\u540D\u4E3AAI Coach Advanced\u3002\u4F60\u7684\u4E3B\u8981\u804C\u8D23\u662F\uFF1A

1. **\u7406\u89E3\u7528\u6237\u610F\u56FE**\uFF1A\u51C6\u786E\u7406\u89E3\u7528\u6237\u7684\u81EA\u7136\u8BED\u8A00\u6307\u4EE4\uFF0C\u8BC6\u522B\u7528\u6237\u60F3\u8981\u5B8C\u6210\u7684\u4EFB\u52A1\u3002

2. **\u79EF\u6781\u4F7F\u7528\u5DE5\u5177**\uFF1A\u5F53\u7528\u6237\u7684\u8BF7\u6C42\u9700\u8981\u83B7\u53D6\u4FE1\u606F\u3001\u6267\u884C\u64CD\u4F5C\u6216\u5904\u7406\u6570\u636E\u65F6\uFF0C\u5FC5\u987B\u4E3B\u52A8\u8C03\u7528\u76F8\u5E94\u7684\u5DE5\u5177\u3002

3. **\u77E5\u8BC6\u5E93\u67E5\u8BE2**\uFF1A\u5F53\u9700\u8981\u67E5\u627E\u4FE1\u606F\u65F6\uFF0C\u4F18\u5148\u641C\u7D22\u7528\u6237\u7684Obsidian Vault\u7B14\u8BB0\u5185\u5BB9\u3002

4. **\u5185\u5BB9\u751F\u6210**\uFF1A\u57FA\u4E8E\u67E5\u8BE2\u7ED3\u679C\u548C\u7528\u6237\u9700\u6C42\uFF0C\u751F\u6210\u6709\u7528\u3001\u51C6\u786E\u7684\u56DE\u7B54\u6216\u5185\u5BB9\u3002

**\u91CD\u8981\uFF1A\u5DE5\u5177\u8C03\u7528\u6307\u5BFC**
- \u5F53\u7528\u6237\u8BE2\u95EE\u7B14\u8BB0\u5185\u5BB9\u3001\u67E5\u627E\u4FE1\u606F\u65F6 \u2192 \u4F7F\u7528 vault_query \u5DE5\u5177
- \u5F53\u9700\u8981\u6700\u65B0\u4FE1\u606F\u3001\u7F51\u7EDC\u641C\u7D22\u65F6 \u2192 \u4F7F\u7528 web_search \u5DE5\u5177
- \u5F53\u9700\u8981\u8BA1\u7B97\u3001\u6570\u636E\u5904\u7406\u3001\u4EE3\u7801\u6267\u884C\u65F6 \u2192 \u4F7F\u7528 javascript_executor \u5DE5\u5177
- \u5F53\u9700\u8981\u7BA1\u7406\u63D2\u4EF6\u3001\u6267\u884C\u547D\u4EE4\u65F6 \u2192 \u4F7F\u7528 plugin_manager \u5DE5\u5177
- \u4E0D\u8981\u4EC5\u51ED\u8BB0\u5FC6\u56DE\u7B54\uFF0C\u8981\u901A\u8FC7\u5DE5\u5177\u83B7\u53D6\u51C6\u786E\u4FE1\u606F

**\u884C\u4E3A\u51C6\u5219**\uFF1A
- \u59CB\u7EC8\u4EE5\u7528\u6237\u7684\u9700\u6C42\u4E3A\u4E2D\u5FC3
- \u4E3B\u52A8\u4F7F\u7528\u5DE5\u5177\u83B7\u53D6\u51C6\u786E\u4FE1\u606F
- \u5728\u4E0D\u786E\u5B9A\u65F6\u4E3B\u52A8\u8BE2\u95EE\u6F84\u6E05
- \u4FDD\u62A4\u7528\u6237\u9690\u79C1\u548C\u6570\u636E\u5B89\u5168
- \u627F\u8BA4\u81EA\u5DF1\u7684\u5C40\u9650\u6027

**\u5F53\u524D\u53EF\u7528\u5DE5\u5177**\uFF1A
{{#if tools}}
{{#each tools}}
- {{name}}: {{description}}
{{/each}}
{{/if}}

\u8BF7\u6839\u636E\u7528\u6237\u7684\u6307\u4EE4\uFF0C\u79EF\u6781\u9009\u62E9\u5408\u9002\u7684\u5DE5\u5177\u5E76\u63D0\u4F9B\u5E2E\u52A9\u3002`).variable(M.TOOLS).build(),S.TASK_ANALYSIS=new b().id("task_analysis").name("\u4EFB\u52A1\u5206\u6790").description("\u5206\u6790\u7528\u6237\u8F93\u5165\uFF0C\u8BC6\u522B\u4EFB\u52A1\u7C7B\u578B\u548C\u6240\u9700\u5DE5\u5177").category("task").content(`\u8BF7\u5206\u6790\u4EE5\u4E0B\u7528\u6237\u8F93\u5165\uFF0C\u786E\u5B9A\u9700\u8981\u6267\u884C\u7684\u4EFB\u52A1\uFF1A

\u7528\u6237\u8F93\u5165\uFF1A{{userInput}}

{{#if context}}
\u4E0A\u4E0B\u6587\u4FE1\u606F\uFF1A{{context}}
{{/if}}

\u8BF7\u6309\u4EE5\u4E0B\u683C\u5F0F\u5206\u6790\uFF1A

**\u4EFB\u52A1\u7C7B\u578B**\uFF1A[\u67E5\u8BE2\u4FE1\u606F/\u521B\u5EFA\u5185\u5BB9/\u6267\u884C\u64CD\u4F5C/\u5176\u4ED6]

**\u4E3B\u8981\u76EE\u6807**\uFF1A[\u7B80\u8981\u63CF\u8FF0\u7528\u6237\u60F3\u8981\u8FBE\u6210\u7684\u76EE\u6807]

**\u6240\u9700\u4FE1\u606F**\uFF1A[\u5217\u51FA\u5B8C\u6210\u4EFB\u52A1\u9700\u8981\u7684\u4FE1\u606F]

**\u5EFA\u8BAE\u5DE5\u5177**\uFF1A[\u6839\u636E\u4EFB\u52A1\u9700\u6C42\u63A8\u8350\u4F7F\u7528\u7684\u5DE5\u5177]

**\u6267\u884C\u6B65\u9AA4**\uFF1A
1. [\u7B2C\u4E00\u6B65]
2. [\u7B2C\u4E8C\u6B65]
3. [\u540E\u7EED\u6B65\u9AA4...]

\u8BF7\u57FA\u4E8E\u8FD9\u4E2A\u5206\u6790\u5236\u5B9A\u6267\u884C\u8BA1\u5212\u3002`).variable(M.USER_INPUT).variable(M.CONTEXT).build(),S.TOOL_SELECTION=new b().id("tool_selection").name("\u5DE5\u5177\u9009\u62E9").description("\u6839\u636E\u4EFB\u52A1\u9700\u6C42\u9009\u62E9\u5408\u9002\u7684\u5DE5\u5177").category("tool").content(`\u6839\u636E\u4EE5\u4E0B\u4EFB\u52A1\u9700\u6C42\uFF0C\u9009\u62E9\u6700\u5408\u9002\u7684\u5DE5\u5177\uFF1A

**\u4EFB\u52A1\u63CF\u8FF0**\uFF1A{{taskDescription}}

**\u53EF\u7528\u5DE5\u5177**\uFF1A
{{#each tools}}
- **{{name}}**\uFF1A{{description}}
  \u53C2\u6570\uFF1A{{#each parameters}}{{name}} ({{type}}){{#unless @last}}, {{/unless}}{{/each}}
{{/each}}

**\u9009\u62E9\u6807\u51C6**\uFF1A
1. \u5DE5\u5177\u529F\u80FD\u662F\u5426\u5339\u914D\u4EFB\u52A1\u9700\u6C42
2. \u5DE5\u5177\u7684\u53EF\u9760\u6027\u548C\u5B89\u5168\u6027
3. \u6267\u884C\u6548\u7387\u548C\u7528\u6237\u4F53\u9A8C

\u8BF7\u9009\u62E9\u6700\u5408\u9002\u7684\u5DE5\u5177\u5E76\u8BF4\u660E\u7406\u7531\u3002\u5982\u679C\u9700\u8981\u591A\u4E2A\u5DE5\u5177\u914D\u5408\uFF0C\u8BF7\u8BF4\u660E\u8C03\u7528\u987A\u5E8F\u3002

**\u9009\u62E9\u7ED3\u679C**\uFF1A
\u5DE5\u5177\u540D\u79F0\uFF1A[\u9009\u62E9\u7684\u5DE5\u5177]
\u8C03\u7528\u53C2\u6570\uFF1A[\u5177\u4F53\u53C2\u6570\u503C]
\u9009\u62E9\u7406\u7531\uFF1A[\u4E3A\u4EC0\u4E48\u9009\u62E9\u8FD9\u4E2A\u5DE5\u5177]`).variable({name:"taskDescription",type:"string",required:!0,description:"\u4EFB\u52A1\u63CF\u8FF0"}).variable(M.TOOLS).build(),S.VAULT_QUERY=new b().id("vault_query").name("Vault\u67E5\u8BE2").description("\u6784\u9020Vault\u77E5\u8BC6\u5E93\u67E5\u8BE2").category("tool").content(`\u57FA\u4E8E\u7528\u6237\u95EE\u9898\u6784\u9020Vault\u67E5\u8BE2\uFF1A

**\u7528\u6237\u95EE\u9898**\uFF1A{{userInput}}

**\u67E5\u8BE2\u7B56\u7565**\uFF1A
1. \u63D0\u53D6\u5173\u952E\u8BCD\u548C\u6982\u5FF5
2. \u8003\u8651\u540C\u4E49\u8BCD\u548C\u76F8\u5173\u672F\u8BED
3. \u6784\u9020\u6709\u6548\u7684\u641C\u7D22\u67E5\u8BE2

**\u67E5\u8BE2\u5173\u952E\u8BCD**\uFF1A[\u63D0\u53D6\u7684\u5173\u952E\u8BCD]

**\u641C\u7D22\u67E5\u8BE2**\uFF1A[\u4F18\u5316\u540E\u7684\u67E5\u8BE2\u8BED\u53E5]

**\u9884\u671F\u7ED3\u679C**\uFF1A[\u671F\u671B\u627E\u5230\u4EC0\u4E48\u7C7B\u578B\u7684\u4FE1\u606F]`).variable(M.USER_INPUT).build(),S.CONTENT_SUMMARY=new b().id("content_summary").name("\u5185\u5BB9\u603B\u7ED3").description("\u603B\u7ED3\u67E5\u8BE2\u7ED3\u679C\u6216\u751F\u6210\u5185\u5BB9").category("response").content(`\u57FA\u4E8E\u4EE5\u4E0B\u4FE1\u606F\u4E3A\u7528\u6237\u63D0\u4F9B\u56DE\u7B54\uFF1A

**\u7528\u6237\u95EE\u9898**\uFF1A{{userInput}}

**\u67E5\u8BE2\u7ED3\u679C**\uFF1A
{{#each results}}
**\u6765\u6E90**\uFF1A{{source}}
**\u5185\u5BB9**\uFF1A{{content}}
**\u76F8\u5173\u6027**\uFF1A{{score}}

{{/each}}

\u8BF7\u57FA\u4E8E\u8FD9\u4E9B\u4FE1\u606F\u63D0\u4F9B\u4E00\u4E2A\u51C6\u786E\u3001\u6709\u7528\u7684\u56DE\u7B54\u3002\u8981\u6C42\uFF1A

1. **\u76F4\u63A5\u56DE\u7B54**\uFF1A\u9996\u5148\u76F4\u63A5\u56DE\u7B54\u7528\u6237\u7684\u95EE\u9898
2. **\u652F\u6491\u4FE1\u606F**\uFF1A\u5F15\u7528\u76F8\u5173\u7684\u7B14\u8BB0\u5185\u5BB9\u4F5C\u4E3A\u652F\u6491
3. **\u6765\u6E90\u6807\u6CE8**\uFF1A\u6807\u660E\u4FE1\u606F\u6765\u6E90\u7684\u7B14\u8BB0\u6587\u4EF6
4. **\u8865\u5145\u5EFA\u8BAE**\uFF1A\u5982\u679C\u6709\u76F8\u5173\u7684\u8865\u5145\u4FE1\u606F\u6216\u5EFA\u8BAE\uFF0C\u8BF7\u63D0\u4F9B

**\u56DE\u7B54\u683C\u5F0F**\uFF1A
[\u76F4\u63A5\u56DE\u7B54\u7528\u6237\u95EE\u9898]

**\u8BE6\u7EC6\u4FE1\u606F**\uFF1A
[\u57FA\u4E8E\u67E5\u8BE2\u7ED3\u679C\u7684\u8BE6\u7EC6\u8BF4\u660E]

**\u76F8\u5173\u7B14\u8BB0**\uFF1A
{{#each results}}
- [[{{source}}]]\uFF1A{{content}}
{{/each}}

{{#if suggestions}}
**\u5EFA\u8BAE**\uFF1A
{{suggestions}}
{{/if}}`).variable(M.USER_INPUT).variable(M.RESULTS).variable({name:"suggestions",type:"string",required:!1,description:"\u8865\u5145\u5EFA\u8BAE"}).build(),S.ERROR_HANDLING=new b().id("error_handling").name("\u9519\u8BEF\u5904\u7406").description("\u5904\u7406\u5DE5\u5177\u8C03\u7528\u9519\u8BEF\u6216\u5F02\u5E38\u60C5\u51B5").category("error").content(`\u5904\u7406\u6267\u884C\u8FC7\u7A0B\u4E2D\u7684\u9519\u8BEF\uFF1A

**\u9519\u8BEF\u4FE1\u606F**\uFF1A{{error}}

**\u7528\u6237\u8BF7\u6C42**\uFF1A{{userInput}}

**\u9519\u8BEF\u5206\u6790**\uFF1A
[\u5206\u6790\u9519\u8BEF\u539F\u56E0\u548C\u53EF\u80FD\u7684\u89E3\u51B3\u65B9\u6848]

**\u7528\u6237\u53CD\u9988**\uFF1A
\u62B1\u6B49\uFF0C\u5728\u5904\u7406\u60A8\u7684\u8BF7\u6C42\u65F6\u9047\u5230\u4E86\u95EE\u9898\uFF1A{{error}}

**\u53EF\u80FD\u7684\u89E3\u51B3\u65B9\u6848**\uFF1A
1. [\u5EFA\u8BAE\u7684\u89E3\u51B3\u65B9\u68481]
2. [\u5EFA\u8BAE\u7684\u89E3\u51B3\u65B9\u68482]
3. [\u5176\u4ED6\u66FF\u4EE3\u65B9\u6848]

\u60A8\u53EF\u4EE5\u5C1D\u8BD5\uFF1A
- \u91CD\u65B0\u8868\u8FF0\u60A8\u7684\u95EE\u9898
- \u68C0\u67E5\u76F8\u5173\u8BBE\u7F6E
- \u6216\u8005\u6211\u53EF\u4EE5\u7528\u5176\u4ED6\u65B9\u5F0F\u5E2E\u52A9\u60A8

\u8BF7\u544A\u8BC9\u6211\u60A8\u5E0C\u671B\u5982\u4F55\u7EE7\u7EED\u3002`).variable(M.ERROR).variable(M.USER_INPUT).build(),S.TOOL_RESULT_PROCESSING=new b().id("tool_result_processing").name("\u5DE5\u5177\u7ED3\u679C\u5904\u7406").description("\u5904\u7406\u548C\u89E3\u91CA\u5DE5\u5177\u8C03\u7528\u7ED3\u679C").category("response").content(`\u5904\u7406\u5DE5\u5177\u8C03\u7528\u7ED3\u679C\uFF1A

**\u5DE5\u5177\u540D\u79F0**\uFF1A{{toolName}}
**\u8C03\u7528\u53C2\u6570**\uFF1A{{parameters}}
**\u6267\u884C\u7ED3\u679C**\uFF1A{{result}}
**\u7528\u6237\u539F\u59CB\u8BF7\u6C42**\uFF1A{{userInput}}

**\u7ED3\u679C\u5206\u6790**\uFF1A
{{#if result.success}}
\u5DE5\u5177\u6267\u884C\u6210\u529F\u3002\u7ED3\u679C\u6570\u636E\uFF1A{{result.data}}

**\u7528\u6237\u56DE\u590D**\uFF1A
[\u57FA\u4E8E\u7ED3\u679C\u4E3A\u7528\u6237\u63D0\u4F9B\u6709\u7528\u7684\u56DE\u7B54]
{{else}}
\u5DE5\u5177\u6267\u884C\u5931\u8D25\u3002\u9519\u8BEF\u4FE1\u606F\uFF1A{{result.error}}

**\u7528\u6237\u56DE\u590D**\uFF1A
\u5F88\u62B1\u6B49\uFF0C{{toolName}}\u5DE5\u5177\u6267\u884C\u65F6\u51FA\u73B0\u95EE\u9898\uFF1A{{result.error}}

\u8BA9\u6211\u5C1D\u8BD5\u5176\u4ED6\u65B9\u6CD5\u6765\u5E2E\u52A9\u60A8\u3002
{{/if}}`).variable({name:"toolName",type:"string",required:!0,description:"\u5DE5\u5177\u540D\u79F0"}).variable({name:"parameters",type:"object",required:!0,description:"\u8C03\u7528\u53C2\u6570"}).variable({name:"result",type:"object",required:!0,description:"\u6267\u884C\u7ED3\u679C"}).variable(M.USER_INPUT).build();var X=class{constructor(){d(this,"templateEngine");d(this,"availableTools",[]);d(this,"conversationHistory",[]);this.templateEngine=new J,this.initializeBaseTemplates()}initializeBaseTemplates(){S.getAllTemplates().forEach(e=>{this.templateEngine.registerTemplate(e)})}setAvailableTools(s){this.availableTools=s}setConversationHistory(s){this.conversationHistory=s}generateSystemPrompt(){let s=this.availableTools.map(e=>({name:e.name,description:e.description}));return this.templateEngine.render("system_prompt",{tools:s})}generateTaskAnalysisPrompt(s,e){return this.templateEngine.render("task_analysis",{userInput:s,context:e||this.buildContextFromHistory()})}generateToolSelectionPrompt(s){let e=this.availableTools.map(t=>({name:t.name,description:t.description,parameters:Object.entries(t.parameters.properties||{}).map(([r,o])=>({name:r,type:o.type,description:o.description}))}));return this.templateEngine.render("tool_selection",{taskDescription:s,tools:e})}generateVaultQueryPrompt(s){return this.templateEngine.render("vault_query",{userInput:s})}generateContentSummaryPrompt(s,e,t){return this.templateEngine.render("content_summary",{userInput:s,results:e,suggestions:t})}generateErrorHandlingPrompt(s,e){return this.templateEngine.render("error_handling",{userInput:s,error:e})}generateToolResultProcessingPrompt(s,e,t,r){return this.templateEngine.render("tool_result_processing",{userInput:s,toolName:e,parameters:t,result:r})}generateConversationPrompt(s,e=!0,t=5){let r=this.generateSystemPrompt(),o=[];return e&&this.conversationHistory.length>0&&this.conversationHistory.slice(-t).flatMap(i=>i.messages.slice(-2)).forEach(i=>{o.push({role:i.role,content:i.content})}),o.push({role:"user",content:s}),{systemPrompt:r,messages:o}}generateTaskSpecificPrompt(s,e,t){switch(s){case"query":return this.generateVaultQueryPrompt(e);case"create":return this.generateCreationPrompt(e,t);case"analyze":return this.generateAnalysisPrompt(e,t);case"execute":return this.generateExecutionPrompt(e,t);default:return this.generateTaskAnalysisPrompt(e)}}generateCreationPrompt(s,e){return`\u57FA\u4E8E\u4EE5\u4E0B\u8981\u6C42\u521B\u5EFA\u5185\u5BB9\uFF1A

\u7528\u6237\u9700\u6C42\uFF1A${s}

${e?`\u53C2\u8003\u4FE1\u606F\uFF1A${JSON.stringify(e,null,2)}`:""}

\u8BF7\u521B\u5EFA\u7B26\u5408\u8981\u6C42\u7684\u5185\u5BB9\uFF0C\u786E\u4FDD\uFF1A
1. \u5185\u5BB9\u7ED3\u6784\u6E05\u6670
2. \u4FE1\u606F\u51C6\u786E\u5B8C\u6574
3. \u683C\u5F0F\u7B26\u5408Obsidian Markdown\u89C4\u8303
4. \u5305\u542B\u9002\u5F53\u7684\u94FE\u63A5\u548C\u6807\u7B7E`}generateAnalysisPrompt(s,e){return`\u8BF7\u5206\u6790\u4EE5\u4E0B\u5185\u5BB9\uFF1A

\u5206\u6790\u76EE\u6807\uFF1A${s}

${e?`\u76F8\u5173\u6570\u636E\uFF1A${JSON.stringify(e,null,2)}`:""}

\u8BF7\u63D0\u4F9B\uFF1A
1. \u5173\u952E\u53D1\u73B0\u548C\u6D1E\u5BDF
2. \u6570\u636E\u8D8B\u52BF\u548C\u6A21\u5F0F
3. \u7ED3\u8BBA\u548C\u5EFA\u8BAE
4. \u652F\u6491\u8BC1\u636E`}generateExecutionPrompt(s,e){return`\u6267\u884C\u4EE5\u4E0B\u4EFB\u52A1\uFF1A

\u4EFB\u52A1\u63CF\u8FF0\uFF1A${s}

${e?`\u6267\u884C\u73AF\u5883\uFF1A${JSON.stringify(e,null,2)}`:""}

\u8BF7\uFF1A
1. \u786E\u8BA4\u4EFB\u52A1\u7406\u89E3\u6B63\u786E
2. \u9009\u62E9\u5408\u9002\u7684\u5DE5\u5177\u548C\u65B9\u6CD5
3. \u6309\u6B65\u9AA4\u6267\u884C
4. \u62A5\u544A\u6267\u884C\u7ED3\u679C`}buildContextFromHistory(){return this.conversationHistory.length===0?"":this.conversationHistory.slice(-1)[0].messages.slice(-3).map(t=>`${t.role}: ${t.content}`).join(`
`)}registerCustomTemplate(s){this.templateEngine.registerTemplate(s)}getTemplateEngine(){return this.templateEngine}optimizePromptLength(s,e=4e3){if(s.length<=e)return s;let t=Math.floor(e*.6),r=Math.floor(e*.3),o=s.substring(0,t),n=s.substring(s.length-r);return`${o}

[... \u5185\u5BB9\u5DF2\u622A\u65AD ...]

${n}`}validatePromptQuality(s){let e=[],t=[],r=100;return s.length<50?(e.push("\u63D0\u793A\u8FC7\u77ED\uFF0C\u53EF\u80FD\u7F3A\u5C11\u5FC5\u8981\u4FE1\u606F"),r-=20):s.length>8e3&&(e.push("\u63D0\u793A\u8FC7\u957F\uFF0C\u53EF\u80FD\u5F71\u54CD\u5904\u7406\u6548\u7387"),r-=10),!s.includes("\uFF1A")&&!s.includes(":")&&(t.push("\u5EFA\u8BAE\u6DFB\u52A0\u660E\u786E\u7684\u6307\u4EE4\u7ED3\u6784"),r-=5),(s.includes("{{")||s.includes("}}"))&&(e.push("\u5B58\u5728\u672A\u66FF\u6362\u7684\u6A21\u677F\u53D8\u91CF"),r-=15),{score:Math.max(0,r),issues:e,suggestions:t}}};var Z=class{constructor(){d(this,"tools",new Map);d(this,"toolsByCategory",new Map);d(this,"executionHistory",[]);d(this,"maxHistorySize",1e3);["vault","web","plugin","javascript","memory","utility","ai","custom"].forEach(e=>{this.toolsByCategory.set(e,new Set)})}async register(s){try{if(this.tools.has(s.name))throw new Error(`Tool with name '${s.name}' already exists`);this.validateToolConfig(s),s.initialize&&await s.initialize(),this.tools.set(s.name,s);let e=this.toolsByCategory.get(s.category);e&&e.add(s.name),console.log(`Tool registered: ${s.name} (${s.category})`)}catch(e){throw console.error(`Failed to register tool ${s.name}:`,e),e}}async unregister(s){let e=this.tools.get(s);if(!e)throw new Error(`Tool not found: ${s}`);try{e.cleanup&&await e.cleanup(),this.tools.delete(s);let t=this.toolsByCategory.get(e.category);t&&t.delete(s),console.log(`Tool unregistered: ${s}`)}catch(t){throw console.error(`Failed to unregister tool ${s}:`,t),t}}get(s){return this.tools.get(s)}list(s){if(s){let e=this.toolsByCategory.get(s);return e?Array.from(e).map(t=>this.tools.get(t)).filter(t=>t!==void 0):[]}return Array.from(this.tools.values())}async execute(s,e,t){let r=Date.now();console.log("\u{1F527} ToolRegistry.execute \u5F00\u59CB\u6267\u884C"),console.log("\u{1F527} \u5DE5\u5177\u540D\u79F0:",s),console.log("\u{1F527} \u5DE5\u5177\u53C2\u6570:",e),console.log("\u{1F527} \u5DF2\u6CE8\u518C\u5DE5\u5177:",Array.from(this.tools.keys()));let o=this.tools.get(s);if(!o){console.log("\u274C \u5DE5\u5177\u672A\u627E\u5230:",s);let n={success:!1,error:`Tool not found: ${s}`,metadata:{executionTime:Date.now()-r}};return this.recordExecution(s,e,n,t),n}console.log("\u2705 \u627E\u5230\u5DE5\u5177:",o.name,"\u7C7B\u522B:",o.category);try{console.log("\u{1F527} \u8C03\u7528\u5DE5\u5177\u7684execute\u65B9\u6CD5...");let n=await o.execute(e,t);return console.log("\u{1F527} \u5DE5\u5177\u6267\u884C\u5B8C\u6210:",{success:n.success,hasData:!!n.data,hasError:!!n.error,executionTime:Date.now()-r}),this.recordExecution(s,e,n,t),n}catch(n){console.log("\u274C \u5DE5\u5177\u6267\u884C\u5F02\u5E38:",n.message),console.log("\u274C \u5F02\u5E38\u8BE6\u60C5:",n);let i={success:!1,error:n instanceof Error?n.message:String(n),metadata:{executionTime:Date.now()-r}};return this.recordExecution(s,e,i,t),i}}async validate(s,e){let t=this.tools.get(s);return t?t.validate?await t.validate(e):{valid:!0,errors:[],warnings:[]}:{valid:!1,errors:[`Tool not found: ${s}`],warnings:[]}}getToolDefinition(s){let e=this.tools.get(s);if(e)return{name:e.name,description:e.description,category:e.category,parameters:e.parameters,permissions:e.permissions,metadata:e.metadata}}getToolDefinitions(s){return this.list(s).map(t=>({name:t.name,description:t.description,category:t.category,parameters:t.parameters,permissions:t.permissions,metadata:t.metadata}))}async registerBatch(s){let t=(await Promise.allSettled(s.map(r=>this.register(r)))).map((r,o)=>({result:r,tool:s[o]})).filter(({result:r})=>r.status==="rejected").map(({result:r,tool:o})=>({tool:o.name,error:r.reason}));t.length>0&&console.warn("Some tools failed to register:",t)}getStats(){let s=this.tools.size,e={};for(let[r,o]of this.toolsByCategory.entries())e[r]=o.size;let t=this.executionHistory.filter(r=>Date.now()-r.timestamp.getTime()<24*60*60*1e3).length;return{totalTools:s,toolsByCategory:e,recentExecutions:t,totalExecutions:this.executionHistory.length}}getExecutionHistory(s){let e=[...this.executionHistory].reverse();return s?e.slice(0,s):e}clearExecutionHistory(){this.executionHistory=[]}validateToolConfig(s){if(!s.name||typeof s.name!="string")throw new Error("Tool name is required and must be a string");if(!s.description||typeof s.description!="string")throw new Error("Tool description is required and must be a string");if(!s.category)throw new Error("Tool category is required");if(!s.parameters||typeof s.parameters!="object")throw new Error("Tool parameters are required");if(!s.permissions||typeof s.permissions!="object")throw new Error("Tool permissions are required");if(!s.metadata||typeof s.metadata!="object")throw new Error("Tool metadata is required")}recordExecution(s,e,t,r){let o={toolName:s,args:e,result:t,context:r,timestamp:new Date};this.executionHistory.push(o),this.executionHistory.length>this.maxHistorySize&&(this.executionHistory=this.executionHistory.slice(-this.maxHistorySize))}};var ee=class{constructor(s,e){d(this,"registry");d(this,"executionQueue",[]);d(this,"isProcessing",!1);d(this,"maxConcurrentExecutions",3);d(this,"currentExecutions",new Set);d(this,"rateLimiter");this.registry=s,this.maxConcurrentExecutions=(e==null?void 0:e.maxConcurrentExecutions)||3,this.rateLimiter=new fe((e==null?void 0:e.rateLimit)||{maxRequests:60,windowMs:6e4})}async executeTool(s,e,t){var o;let r=this.generateExecutionId();try{if(!this.rateLimiter.checkLimit((t==null?void 0:t.userId)||"anonymous"))return{success:!1,error:"\u8BF7\u6C42\u9891\u7387\u8FC7\u9AD8\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5",metadata:{executionId:r,rateLimited:!0}};let n=this.registry.get(s);if(!n)return{success:!1,error:`\u5DE5\u5177\u4E0D\u5B58\u5728: ${s}`,metadata:{executionId:r}};if(this.currentExecutions.size>=this.maxConcurrentExecutions)return{success:!1,error:"\u7CFB\u7EDF\u7E41\u5FD9\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5",metadata:{executionId:r,concurrencyLimited:!0}};let i=await this.registry.validate(s,e);if(!i.valid)return{success:!1,error:`\u53C2\u6570\u9A8C\u8BC1\u5931\u8D25: ${i.errors.join(", ")}`,metadata:{executionId:r,validationErrors:i.errors,validationWarnings:i.warnings}};if(t){let a=this.checkToolPermissions(n,t);if(!a.allowed)return{success:!1,error:`\u6743\u9650\u4E0D\u8DB3: ${a.reason}`,metadata:{executionId:r,permissionDenied:!0}};if(n.permissions.requiresConfirmation&&!((o=t.config)!=null&&o.confirmed))return{success:!1,error:"\u6B64\u64CD\u4F5C\u9700\u8981\u7528\u6237\u786E\u8BA4",metadata:{executionId:r,requiresConfirmation:!0,toolName:s,args:e}}}this.currentExecutions.add(r);try{let a=await this.registry.execute(s,e,{...t,requestId:r});return this.postProcessResult(a,n,e)}finally{this.currentExecutions.delete(r)}}catch(n){return this.currentExecutions.delete(r),{success:!1,error:n instanceof Error?n.message:String(n),metadata:{executionId:r,unexpectedError:!0}}}}async executeToolsBatch(s,e){let t=[];for(let r of s){let o=await this.executeTool(r.toolName,r.args,e);if(t.push(o),!o.success&&r.stopOnFailure)break}return t}async queueToolExecution(s,e,t,r="normal"){let o=this.generateExecutionId(),n={id:o,toolName:s,args:e,context:t,priority:r,createdAt:new Date,status:"queued"};return this.executionQueue.push(n),this.sortQueue(),this.isProcessing||this.processQueue(),o}getExecutionStatus(s){if(this.currentExecutions.has(s))return{status:"running",executionId:s};let e=this.executionQueue.find(t=>t.id===s);return e?{status:e.status,executionId:s,queuePosition:this.executionQueue.indexOf(e)+1}:null}cancelExecution(s){let e=this.executionQueue.findIndex(t=>t.id===s);return e!==-1?(this.executionQueue.splice(e,1),!0):!1}getQueueStatus(){return{queueLength:this.executionQueue.length,currentExecutions:this.currentExecutions.size,maxConcurrentExecutions:this.maxConcurrentExecutions,isProcessing:this.isProcessing}}checkToolPermissions(s,e){for(let r of s.permissions.required)if(!e.permissions.includes(r))return{allowed:!1,reason:`\u7F3A\u5C11\u5FC5\u9700\u6743\u9650: ${r}`};if(s.permissions.dangerous&&!e.permissions.includes("dangerous_operations"))return{allowed:!1,reason:"\u6B64\u5DE5\u5177\u6267\u884C\u5371\u9669\u64CD\u4F5C\uFF0C\u9700\u8981\u7279\u6B8A\u6743\u9650"};let t=`tool_${s.category}`;return!e.permissions.includes(t)&&!e.permissions.includes("tool_all")?{allowed:!1,reason:`\u7F3A\u5C11\u5DE5\u5177\u5206\u7C7B\u6743\u9650: ${t}`}:{allowed:!0}}postProcessResult(s,e,t){let r={...s.metadata,toolName:e.name,toolCategory:e.category,toolVersion:e.version};return e.permissions.dangerous&&s.success&&(r.dangerousOperation=!0),{...s,metadata:r}}async processQueue(){if(!this.isProcessing){this.isProcessing=!0;try{for(;this.executionQueue.length>0&&this.currentExecutions.size<this.maxConcurrentExecutions;){let s=this.executionQueue.shift();if(!s)break;s.status="running",this.executeQueuedTask(s).catch(e=>{console.error("Queued task execution failed:",e)})}}finally{this.isProcessing=!1}this.executionQueue.length>0&&setTimeout(()=>this.processQueue(),100)}}async executeQueuedTask(s){try{let e=await this.executeTool(s.toolName,s.args,s.context);s.status="completed",s.result=e}catch(e){s.status="failed",s.error=e instanceof Error?e.message:String(e)}}sortQueue(){let s={high:3,normal:2,low:1};this.executionQueue.sort((e,t)=>{let r=s[e.priority];return s[t.priority]-r})}generateExecutionId(){return`exec_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}},fe=class{constructor(s){d(this,"requests",new Map);d(this,"maxRequests");d(this,"windowMs");this.maxRequests=s.maxRequests,this.windowMs=s.windowMs}checkLimit(s){let e=Date.now(),r=(this.requests.get(s)||[]).filter(o=>e-o<this.windowMs);return r.length>=this.maxRequests?!1:(r.push(e),this.requests.set(s,r),!0)}};var H=class{constructor(s,e,t){this.maxIterations=10;this.maxThinkingSteps=3;this.currentUserInput="";this.llm=s,this.promptManager=e,this.toolRegistry=t}async planAndExecute(s,e){let t=Date.now(),r=[];this.currentUserInput=s,console.log("\u{1F680} TaskPlanner.planAndExecute \u5F00\u59CB\u6267\u884C"),console.log("\u{1F4DD} \u7528\u6237\u8F93\u5165:",s),console.log("\u{1F527} \u53EF\u7528\u5DE5\u5177\u6570\u91CF:",this.toolRegistry.list().length),console.log("\u{1F527} \u53EF\u7528\u5DE5\u5177\u5217\u8868:",this.toolRegistry.list().map(o=>o.name));try{console.log("\u{1F4CB} \u5F00\u59CB\u4EFB\u52A1\u5206\u6790...");let o=await this.analyzeTask(s,e);if(console.log("\u{1F4CB} \u4EFB\u52A1\u5206\u6790\u7ED3\u679C:",{executable:o.executable,complexity:o.complexity,requiredTools:o.requiredTools}),console.log("\u{1F4C4} \u5206\u6790\u5185\u5BB9:",o.analysis),r.push({type:"analysis",content:o.analysis,timestamp:new Date}),!o.executable)return console.log("\u274C \u4EFB\u52A1\u88AB\u5224\u5B9A\u4E3A\u4E0D\u53EF\u6267\u884C\uFF0C\u76F4\u63A5\u8FD4\u56DE"),{success:!1,result:o.analysis,executionLog:r,executionTime:Date.now()-t,error:"\u4EFB\u52A1\u65E0\u6CD5\u6267\u884C"};console.log("\u{1F504} \u5F00\u59CBReAct\u5FAA\u73AF\u6267\u884C...");let n=await this.executeReActLoop(s,o,r);return console.log("\u{1F504} ReAct\u5FAA\u73AF\u6267\u884C\u5B8C\u6210:",{success:n.success,toolsUsed:n.toolsUsed,iterations:n.iterations}),{success:n.success,result:n.finalAnswer,executionLog:r,executionTime:Date.now()-t,toolsUsed:n.toolsUsed,iterations:n.iterations}}catch(o){return r.push({type:"error",content:`\u6267\u884C\u9519\u8BEF: ${o.message}`,timestamp:new Date}),{success:!1,result:"\u4EFB\u52A1\u6267\u884C\u8FC7\u7A0B\u4E2D\u53D1\u751F\u9519\u8BEF",executionLog:r,executionTime:Date.now()-t,error:o.message}}}async analyzeTask(s,e){let r=[{role:"system",content:this.promptManager.generateTaskAnalysisPrompt(s,e==null?void 0:e.conversationHistory)},{role:"user",content:s}],o=await this.llm.generateText(r);return this.parseTaskAnalysis(o.content)}async executeReActLoop(s,e,t){let r=this.toolRegistry.list(),o=[],n=s,i="",a=0;console.log("\u{1F504} \u5F00\u59CBReAct\u5FAA\u73AF\uFF0C\u6700\u5927\u8FED\u4EE3\u6B21\u6570:",this.maxIterations);for(let c=0;c<this.maxIterations;c++){a++,console.log(`
\u{1F504} === \u7B2C${a}\u8F6E\u8FED\u4EE3\u5F00\u59CB ===`),console.log("\u{1F4AD} \u751F\u6210\u601D\u8003...");let l=await this.generateThought(n,r,t);console.log("\u{1F4AD} \u601D\u8003\u7ED3\u679C:",l.substring(0,200)+"..."),t.push({type:"thought",content:l,timestamp:new Date}),console.log("\u{1F3AF} \u751F\u6210\u884C\u52A8\u8BA1\u5212...");let u=await this.generateAction(l,r);if(console.log("\u{1F3AF} \u884C\u52A8\u8BA1\u5212:",u),t.push({type:"action",content:`\u8BA1\u5212\u6267\u884C: ${u.type} - ${u.description}`,timestamp:new Date}),console.log("\u26A1 \u6267\u884C\u884C\u52A8\uFF0C\u7C7B\u578B:",u.type),u.type==="tool_call"){console.log("\u{1F527} \u51C6\u5907\u8C03\u7528\u5DE5\u5177:",u.toolName,"\u53C2\u6570:",u.parameters);let p=await this.executeTool(u.toolName,u.parameters);console.log("\u{1F527} \u5DE5\u5177\u6267\u884C\u5B8C\u6210:",{success:p.success,hasData:!!p.data,hasError:!!p.error}),t.push({type:"tool_result",content:`\u5DE5\u5177 ${u.toolName} \u6267\u884C\u7ED3\u679C: ${JSON.stringify(p.data)}`,timestamp:new Date,toolName:u.toolName,toolResult:p}),o.includes(u.toolName)||(o.push(u.toolName),console.log("\u{1F4DD} \u6DFB\u52A0\u5230\u5DF2\u4F7F\u7528\u5DE5\u5177\u5217\u8868:",u.toolName)),n+=`

\u5DE5\u5177\u6267\u884C\u7ED3\u679C:
${JSON.stringify(p.data)}`}else if(u.type==="final_answer"){console.log("\u2705 \u751F\u6210\u6700\u7EC8\u7B54\u6848"),i=u.answer,t.push({type:"final_answer",content:i,timestamp:new Date});break}else if(u.type==="need_more_info"){console.log("\u2753 \u9700\u8981\u66F4\u591A\u4FE1\u606F"),i=u.question,t.push({type:"clarification",content:i,timestamp:new Date});break}else console.log("\u26A0\uFE0F \u672A\u77E5\u7684\u884C\u52A8\u7C7B\u578B:",u.type);if(await this.shouldStop(n,t)){i=await this.generateFinalAnswer(n,t);break}}return a>=this.maxIterations&&!i&&(i="\u4EFB\u52A1\u6267\u884C\u8D85\u8FC7\u6700\u5927\u8FED\u4EE3\u6B21\u6570\uFF0C\u8BF7\u7B80\u5316\u4EFB\u52A1\u6216\u63D0\u4F9B\u66F4\u660E\u786E\u7684\u6307\u4EE4\u3002"),{success:!!i,finalAnswer:i,toolsUsed:o,iterations:a}}async generateThought(s,e,t){let r=this.promptManager.generateSystemPrompt(),o=e.map(l=>`${l.name}: ${l.description}`).join(`
`),n=t.slice(-5).map(l=>`${l.type}: ${l.content}`).join(`
`),i=`
\u4F60\u662F\u4E00\u4E2A\u667A\u80FD\u52A9\u624B\uFF0C\u9700\u8981\u5206\u6790\u7528\u6237\u8BF7\u6C42\u5E76\u51B3\u5B9A\u5982\u4F55\u5E2E\u52A9\u7528\u6237\u3002

\u7528\u6237\u539F\u59CB\u8BF7\u6C42\uFF1A${this.currentUserInput}

\u5F53\u524D\u4E0A\u4E0B\u6587\uFF1A
${s}

\u53EF\u7528\u5DE5\u5177\uFF1A
${o}

\u6700\u8FD1\u7684\u6267\u884C\u6B65\u9AA4\uFF1A
${n}

\u8BF7\u4ED4\u7EC6\u5206\u6790\u7528\u6237\u7684\u8BF7\u6C42\u7C7B\u578B\uFF1A
- \u5982\u679C\u662F\u8BA1\u7B97\u3001\u6570\u5B66\u95EE\u9898 \u2192 \u9700\u8981\u4F7F\u7528 javascript_executor \u5DE5\u5177
- \u5982\u679C\u662F\u641C\u7D22\u7B14\u8BB0\u3001\u67E5\u627E\u6587\u6863 \u2192 \u9700\u8981\u4F7F\u7528 vault_query \u5DE5\u5177
- \u5982\u679C\u662F\u7F51\u7EDC\u641C\u7D22\u3001\u6700\u65B0\u4FE1\u606F \u2192 \u9700\u8981\u4F7F\u7528 web_search \u5DE5\u5177
- \u5982\u679C\u662F\u63D2\u4EF6\u64CD\u4F5C \u2192 \u9700\u8981\u4F7F\u7528 plugin_manager \u5DE5\u5177

\u8BF7\u601D\u8003\uFF1A\u7528\u6237\u60F3\u8981\u4EC0\u4E48\uFF1F\u9700\u8981\u4F7F\u7528\u54EA\u4E2A\u5DE5\u5177\uFF1F\u4E3A\u4EC0\u4E48\uFF1F`,a=[{role:"system",content:r},{role:"user",content:i}];return(await this.llm.generateText(a)).content}async generateAction(s,e){let t=e.map(i=>({name:i.name,description:i.description,parameters:i.parameters})),o=[{role:"user",content:`
\u4F60\u9700\u8981\u6839\u636E\u7528\u6237\u7684\u8BF7\u6C42\u548C\u5F53\u524D\u601D\u8003\uFF0C\u51B3\u5B9A\u4E0B\u4E00\u6B65\u7684\u5177\u4F53\u884C\u52A8\u3002

\u7528\u6237\u539F\u59CB\u8BF7\u6C42\uFF1A${this.currentUserInput||""}

\u5F53\u524D\u601D\u8003\uFF1A
${s}

\u53EF\u7528\u5DE5\u5177\uFF1A
${JSON.stringify(t,null,2)}

**\u5DE5\u5177\u9009\u62E9\u6307\u5357**\uFF1A
1. **vault_query** - \u7528\u4E8E\u641C\u7D22Obsidian\u7B14\u8BB0\u5185\u5BB9
   - \u5173\u952E\u8BCD\uFF1A\u641C\u7D22\u7B14\u8BB0\u3001\u67E5\u627E\u6587\u6863\u3001\u6211\u7684\u7B14\u8BB0\u3001vault\u5185\u5BB9

2. **web_search** - \u7528\u4E8E\u7F51\u7EDC\u641C\u7D22\u6700\u65B0\u4FE1\u606F
   - \u5173\u952E\u8BCD\uFF1A\u6700\u65B0\u4FE1\u606F\u3001\u7F51\u7EDC\u641C\u7D22\u3001\u5B9E\u65F6\u6570\u636E\u3001\u65B0\u95FB

3. **javascript_executor** - \u7528\u4E8E\u6267\u884C\u8BA1\u7B97\u548C\u4EE3\u7801
   - \u5173\u952E\u8BCD\uFF1A\u8BA1\u7B97\u3001\u6570\u5B66\u3001\u4EE3\u7801\u6267\u884C\u3001\u6570\u636E\u5904\u7406\u3001\u7F16\u7A0B

4. **plugin_manager** - \u7528\u4E8E\u7BA1\u7406Obsidian\u63D2\u4EF6
   - \u5173\u952E\u8BCD\uFF1A\u63D2\u4EF6\u3001\u547D\u4EE4\u3001Obsidian\u529F\u80FD

5. **file_operation** - \u7528\u4E8E\u6587\u4EF6\u548C\u6587\u4EF6\u5939\u64CD\u4F5C
   - \u5173\u952E\u8BCD\uFF1A\u521B\u5EFA\u6587\u4EF6\u3001\u5220\u9664\u6587\u4EF6\u3001\u7F16\u8F91\u6587\u4EF6\u3001\u79FB\u52A8\u6587\u4EF6\u3001\u65B0\u5EFA\u6587\u4EF6\u5939

6. **note_template** - \u7528\u4E8E\u521B\u5EFA\u6A21\u677F\u5316\u7B14\u8BB0
   - \u5173\u952E\u8BCD\uFF1A\u521B\u5EFA\u7B14\u8BB0\u3001\u6A21\u677F\u3001\u65E5\u8BB0\u3001\u4F1A\u8BAE\u8BB0\u5F55\u3001\u9879\u76EE\u7B14\u8BB0

**\u51B3\u7B56\u89C4\u5219**\uFF1A
- \u5982\u679C\u7528\u6237\u8981\u6C42\u8BA1\u7B97\u3001\u6570\u5B66\u8FD0\u7B97 \u2192 \u4F7F\u7528 javascript_executor
- \u5982\u679C\u7528\u6237\u8981\u6C42\u641C\u7D22\u7B14\u8BB0\u3001\u67E5\u627E\u6587\u6863 \u2192 \u4F7F\u7528 vault_query
- \u5982\u679C\u7528\u6237\u8981\u6C42\u7F51\u7EDC\u641C\u7D22\u3001\u6700\u65B0\u4FE1\u606F \u2192 \u4F7F\u7528 web_search
- \u5982\u679C\u7528\u6237\u8981\u6C42\u63D2\u4EF6\u64CD\u4F5C \u2192 \u4F7F\u7528 plugin_manager
- \u5982\u679C\u7528\u6237\u8981\u6C42\u6587\u4EF6\u64CD\u4F5C\uFF08\u521B\u5EFA\u3001\u5220\u9664\u3001\u7F16\u8F91\u3001\u79FB\u52A8\u6587\u4EF6\uFF09 \u2192 \u4F7F\u7528 file_operation
- \u5982\u679C\u7528\u6237\u8981\u6C42\u521B\u5EFA\u7279\u5B9A\u7C7B\u578B\u7684\u7B14\u8BB0\uFF08\u65E5\u8BB0\u3001\u4F1A\u8BAE\u8BB0\u5F55\u7B49\uFF09 \u2192 \u4F7F\u7528 note_template
- \u5982\u679C\u5DF2\u7ECF\u83B7\u5F97\u8DB3\u591F\u4FE1\u606F\u53EF\u4EE5\u56DE\u7B54 \u2192 \u4F7F\u7528 final_answer

**\u91CD\u8981**\uFF1A\u8BF7\u4E25\u683C\u6309\u7167\u4EE5\u4E0B\u8981\u6C42\u8FD4\u56DE\uFF1A
1. \u53EA\u8FD4\u56DE\u7EAFJSON\u683C\u5F0F\uFF0C\u4E0D\u8981\u4F7F\u7528markdown\u4EE3\u7801\u5757\uFF08\u4E0D\u8981\u7528\u4E09\u4E2A\u53CD\u5F15\u53F7\u5305\u88F9\uFF09
2. \u4E0D\u8981\u6DFB\u52A0\u4EFB\u4F55\u89E3\u91CA\u6587\u5B57\u6216\u5176\u4ED6\u5185\u5BB9
3. \u786E\u4FDDJSON\u683C\u5F0F\u6B63\u786E\u4E14\u53EF\u89E3\u6790

\u8FD4\u56DE\u683C\u5F0F\uFF1A
{
  "type": "tool_call|final_answer|need_more_info",
  "description": "\u884C\u52A8\u63CF\u8FF0",
  "toolName": "\u5DE5\u5177\u540D\u79F0\uFF08\u4EC5tool_call\u65F6\u9700\u8981\uFF09",
  "parameters": {"\u53C2\u6570\u540D": "\u53C2\u6570\u503C"},
  "answer": "\u6700\u7EC8\u7B54\u6848\uFF08\u4EC5final_answer\u65F6\u9700\u8981\uFF09",
  "question": "\u9700\u8981\u8BE2\u95EE\u7684\u95EE\u9898\uFF08\u4EC5need_more_info\u65F6\u9700\u8981\uFF09"
}`}];console.log("\u{1F916} \u53D1\u9001\u884C\u52A8\u8BA1\u5212\u8BF7\u6C42\u5230LLM...");let n=await this.llm.generateText(o);console.log("\u{1F916} LLM\u884C\u52A8\u8BA1\u5212\u539F\u59CB\u54CD\u5E94:",n.content);try{let i=n.content.trim();i.startsWith("```json")?(i=i.replace(/^```json\s*/,"").replace(/\s*```$/,""),console.log("\u{1F9F9} \u79FB\u9664\u4E86markdown\u4EE3\u7801\u5757\u6807\u8BB0")):i.startsWith("```")&&(i=i.replace(/^```\s*/,"").replace(/\s*```$/,""),console.log("\u{1F9F9} \u79FB\u9664\u4E86\u901A\u7528\u4EE3\u7801\u5757\u6807\u8BB0")),console.log("\u{1F9F9} \u6E05\u7406\u540E\u7684\u5185\u5BB9:",i);let a=JSON.parse(i);if(console.log("\u2705 \u884C\u52A8\u8BA1\u5212\u89E3\u6790\u6210\u529F:",a),!a.type)throw new Error("\u7F3A\u5C11\u5FC5\u8981\u5B57\u6BB5: type");return a}catch(i){console.log("\u274C \u884C\u52A8\u8BA1\u5212JSON\u89E3\u6790\u5931\u8D25:",i.message),console.log("\u{1F4C4} \u539F\u59CB\u54CD\u5E94\u5185\u5BB9:",n.content);let a=this.extractActionFromText(n.content);if(a)return console.log("\u{1F527} \u4ECE\u6587\u672C\u4E2D\u63D0\u53D6\u5230\u884C\u52A8\u8BA1\u5212:",a),a;let c={type:"final_answer",description:"\u89E3\u6790\u884C\u52A8\u8BA1\u5212\u5931\u8D25\uFF0C\u63D0\u4F9B\u57FA\u4E8E\u601D\u8003\u7684\u7B54\u6848",answer:s};return console.log("\u{1F504} \u4F7F\u7528\u5907\u7528\u884C\u52A8\u8BA1\u5212:",c),c}}extractActionFromText(s){console.log("\u{1F50D} \u5C1D\u8BD5\u4ECE\u6587\u672C\u4E2D\u63D0\u53D6\u884C\u52A8\u8BA1\u5212...");try{let e=[/使用\s*(\w+)\s*工具/,/调用\s*(\w+)\s*工具/,/执行\s*(\w+)\s*工具/,/toolName["\s]*:\s*["\s]*(\w+)/,/web_search|vault_query|javascript_executor|plugin_manager|file_operation|note_template/];for(let t of e){let r=s.match(t);if(r){let o=r[1]||r[0];o.includes("web_search")||s.includes("\u7F51\u7EDC\u641C\u7D22")||s.includes("\u641C\u7D22")?o="web_search":o.includes("vault_query")||s.includes("\u7B14\u8BB0")||s.includes("vault")?o="vault_query":o.includes("javascript_executor")||s.includes("\u8BA1\u7B97")||s.includes("\u4EE3\u7801")?o="javascript_executor":o.includes("plugin_manager")||s.includes("\u63D2\u4EF6")?o="plugin_manager":o.includes("file_operation")||s.includes("\u521B\u5EFA\u6587\u4EF6")||s.includes("\u5220\u9664\u6587\u4EF6")||s.includes("\u7F16\u8F91\u6587\u4EF6")?o="file_operation":(o.includes("note_template")||s.includes("\u6A21\u677F")||s.includes("\u65E5\u8BB0")||s.includes("\u4F1A\u8BAE\u8BB0\u5F55"))&&(o="note_template");let n="",i=[/"query"\s*:\s*"([^"]+)"/,/搜索["\s]*([^"]+)["\s]*/,/查询["\s]*([^"]+)["\s]*/];for(let c of i){let l=s.match(c);if(l){n=l[1];break}}let a={type:"tool_call",description:`\u4ECE\u6587\u672C\u4E2D\u63D0\u53D6\u7684\u5DE5\u5177\u8C03\u7528: ${o}`,toolName:o,parameters:n?{query:n}:{}};return console.log("\u2705 \u6210\u529F\u63D0\u53D6\u884C\u52A8\u8BA1\u5212:",a),a}}return console.log("\u274C \u65E0\u6CD5\u4ECE\u6587\u672C\u4E2D\u63D0\u53D6\u5DE5\u5177\u8C03\u7528\u4FE1\u606F"),null}catch(e){return console.log("\u274C \u63D0\u53D6\u884C\u52A8\u8BA1\u5212\u65F6\u51FA\u9519:",e.message),null}}async executeTool(s,e){console.log("\u{1F527} TaskPlanner.executeTool \u5F00\u59CB\u6267\u884C"),console.log("\u{1F527} \u5DE5\u5177\u540D\u79F0:",s),console.log("\u{1F527} \u5DE5\u5177\u53C2\u6570:",e);try{console.log("\u{1F527} \u8C03\u7528 toolRegistry.execute...");let t=await this.toolRegistry.execute(s,e);return console.log("\u{1F527} \u5DE5\u5177\u6267\u884C\u7ED3\u679C:",{success:t.success,hasData:!!t.data,hasError:!!t.error,dataType:typeof t.data}),t.error&&console.log("\u{1F527} \u5DE5\u5177\u6267\u884C\u9519\u8BEF:",t.error),t}catch(t){return console.log("\u274C \u5DE5\u5177\u6267\u884C\u5F02\u5E38:",t.message),console.log("\u274C \u5F02\u5E38\u5806\u6808:",t.stack),{success:!1,error:`\u5DE5\u5177\u6267\u884C\u5931\u8D25: ${t.message}`}}}async shouldStop(s,e){let t=e.slice(-3);if(t.some(o=>o.type==="final_answer"))return!0;let r=t.filter(o=>o.type==="tool_result");return!!(r.length>=2&&r.every(o=>{var n;return!((n=o.toolResult)!=null&&n.success)}))}async generateFinalAnswer(s,e){let r=[{role:"user",content:this.promptManager.generateContentSummaryPrompt(s,e.map(n=>({source:n.type,content:n.content,score:1})))}];return(await this.llm.generateText(r)).content}parseTaskAnalysis(s){let e=s.toLowerCase(),r=["\u65E0\u6CD5","\u4E0D\u80FD","\u65E0\u6548","\u4E0D\u652F\u6301","\u65E0\u6743\u9650"].some(a=>e.includes(a)),n=["\u641C\u7D22","\u67E5\u627E","\u67E5\u8BE2","\u8BA1\u7B97","\u6267\u884C","\u8FD0\u884C","\u5206\u6790","\u7B14\u8BB0","\u6587\u6863","\u7F51\u7EDC","\u4EE3\u7801","\u63D2\u4EF6","\u547D\u4EE4"].some(a=>e.includes(a));return{analysis:s,executable:!r||n,complexity:this.estimateComplexity(s),requiredTools:this.extractRequiredTools(s)}}estimateComplexity(s){let t=["\u591A\u6B65\u9AA4","\u590D\u6742","\u9700\u8981","\u5206\u6790","\u5904\u7406","\u751F\u6210","\u641C\u7D22","\u8BA1\u7B97"].filter(r=>s.includes(r)).length;return t<=2?"low":t<=4?"medium":"high"}extractRequiredTools(s){let e=this.toolRegistry.list(),t=[];for(let r of e)(s.toLowerCase().includes(r.name.toLowerCase())||s.toLowerCase().includes(r.description.toLowerCase()))&&t.push(r.name);return t}};var te=class{constructor(s,e){this.currentConversationId=null;this.conversationState="idle";this.contextWindow=10;this.shortTermMemory=s,this.longTermMemory=e}async startConversation(s){let e=await this.shortTermMemory.execute({action:"create",metadata:{title:(s==null?void 0:s.title)||`\u5BF9\u8BDD ${new Date().toLocaleString()}`,tags:(s==null?void 0:s.tags)||[],summary:s==null?void 0:s.summary}});if(e.success)return this.currentConversationId=e.data.conversationId,this.conversationState="active",this.currentConversationId;throw new Error("\u521B\u5EFA\u5BF9\u8BDD\u5931\u8D25")}async addUserMessage(s,e){this.currentConversationId||await this.startConversation();let t={id:this.generateMessageId(),role:"user",content:s,timestamp:new Date,metadata:e||{}};await this.shortTermMemory.execute({action:"add_message",conversationId:this.currentConversationId,message:t}),this.conversationState="processing"}async addAssistantMessage(s,e,t){if(!this.currentConversationId)throw new Error("\u6CA1\u6709\u6D3B\u52A8\u7684\u5BF9\u8BDD");let r={id:this.generateMessageId(),role:"assistant",content:s,timestamp:new Date,metadata:{...t,executionLog:e==null?void 0:e.map(o=>({type:o.type,content:o.content,timestamp:o.timestamp,toolName:o.toolName}))}};await this.shortTermMemory.execute({action:"add_message",conversationId:this.currentConversationId,message:r}),this.conversationState="active",await this.updateConversationMetadata(e)}async getConversationContext(s){if(!this.currentConversationId)return null;let e=await this.shortTermMemory.execute({action:"get",conversationId:this.currentConversationId});if(e.success){let t=e.data.conversation,r=s||this.contextWindow;return t.messages.length>r&&(t.messages=t.messages.slice(-r)),t}return null}async getRecentMessages(s=5){let e=await this.getConversationContext(s);return(e==null?void 0:e.messages)||[]}async buildLLMContext(s=!0){let e=await this.getConversationContext();if(!e)return{messages:[]};let r={messages:e.messages.map(o=>({role:o.role,content:o.content}))};return s&&(r.systemPrompt=this.buildSystemPrompt(e)),r}setConversationState(s){this.conversationState=s}getConversationState(){return this.conversationState}async endConversation(s){this.currentConversationId&&(s&&await this.shortTermMemory.execute({action:"update",conversationId:this.currentConversationId,metadata:{summary:s}}),await this.saveImportantInformation(),this.conversationState="ended",this.currentConversationId=null)}async searchConversationHistory(s,e=5){return this.shortTermMemory.searchConversations(s,e)}async getConversationStats(){let s=this.shortTermMemory.getStats(),e=await this.getConversationContext();return{totalConversations:s.totalConversations,totalMessages:s.totalMessages,currentConversationLength:(e==null?void 0:e.messages.length)||0,averageMessagesPerConversation:s.averageMessagesPerConversation,conversationState:this.conversationState}}async cleanupOldConversations(s=30){return 0}async updateConversationMetadata(s){if(!this.currentConversationId||!s)return;let e=s.filter(t=>t.toolName).map(t=>t.toolName).filter((t,r,o)=>o.indexOf(t)===r);await this.shortTermMemory.execute({action:"update",conversationId:this.currentConversationId,metadata:{toolsUsed:e,lastActivity:new Date().toISOString()}})}buildSystemPrompt(s){let e=s.metadata.toolsUsed||[],t=s.messages.length,r="\u4F60\u662F\u4E00\u4E2A\u667A\u80FD\u7684Obsidian\u52A9\u624B\u3002";return e.length>0&&(r+=`

\u5728\u8FD9\u6B21\u5BF9\u8BDD\u4E2D\uFF0C\u4F60\u5DF2\u7ECF\u4F7F\u7528\u4E86\u4EE5\u4E0B\u5DE5\u5177\uFF1A${e.join(", ")}\u3002`),t>5&&(r+=`

\u8FD9\u662F\u4E00\u4E2A\u8F83\u957F\u7684\u5BF9\u8BDD\uFF0C\u8BF7\u4FDD\u6301\u4E0A\u4E0B\u6587\u7684\u8FDE\u8D2F\u6027\u3002`),r}async saveImportantInformation(){let s=await this.getConversationContext();if(!s||s.messages.length<3)return;let e=s.messages.filter(t=>t.content.length>50&&(t.content.includes("\u91CD\u8981")||t.content.includes("\u8BB0\u4F4F")||t.content.includes("\u504F\u597D")));for(let t of e)try{await this.longTermMemory.execute({action:"save_memory",content:t.content,tags:["conversation","important"]})}catch(r){console.warn("\u4FDD\u5B58\u91CD\u8981\u4FE1\u606F\u5931\u8D25:",r)}}generateMessageId(){return`msg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}getCurrentConversationId(){return this.currentConversationId}setContextWindow(s){this.contextWindow=Math.max(1,Math.min(50,s))}};var se=class{constructor(s){this.executionHistory=[];this.maxHistorySize=100;this.toolRegistry=s}async coordinateExecution(s,e){let t=Date.now(),r=[],o=[],n=[];try{let i=this.planExecution(s);r.push({type:"action",content:`\u89C4\u5212\u6267\u884C ${s.length} \u4E2A\u5DE5\u5177\u8C03\u7528`,timestamp:new Date});for(let a of i){let c=await this.executePhase(a,e,r);o.push(...c),e&&(e.previousResults=c)}return s.forEach(a=>{n.includes(a.toolName)||n.push(a.toolName)}),this.recordExecution({toolCalls:s,results:o,executionTime:Date.now()-t,success:o.every(a=>a.success),timestamp:new Date}),{success:!0,results:o,executionLog:r,toolsUsed:n,executionTime:Date.now()-t,coordination:{totalPhases:i.length,parallelExecutions:i.reduce((a,c)=>a+c.length,0)}}}catch(i){return r.push({type:"error",content:`\u5DE5\u5177\u534F\u8C03\u6267\u884C\u5931\u8D25: ${i.message}`,timestamp:new Date}),{success:!1,results:o,executionLog:r,toolsUsed:n,executionTime:Date.now()-t,error:i.message}}}planExecution(s){let e=[],t=new Set,r=[...s];for(;r.length>0;){let o=[];for(let n=r.length-1;n>=0;n--){let i=r[n];this.canExecuteNow(i,t)&&(o.push(i),r.splice(n,1),t.add(i.id))}if(o.length===0)throw new Error("\u68C0\u6D4B\u5230\u5FAA\u73AF\u4F9D\u8D56\u6216\u65E0\u6CD5\u89E3\u51B3\u7684\u5DE5\u5177\u4F9D\u8D56\u5173\u7CFB");e.push(o)}return e}canExecuteNow(s,e){return!s.dependencies||s.dependencies.length===0?!0:s.dependencies.every(t=>e.has(t))}async executePhase(s,e,t){let r=[],o=s.map(async i=>{try{t==null||t.push({type:"action",content:`\u6267\u884C\u5DE5\u5177: ${i.toolName}`,timestamp:new Date,toolName:i.toolName});let a=this.processArguments(i.arguments,e),c=await this.toolRegistry.execute(i.toolName,a,e);return t==null||t.push({type:"tool_result",content:`\u5DE5\u5177 ${i.toolName} \u6267\u884C${c.success?"\u6210\u529F":"\u5931\u8D25"}`,timestamp:new Date,toolName:i.toolName,toolResult:c}),c}catch(a){let c={success:!1,error:`\u5DE5\u5177 ${i.toolName} \u6267\u884C\u5931\u8D25: ${a.message}`};return t==null||t.push({type:"error",content:c.error,timestamp:new Date,toolName:i.toolName}),c}}),n=await Promise.all(o);return r.push(...n),r}processArguments(s,e){if(!(e!=null&&e.previousResults)||!s)return s;let t=JSON.parse(JSON.stringify(s));return this.resolveReferences(t,e.previousResults),t}resolveReferences(s,e){if(typeof s=="string"&&s.startsWith("$ref:")){let t=s.substring(5),[r,...o]=t.split("."),n=e.find(i=>{var a;return((a=i.metadata)==null?void 0:a.toolName)===r});if(n&&n.data){let i=n.data;for(let a of o)if(i=i[a],i===void 0)break;return i}}else if(typeof s=="object"&&s!==null){for(let t in s)if(s.hasOwnProperty(t)){let r=this.resolveReferences(s[t],e);r!==void 0&&(s[t]=r)}}}recordExecution(s){this.executionHistory.push(s),this.executionHistory.length>this.maxHistorySize&&(this.executionHistory=this.executionHistory.slice(-this.maxHistorySize))}getExecutionStats(){var o;let s=this.executionHistory.length,e=this.executionHistory.filter(n=>n.success).length,t=s>0?this.executionHistory.reduce((n,i)=>n+i.executionTime,0)/s:0,r={};return this.executionHistory.forEach(n=>{n.toolCalls.forEach(i=>{r[i.toolName]=(r[i.toolName]||0)+1})}),{totalExecutions:s,successfulExecutions:e,failedExecutions:s-e,averageExecutionTime:Math.round(t),toolUsage:r,mostUsedTool:((o=Object.entries(r).sort(([,n],[,i])=>i-n)[0])==null?void 0:o[0])||"none"}}clearExecutionHistory(){this.executionHistory=[]}};var x=class{constructor(s){this.initialized=!1;this.executionCount=0;this.lastExecutionTime=null;this.name=s.name,this.description=s.description,this.category=s.category,this.version=s.version||"1.0.0",this.parameters=s.parameters,this.permissions=s.permissions,this.metadata=s.metadata}async execute(s,e){let t=Date.now(),r=(e==null?void 0:e.requestId)||this.generateRequestId();try{let o=await this.validate(s);if(!o.valid)return{success:!1,error:`\u53C2\u6570\u9A8C\u8BC1\u5931\u8D25: ${o.errors.join(", ")}`,metadata:{executionTime:Date.now()-t,requestId:r}};if(e){let a=this.checkPermissions(e);if(!a.allowed)return{success:!1,error:`\u6743\u9650\u4E0D\u8DB3: ${a.reason}`,metadata:{executionTime:Date.now()-t,requestId:r}}}this.initialized||(await this.initialize(),this.initialized=!0);let n=await this.executeInternal(s,e);this.executionCount++,this.lastExecutionTime=new Date;let i={executionTime:Date.now()-t,requestId:r,...n.metadata};return{...n,metadata:i}}catch(o){return{success:!1,error:o instanceof Error?o.message:String(o),metadata:{executionTime:Date.now()-t,requestId:r}}}}async validate(s){let e=[],t=[];if(this.parameters.required)for(let o of this.parameters.required)o in s||e.push(`\u7F3A\u5C11\u5FC5\u9700\u53C2\u6570: ${o}`);for(let[o,n]of Object.entries(this.parameters.properties))if(o in s){let i=s[o],a=this.validateParameterType(i,n);a.valid||e.push(`\u53C2\u6570 ${o} ${a.error}`)}let r=await this.validateInternal(s);return e.push(...r.errors),t.push(...r.warnings),{valid:e.length===0,errors:e,warnings:t}}async initialize(){}async cleanup(){this.initialized=!1}getStats(){return{executionCount:this.executionCount,lastExecutionTime:this.lastExecutionTime,initialized:this.initialized}}checkPermissions(s){for(let e of this.permissions.required)if(!s.permissions.includes(e))return{allowed:!1,reason:`\u7F3A\u5C11\u5FC5\u9700\u6743\u9650: ${e}`};return this.permissions.dangerous&&!s.permissions.includes("dangerous_operations")?{allowed:!1,reason:"\u6B64\u5DE5\u5177\u6267\u884C\u5371\u9669\u64CD\u4F5C\uFF0C\u9700\u8981\u7279\u6B8A\u6743\u9650"}:{allowed:!0}}validateParameterType(s,e){switch(e.type){case"string":if(typeof s!="string")return{valid:!1,error:"\u7C7B\u578B\u9519\u8BEF\uFF0C\u671F\u671B\u5B57\u7B26\u4E32"};if(e.pattern&&!new RegExp(e.pattern).test(s))return{valid:!1,error:"\u683C\u5F0F\u4E0D\u5339\u914D"};break;case"number":if(typeof s!="number")return{valid:!1,error:"\u7C7B\u578B\u9519\u8BEF\uFF0C\u671F\u671B\u6570\u5B57"};if(e.minimum!==void 0&&s<e.minimum)return{valid:!1,error:`\u503C\u8FC7\u5C0F\uFF0C\u6700\u5C0F\u503C\u4E3A ${e.minimum}`};if(e.maximum!==void 0&&s>e.maximum)return{valid:!1,error:`\u503C\u8FC7\u5927\uFF0C\u6700\u5927\u503C\u4E3A ${e.maximum}`};break;case"boolean":if(typeof s!="boolean")return{valid:!1,error:"\u7C7B\u578B\u9519\u8BEF\uFF0C\u671F\u671B\u5E03\u5C14\u503C"};break;case"array":if(!Array.isArray(s))return{valid:!1,error:"\u7C7B\u578B\u9519\u8BEF\uFF0C\u671F\u671B\u6570\u7EC4"};break;case"object":if(typeof s!="object"||s===null||Array.isArray(s))return{valid:!1,error:"\u7C7B\u578B\u9519\u8BEF\uFF0C\u671F\u671B\u5BF9\u8C61"};break}return e.enum&&!e.enum.includes(s)?{valid:!1,error:`\u503C\u4E0D\u5728\u5141\u8BB8\u8303\u56F4\u5185: ${e.enum.join(", ")}`}:{valid:!0}}generateRequestId(){return`${this.name}_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}async validateInternal(s){return{valid:!0,errors:[],warnings:[]}}};var O=class extends x{constructor(e){super({name:"short_term_memory",description:"\u7BA1\u7406\u77ED\u671F\u5BF9\u8BDD\u8BB0\u5FC6\u548C\u4F1A\u8BDD\u4E0A\u4E0B\u6587",category:"memory",version:"1.0.0",parameters:{type:"object",properties:{action:{type:"string",description:"\u64CD\u4F5C\u7C7B\u578B",enum:["create","get","update","delete","list","add_message","get_context"]},conversationId:{type:"string",description:"\u5BF9\u8BDDID"},message:{type:"object",description:"\u6D88\u606F\u5BF9\u8C61",properties:{role:{type:"string",enum:["user","assistant","system"]},content:{type:"string"},metadata:{type:"object"}}},metadata:{type:"object",description:"\u5BF9\u8BDD\u5143\u6570\u636E"},limit:{type:"number",description:"\u9650\u5236\u8FD4\u56DE\u6570\u91CF",default:10,minimum:1,maximum:100}},required:["action"]},permissions:{required:["memory_read","memory_write"],optional:[],dangerous:!1,requiresConfirmation:!1},metadata:{author:"AI Coach Team",tags:["memory","conversation","context"],documentation:"\u7BA1\u7406\u77ED\u671F\u5BF9\u8BDD\u8BB0\u5FC6\uFF0C\u7EF4\u62A4\u4F1A\u8BDD\u4E0A\u4E0B\u6587",examples:[{name:"\u521B\u5EFA\u65B0\u5BF9\u8BDD",description:"\u521B\u5EFA\u4E00\u4E2A\u65B0\u7684\u5BF9\u8BDD\u4F1A\u8BDD",input:{action:"create",metadata:{title:"New Chat"}},expectedOutput:{conversationId:"uuid"}},{name:"\u6DFB\u52A0\u6D88\u606F",description:"\u5411\u5BF9\u8BDD\u4E2D\u6DFB\u52A0\u65B0\u6D88\u606F",input:{action:"add_message",conversationId:"uuid",message:{role:"user",content:"Hello"}},expectedOutput:{success:!0}}]}});this.conversations=new Map;this.maxConversations=100;this.maxMessagesPerConversation=50;this.app=e}async executeInternal(e,t){let{action:r}=e;try{switch(r){case"create":return await this.createConversation(e);case"get":return await this.getConversation(e);case"update":return await this.updateConversation(e);case"delete":return await this.deleteConversation(e);case"list":return await this.listConversations(e);case"add_message":return await this.addMessage(e);case"get_context":return await this.getContext(e);default:return{success:!1,error:`\u672A\u77E5\u64CD\u4F5C: ${r}`}}}catch(o){return{success:!1,error:`\u77ED\u671F\u8BB0\u5FC6\u64CD\u4F5C\u5931\u8D25: ${o.message}`}}}async createConversation(e){let{metadata:t={}}=e,r=this.generateConversationId(),o=new Date,n={id:r,messages:[],metadata:{title:t.title||`\u5BF9\u8BDD ${o.toLocaleString()}`,tags:t.tags||[],summary:t.summary,toolsUsed:[],filesReferenced:[]},createdAt:o,updatedAt:o};return this.conversations.set(r,n),this.cleanupOldConversations(),{success:!0,data:{conversationId:r,conversation:n}}}async getConversation(e){let{conversationId:t}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u5BF9\u8BDDID"};let r=this.conversations.get(t);return r?{success:!0,data:{conversation:r}}:{success:!1,error:`\u5BF9\u8BDD\u4E0D\u5B58\u5728: ${t}`}}async updateConversation(e){let{conversationId:t,metadata:r}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u5BF9\u8BDDID"};let o=this.conversations.get(t);return o?(r&&(o.metadata={...o.metadata,...r},o.updatedAt=new Date),{success:!0,data:{conversation:o}}):{success:!1,error:`\u5BF9\u8BDD\u4E0D\u5B58\u5728: ${t}`}}async deleteConversation(e){let{conversationId:t}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u5BF9\u8BDDID"};let r=this.conversations.delete(t);return{success:r,data:{deleted:r}}}async listConversations(e){let{limit:t=10}=e;return{success:!0,data:{conversations:Array.from(this.conversations.values()).sort((o,n)=>n.updatedAt.getTime()-o.updatedAt.getTime()).slice(0,t),total:this.conversations.size}}}async addMessage(e){let{conversationId:t,message:r}=e;if(!t||!r)return{success:!1,error:"\u7F3A\u5C11\u5BF9\u8BDDID\u6216\u6D88\u606F\u5185\u5BB9"};let o=this.conversations.get(t);if(!o)return{success:!1,error:`\u5BF9\u8BDD\u4E0D\u5B58\u5728: ${t}`};let n={id:this.generateMessageId(),role:r.role,content:r.content,timestamp:new Date,metadata:r.metadata||{}};return o.messages.push(n),o.updatedAt=new Date,o.messages.length>this.maxMessagesPerConversation&&(o.messages=o.messages.slice(-this.maxMessagesPerConversation)),{success:!0,data:{messageId:n.id,conversation:o}}}async getContext(e){let{conversationId:t,limit:r=10}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u5BF9\u8BDDID"};let o=this.conversations.get(t);if(!o)return{success:!1,error:`\u5BF9\u8BDD\u4E0D\u5B58\u5728: ${t}`};let n=o.messages.slice(-r);return{success:!0,data:{conversationId:t,messages:n,metadata:o.metadata,messageCount:o.messages.length}}}generateConversationId(){return`conv_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}generateMessageId(){return`msg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}cleanupOldConversations(){if(this.conversations.size<=this.maxConversations)return;let e=Array.from(this.conversations.entries()).sort(([,r],[,o])=>r.updatedAt.getTime()-o.updatedAt.getTime());e.slice(0,e.length-this.maxConversations).forEach(([r])=>{this.conversations.delete(r)})}getStats(){let e=Array.from(this.conversations.values()),t=e.reduce((i,a)=>i+a.messages.length,0),r=e.map(i=>i.updatedAt),o=r.length>0?new Date(Math.min(...r.map(i=>i.getTime()))):null,n=r.length>0?new Date(Math.max(...r.map(i=>i.getTime()))):null;return{totalConversations:e.length,totalMessages:t,averageMessagesPerConversation:e.length>0?t/e.length:0,oldestConversation:o,newestConversation:n}}clearAll(){this.conversations.clear()}searchConversations(e,t=10){var n;let r=e.toLowerCase(),o=[];for(let i of this.conversations.values()){let a=0;(n=i.metadata.title)!=null&&n.toLowerCase().includes(r)&&(a+=10);for(let c of i.messages)c.content.toLowerCase().includes(r)&&(a+=1);i.metadata.tags.some(c=>c.toLowerCase().includes(r))&&(a+=5),a>0&&o.push({conversation:i,score:a})}return o.sort((i,a)=>a.score-i.score).slice(0,t).map(i=>i.conversation)}};var q=class extends x{constructor(e){super({name:"long_term_memory",description:"\u7BA1\u7406\u957F\u671F\u7528\u6237\u504F\u597D\u548C\u6301\u4E45\u5316\u8BB0\u5FC6\u5B58\u50A8",category:"memory",version:"1.0.0",parameters:{type:"object",properties:{action:{type:"string",description:"\u64CD\u4F5C\u7C7B\u578B",enum:["set","get","delete","list","save_memory","get_memory","search_memory"]},key:{type:"string",description:"\u504F\u597D\u8BBE\u7F6E\u7684\u952E\u540D"},value:{description:"\u504F\u597D\u8BBE\u7F6E\u7684\u503C"},type:{type:"string",description:"\u503C\u7684\u7C7B\u578B",enum:["string","number","boolean","object","array"]},description:{type:"string",description:"\u504F\u597D\u8BBE\u7F6E\u7684\u63CF\u8FF0"},memoryId:{type:"string",description:"\u8BB0\u5FC6\u6761\u76EE\u7684ID"},content:{type:"string",description:"\u8BB0\u5FC6\u5185\u5BB9"},tags:{type:"array",description:"\u8BB0\u5FC6\u6807\u7B7E",items:{type:"string"}},query:{type:"string",description:"\u641C\u7D22\u67E5\u8BE2"}},required:["action"]},permissions:{required:["vault_write","vault_read"],optional:[],dangerous:!1,requiresConfirmation:!1},metadata:{author:"AI Coach Team",tags:["memory","preferences","storage"],documentation:"\u7BA1\u7406\u957F\u671F\u7528\u6237\u504F\u597D\u548C\u8BB0\u5FC6\u5B58\u50A8",examples:[{name:"\u8BBE\u7F6E\u7528\u6237\u504F\u597D",description:"\u4FDD\u5B58\u7528\u6237\u7684\u504F\u597D\u8BBE\u7F6E",input:{action:"set",key:"preferred_language",value:"zh",type:"string",description:"\u7528\u6237\u9996\u9009\u8BED\u8A00"},expectedOutput:{success:!0}},{name:"\u4FDD\u5B58\u8BB0\u5FC6",description:"\u4FDD\u5B58\u91CD\u8981\u7684\u8BB0\u5FC6\u4FE1\u606F",input:{action:"save_memory",content:"\u7528\u6237\u559C\u6B22\u4F7F\u7528Markdown\u683C\u5F0F",tags:["preference","format"]},expectedOutput:{memoryId:"uuid"}}]}});this.preferencesFile=".ai-coach/preferences.json";this.memoryFolder=".ai-coach/memory";this.preferences=new Map;this.initialized=!1;this.app=e}async initialize(){if(!this.initialized)try{await this.ensureDirectories(),await this.loadPreferences(),this.initialized=!0,console.log("LongTermMemoryTool initialized successfully")}catch(e){throw console.error("Failed to initialize LongTermMemoryTool:",e),e}}async executeInternal(e,t){try{this.initialized||await this.initialize();let{action:r}=e;switch(r){case"set":return await this.setPreference(e);case"get":return await this.getPreference(e);case"delete":return await this.deletePreference(e);case"list":return await this.listPreferences(e);case"save_memory":return await this.saveMemory(e);case"get_memory":return await this.getMemory(e);case"search_memory":return await this.searchMemory(e);default:return{success:!1,error:`\u672A\u77E5\u64CD\u4F5C: ${r}`}}}catch(r){return{success:!1,error:`\u957F\u671F\u8BB0\u5FC6\u64CD\u4F5C\u5931\u8D25: ${r.message}`}}}async setPreference(e){let{key:t,value:r,type:o,description:n}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u504F\u597D\u952E\u540D"};let i={key:t,value:r,type:o||typeof r,description:n,updatedAt:new Date};return this.preferences.set(t,i),await this.savePreferences(),{success:!0,data:{preference:i}}}async getPreference(e){let{key:t}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u504F\u597D\u952E\u540D"};let r=this.preferences.get(t);return{success:!0,data:{preference:r,exists:!!r}}}async deletePreference(e){let{key:t}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u504F\u597D\u952E\u540D"};let r=this.preferences.delete(t);return r&&await this.savePreferences(),{success:!0,data:{deleted:r}}}async listPreferences(){let e=Array.from(this.preferences.values());return{success:!0,data:{preferences:e,count:e.length}}}async saveMemory(e){let{content:t,tags:r=[],memoryId:o}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u8BB0\u5FC6\u5185\u5BB9"};let n=o||this.generateMemoryId(),i=`${this.memoryFolder}/${n}.md`,a=this.formatMemoryContent(t,r);try{return await this.app.vault.adapter.write(i,a),{success:!0,data:{memoryId:n,fileName:i}}}catch(c){return{success:!1,error:`\u4FDD\u5B58\u8BB0\u5FC6\u5931\u8D25: ${c.message}`}}}async getMemory(e){let{memoryId:t}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u8BB0\u5FC6ID"};let r=`${this.memoryFolder}/${t}.md`;try{let o=await this.app.vault.adapter.read(r),n=this.parseMemoryContent(o);return{success:!0,data:{memoryId:t,...n}}}catch(o){return{success:!1,error:`\u83B7\u53D6\u8BB0\u5FC6\u5931\u8D25: ${o.message}`}}}async searchMemory(e){let{query:t}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u641C\u7D22\u67E5\u8BE2"};try{let r=await this.getMemoryFiles(),o=[];for(let n of r){let i=await this.app.vault.adapter.read(n);if(i.toLowerCase().includes(t.toLowerCase())){let a=this.parseMemoryContent(i),c=n.replace(`${this.memoryFolder}/`,"").replace(".md","");o.push({memoryId:c,...a,fileName:n})}}return{success:!0,data:{query:t,results:o,count:o.length}}}catch(r){return{success:!1,error:`\u641C\u7D22\u8BB0\u5FC6\u5931\u8D25: ${r.message}`}}}async ensureDirectories(){let e=this.app.vault.adapter;await e.exists(".ai-coach")||await e.mkdir(".ai-coach"),await e.exists(this.memoryFolder)||await e.mkdir(this.memoryFolder)}async loadPreferences(){try{let e=this.app.vault.adapter;if(await e.exists(this.preferencesFile)){let t=await e.read(this.preferencesFile),r=JSON.parse(t);for(let[o,n]of Object.entries(r))this.preferences.set(o,n)}}catch(e){console.warn("Failed to load preferences:",e)}}async savePreferences(){try{let e={};for(let[r,o]of this.preferences.entries())e[r]=o;let t=JSON.stringify(e,null,2);await this.app.vault.adapter.write(this.preferencesFile,t)}catch(e){throw console.error("Failed to save preferences:",e),e}}formatMemoryContent(e,t){let r=new Date().toISOString(),o=t.map(n=>`#${n}`).join(" ");return`---
created: ${r}
tags: [${t.map(n=>`"${n}"`).join(", ")}]
type: memory
---

# AI Coach Memory

${o}

${e}

---
*Created by AI Coach Advanced on ${new Date().toLocaleString()}*`}parseMemoryContent(e){let t=e.match(/^---\n([\s\S]*?)\n---/),r=[],o="",n=e;if(t){let i=t[1],a=i.match(/tags:\s*\[(.*?)\]/),c=i.match(/created:\s*(.+)/);a&&(r=a[1].split(",").map(l=>l.trim().replace(/"/g,""))),c&&(o=c[1].trim()),n=e.replace(t[0],"").trim()}return{content:n,tags:r,created:o}}async getMemoryFiles(){try{return(await this.app.vault.adapter.list(this.memoryFolder)).files.filter(r=>r.endsWith(".md"))}catch(e){return console.error("Failed to get memory files:",e),[]}}generateMemoryId(){return`memory_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}async getStats(){let e=await this.getMemoryFiles(),t=0;for(let r of e)try{let o=await this.app.vault.adapter.stat(r);t+=(o==null?void 0:o.size)||0}catch(o){}return{preferencesCount:this.preferences.size,memoriesCount:e.length,totalSize:t}}async cleanupOldMemories(e=90){let t=await this.getMemoryFiles(),r=new Date;r.setDate(r.getDate()-e);let o=0;for(let n of t)try{let i=await this.app.vault.adapter.stat(n);i&&i.mtime<r.getTime()&&(await this.app.vault.adapter.remove(n),o++)}catch(i){console.warn(`Failed to process file ${n}:`,i)}return o}};var re=class{constructor(s,e){d(this,"app");d(this,"configManager");d(this,"llm",null);d(this,"promptManager");d(this,"toolRegistry");d(this,"toolExecutor");d(this,"toolCoordinator");d(this,"taskPlanner",null);d(this,"conversationManager");d(this,"shortTermMemory");d(this,"longTermMemory");d(this,"initialized",!1);this.app=s,this.configManager=e,this.promptManager=new X,this.toolRegistry=new Z,this.toolExecutor=new ee(this.toolRegistry),this.toolCoordinator=new se(this.toolRegistry),this.shortTermMemory=new O(s),this.longTermMemory=new q(s),this.conversationManager=new te(this.shortTermMemory,this.longTermMemory)}async initialize(){if(!this.initialized)try{await this.initializeLLM(),this.llm&&(this.taskPlanner=new H(this.llm,this.promptManager,this.toolRegistry)),await this.shortTermMemory.initialize(),await this.longTermMemory.initialize(),this.promptManager.setAvailableTools(this.toolRegistry.list()),this.initialized=!0,console.log("OrchestrationEngine initialized successfully")}catch(s){throw console.error("Failed to initialize OrchestrationEngine:",s),s}}async processUserInput(s,e){var r;if(console.log("\u{1F680} OrchestrationEngine.processUserInput \u5F00\u59CB\u5904\u7406"),console.log("\u{1F4DD} \u7528\u6237\u8F93\u5165:",s),this.initialized||(console.log("\u26A0\uFE0F \u5F15\u64CE\u672A\u521D\u59CB\u5316\uFF0C\u5F00\u59CB\u521D\u59CB\u5316..."),await this.initialize()),!this.taskPlanner)throw new Error("Task planner not initialized");console.log("\u{1F527} \u53EF\u7528\u5DE5\u5177\u6570\u91CF:",this.toolRegistry.list().length),console.log("\u{1F527} \u53EF\u7528\u5DE5\u5177\u5217\u8868:",this.toolRegistry.list().map(o=>o.name));let t=Date.now();try{await this.conversationManager.addUserMessage(s,e==null?void 0:e.messageMetadata);let o={conversationHistory:await this.buildConversationHistory(),userPreferences:e==null?void 0:e.userPreferences,availableTools:this.toolRegistry.list().map(i=>i.name)};console.log("\u{1F4CB} \u89C4\u5212\u4E0A\u4E0B\u6587:",{hasConversationHistory:!!o.conversationHistory,availableToolsCount:((r=o.availableTools)==null?void 0:r.length)||0,availableTools:o.availableTools}),console.log("\u{1F3AF} \u5F00\u59CB\u4EFB\u52A1\u89C4\u5212\u548C\u6267\u884C...");let n=await this.taskPlanner.planAndExecute(s,o);return console.log("\u{1F3AF} \u4EFB\u52A1\u89C4\u5212\u548C\u6267\u884C\u5B8C\u6210:",{success:n.success,toolsUsed:n.toolsUsed,iterations:n.iterations}),await this.conversationManager.addAssistantMessage(n.result,n.executionLog,{toolsUsed:n.toolsUsed,iterations:n.iterations}),{success:n.success,response:n.result,executionLog:n.executionLog,processingTime:Date.now()-t,toolsUsed:n.toolsUsed||[],conversationId:this.conversationManager.getCurrentConversationId(),metadata:{iterations:n.iterations,llmCalls:this.countLLMCalls(n.executionLog),complexity:this.assessComplexity(n.executionLog)}}}catch(o){let n=`\u5904\u7406\u8BF7\u6C42\u65F6\u53D1\u751F\u9519\u8BEF: ${o.message}`;return await this.conversationManager.addAssistantMessage(n,[{type:"error",content:o.message,timestamp:new Date}]),{success:!1,response:n,executionLog:[],processingTime:Date.now()-t,toolsUsed:[],conversationId:this.conversationManager.getCurrentConversationId(),error:o.message}}}async startNewConversation(s){return await this.conversationManager.startConversation({title:s})}async endCurrentConversation(s){await this.conversationManager.endConversation(s)}getConversationState(){return this.conversationManager.getConversationState()}async registerTool(s){await this.toolRegistry.register(s),this.promptManager.setAvailableTools(this.toolRegistry.list())}getAvailableTools(){return this.toolRegistry.list()}async getExecutionStats(){var r;let s=await this.conversationManager.getConversationStats(),e=this.toolRegistry.getStats(),t=this.toolCoordinator.getExecutionStats();return{conversations:s,tools:e,coordination:t,llm:{provider:((r=this.llm)==null?void 0:r.constructor.name)||"Unknown",initialized:!!this.llm}}}async updateConfig(s){await this.configManager.updateLLMConfig(s),await this.initializeLLM(),this.llm&&(this.taskPlanner=new H(this.llm,this.promptManager,this.toolRegistry))}async initializeLLM(){let s=this.configManager.getLLMConfig();this.llm=await R.createLLM(s)}async buildConversationHistory(){return(await this.conversationManager.getRecentMessages(5)).map(e=>`${e.role}: ${e.content}`).join(`
`)}countLLMCalls(s){return s.filter(e=>e.type==="thought"||e.type==="action"||e.type==="final_answer").length}assessComplexity(s){let e=s.filter(r=>r.type==="tool_result").length,t=s.filter(r=>r.type==="thought").length;return e===0&&t<=1?"low":e<=2&&t<=3?"medium":"high"}async cleanup(){await this.conversationManager.endConversation(),this.initialized=!1}};var h=require("obsidian"),N="ai-coach-chat-sidebar",oe=class extends h.ItemView{constructor(e,t){super(e);this.isProcessing=!1;this.currentTab="chat";this.orchestrationEngine=t}getViewType(){return N}getDisplayText(){return"AI Coach"}getIcon(){return"bot"}async onOpen(){console.log("\u{1F3A8} ChatSidebarView: \u5F00\u59CB\u521B\u5EFA\u4FA7\u8FB9\u680F\u754C\u9762");let e=this.containerEl.children[1];e.empty(),e.addClass("ai-coach-sidebar"),this.createTabHeader(e),this.createChatInterface(e),this.createMemoryInterface(e),this.switchTab("chat"),console.log("\u2705 ChatSidebarView: \u4FA7\u8FB9\u680F\u754C\u9762\u521B\u5EFA\u5B8C\u6210")}async onClose(){console.log("ChatSidebarView: \u5173\u95ED\u4FA7\u8FB9\u680F")}createTabHeader(e){let t=e.createDiv("ai-coach-tab-header"),r=t.createDiv("ai-coach-tab");r.setText("\u{1F4AC} \u5BF9\u8BDD"),r.addClass("ai-coach-tab-active"),r.addEventListener("click",()=>this.switchTab("chat"));let o=t.createDiv("ai-coach-tab");o.setText("\u{1F9E0} \u8BB0\u5FC6"),o.addEventListener("click",()=>this.switchTab("memory"))}createChatInterface(e){let t=e.createDiv("ai-coach-chat-panel");t.style.display="flex",t.style.flexDirection="column",t.style.height="calc(100% - 40px)",this.chatContainer=t.createDiv("ai-coach-chat-container"),this.chatContainer.style.flex="1",this.chatContainer.style.overflowY="auto",this.chatContainer.style.padding="10px",this.chatContainer.style.borderBottom="1px solid var(--background-modifier-border)";let r=t.createDiv("ai-coach-input-container");r.style.padding="10px",r.style.display="flex",r.style.flexDirection="column",r.style.gap="8px",this.inputArea=new h.TextAreaComponent(r),this.inputArea.inputEl.placeholder="\u8F93\u5165\u60A8\u7684\u95EE\u9898...",this.inputArea.inputEl.style.minHeight="60px",this.inputArea.inputEl.style.resize="vertical",this.inputArea.inputEl.addEventListener("keydown",i=>{i.key==="Enter"&&(i.ctrlKey||i.metaKey)&&(i.preventDefault(),this.sendMessage())});let o=r.createDiv();o.style.display="flex",o.style.gap="8px",this.sendButton=new h.ButtonComponent(o),this.sendButton.setButtonText("\u53D1\u9001"),this.sendButton.setCta(),this.sendButton.onClick(()=>this.sendMessage());let n=new h.ButtonComponent(o);n.setButtonText("\u6E05\u7A7A"),n.onClick(()=>this.clearChat()),this.addWelcomeMessage()}createMemoryInterface(e){let t=e.createDiv("ai-coach-memory-panel");t.style.display="none",t.style.height="calc(100% - 40px)",t.style.padding="10px",this.memoryContainer=t;let r=t.createEl("h3");r.setText("\u{1F9E0} \u957F\u671F\u8BB0\u5FC6\u7BA1\u7406"),r.style.marginTop="0";let o=new h.ButtonComponent(t);o.setButtonText("\u{1F504} \u5237\u65B0\u8BB0\u5FC6"),o.onClick(()=>this.loadMemories());let n=t.createDiv("ai-coach-memories-container");n.style.marginTop="15px",n.style.maxHeight="calc(100% - 100px)",n.style.overflowY="auto",this.loadMemories()}switchTab(e){this.currentTab=e,this.containerEl.querySelectorAll(".ai-coach-tab").forEach((n,i)=>{i===0&&e==="chat"||i===1&&e==="memory"?n.addClass("ai-coach-tab-active"):n.removeClass("ai-coach-tab-active")});let r=this.containerEl.querySelector(".ai-coach-chat-panel"),o=this.containerEl.querySelector(".ai-coach-memory-panel");e==="chat"?(r.style.display="flex",o.style.display="none"):(r.style.display="none",o.style.display="block",this.loadMemories())}addWelcomeMessage(){let e=this.chatContainer.createDiv("ai-coach-message ai-coach-message-assistant");e.innerHTML=`
      <div class="ai-coach-message-content">
        <strong>\u{1F44B} \u6B22\u8FCE\u4F7F\u7528 AI Coach Advanced\uFF01</strong><br><br>
        \u6211\u53EF\u4EE5\u5E2E\u52A9\u60A8\uFF1A<br>
        \u2022 \u{1F50D} \u641C\u7D22\u548C\u67E5\u8BE2\u60A8\u7684\u7B14\u8BB0<br>
        \u2022 \u{1F4DD} \u521B\u5EFA\u548C\u7F16\u8F91\u6587\u4EF6<br>
        \u2022 \u{1F4CB} \u4F7F\u7528\u6A21\u677F\u521B\u5EFA\u7ED3\u6784\u5316\u7B14\u8BB0<br>
        \u2022 \u{1F310} \u641C\u7D22\u7F51\u7EDC\u4FE1\u606F<br>
        \u2022 \u26A1 \u6267\u884C\u8BA1\u7B97\u548C\u4EE3\u7801<br>
        \u2022 \u{1F9E0} \u7BA1\u7406\u957F\u671F\u8BB0\u5FC6<br><br>
        \u8BF7\u8F93\u5165\u60A8\u7684\u95EE\u9898\u6216\u6307\u4EE4\uFF01
      </div>
    `}async sendMessage(){let e=this.inputArea.getValue().trim();if(console.log("\u{1F4AC} ChatSidebarView.sendMessage: \u7528\u6237\u53D1\u9001\u6D88\u606F:",e),!e||this.isProcessing){console.log("\u26A0\uFE0F \u6D88\u606F\u4E3A\u7A7A\u6216\u6B63\u5728\u5904\u7406\u4E2D\uFF0C\u5FFD\u7565");return}console.log("\u26A1 \u5F00\u59CB\u5904\u7406\u7528\u6237\u6D88\u606F..."),this.isProcessing=!0,this.updateSendButton();try{console.log("\u{1F4AC} \u6DFB\u52A0\u7528\u6237\u6D88\u606F\u5230\u754C\u9762..."),this.addMessage("user",e),this.inputArea.setValue(""),console.log("\u23F3 \u663E\u793A\u5904\u7406\u4E2D\u72B6\u6001...");let t=this.addMessage("assistant","\u6B63\u5728\u601D\u8003\u4E2D...");console.log("\u{1F3AF} \u8C03\u7528 orchestrationEngine.processUserInput...");let r=await this.orchestrationEngine.processUserInput(e);console.log("\u{1F3AF} processUserInput \u8FD4\u56DE\u7ED3\u679C:",{success:r.success,hasResponse:!!r.response,hasError:!!r.error,hasExecutionLog:!!(r.executionLog&&r.executionLog.length>0),toolsUsed:r.toolsUsed}),t&&t.remove(),r.success?(console.log("\u2705 \u5904\u7406\u6210\u529F\uFF0C\u6DFB\u52A0AI\u56DE\u590D..."),this.addMessage("assistant",r.response),r.executionLog&&r.executionLog.length>0&&(console.log("\u{1F4CB} \u6DFB\u52A0\u6267\u884C\u8BE6\u60C5..."),this.addExecutionDetails(r))):(console.log("\u274C \u5904\u7406\u5931\u8D25:",r.error),this.addMessage("assistant",`\u62B1\u6B49\uFF0C\u5904\u7406\u60A8\u7684\u8BF7\u6C42\u65F6\u51FA\u73B0\u4E86\u95EE\u9898\uFF1A${r.error||"\u672A\u77E5\u9519\u8BEF"}`))}catch(t){console.error("\u274C ChatSidebarView.sendMessage \u5F02\u5E38:",t),console.error("\u274C \u9519\u8BEF\u5806\u6808:",t.stack),this.addMessage("assistant",`\u5904\u7406\u6D88\u606F\u65F6\u53D1\u751F\u9519\u8BEF\uFF1A${t.message}`)}finally{console.log("\u{1F3C1} \u6D88\u606F\u5904\u7406\u5B8C\u6210\uFF0C\u91CD\u7F6E\u72B6\u6001"),this.isProcessing=!1,this.updateSendButton(),this.inputArea.inputEl.focus()}}addMessage(e,t){let r=this.chatContainer.createDiv(`ai-coach-message ai-coach-message-${e}`),o=r.createDiv("ai-coach-message-content");return o.innerHTML=this.formatMessage(t),this.chatContainer.scrollTop=this.chatContainer.scrollHeight,r}formatMessage(e){return e.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/`(.*?)`/g,"<code>$1</code>").replace(/\n/g,"<br>")}addExecutionDetails(e){var r;if(!e.executionLog||e.executionLog.length===0)return;let t=this.chatContainer.createDiv("ai-coach-execution-details");t.innerHTML=`
      <details>
        <summary>\u{1F4CB} \u6267\u884C\u8BE6\u60C5 (${((r=e.toolsUsed)==null?void 0:r.length)||0} \u4E2A\u5DE5\u5177)</summary>
        <div class="ai-coach-execution-log">
          ${e.executionLog.map(o=>`
            <div class="ai-coach-log-step">
              <strong>${o.type}:</strong> ${o.content.substring(0,100)}${o.content.length>100?"...":""}
            </div>
          `).join("")}
        </div>
      </details>
    `}updateSendButton(){this.sendButton.setDisabled(this.isProcessing),this.sendButton.setButtonText(this.isProcessing?"\u5904\u7406\u4E2D...":"\u53D1\u9001")}clearChat(){this.chatContainer.empty(),this.addWelcomeMessage(),new h.Notice("\u804A\u5929\u8BB0\u5F55\u5DF2\u6E05\u7A7A")}async loadMemories(){console.log("\u{1F9E0} \u52A0\u8F7D\u957F\u671F\u8BB0\u5FC6...");try{let t=this.orchestrationEngine.getAvailableTools().find(o=>o.name==="long_term_memory");if(!t){this.showMemoryError("\u957F\u671F\u8BB0\u5FC6\u5DE5\u5177\u672A\u627E\u5230");return}let r=await t.execute({action:"list"});r.success&&r.data?this.displayMemories(r.data):this.showMemoryError(r.error||"\u83B7\u53D6\u8BB0\u5FC6\u5931\u8D25")}catch(e){console.error("\u52A0\u8F7D\u8BB0\u5FC6\u5931\u8D25:",e),this.showMemoryError(`\u52A0\u8F7D\u8BB0\u5FC6\u5931\u8D25: ${e.message}`)}}displayMemories(e){var o;let t=this.memoryContainer.querySelector(".ai-coach-memories-container");if(!t)return;if(t.empty(),!e||e.length===0){t.createDiv("ai-coach-no-memories").setText("\u6682\u65E0\u957F\u671F\u8BB0\u5FC6");return}e.forEach((n,i)=>{let a=t.createDiv("ai-coach-memory-item");a.innerHTML=`
        <div class="ai-coach-memory-header">
          <strong>${n.key||`\u8BB0\u5FC6 ${i+1}`}</strong>
          <span class="ai-coach-memory-date">${new Date(n.timestamp||Date.now()).toLocaleDateString()}</span>
        </div>
        <div class="ai-coach-memory-content">${n.content||n.value||""}</div>
        <div class="ai-coach-memory-actions">
          <button class="ai-coach-btn ai-coach-btn-edit" data-key="${n.key}">\u7F16\u8F91</button>
          <button class="ai-coach-btn ai-coach-btn-delete" data-key="${n.key}">\u5220\u9664</button>
        </div>
      `;let c=a.querySelector(".ai-coach-btn-edit"),l=a.querySelector(".ai-coach-btn-delete");c==null||c.addEventListener("click",()=>this.editMemory(n)),l==null||l.addEventListener("click",()=>this.deleteMemory(n.key))});let r=t.createDiv("ai-coach-add-memory");r.innerHTML='<button class="ai-coach-btn ai-coach-btn-primary">+ \u6DFB\u52A0\u65B0\u8BB0\u5FC6</button>',(o=r.querySelector("button"))==null||o.addEventListener("click",()=>this.addNewMemory())}showMemoryError(e){let t=this.memoryContainer.querySelector(".ai-coach-memories-container");t&&(t.empty(),t.createDiv("ai-coach-memory-error").setText(`\u9519\u8BEF: ${e}`))}async editMemory(e){new ne(this.app,e,async r=>{try{let n=this.orchestrationEngine.getAvailableTools().find(i=>i.name==="long_term_memory");if(n){let i=await n.execute({action:"store",key:r.key,content:r.content,metadata:r.metadata});i.success?(new h.Notice("\u8BB0\u5FC6\u5DF2\u66F4\u65B0"),this.loadMemories()):new h.Notice(`\u66F4\u65B0\u5931\u8D25: ${i.error}`)}}catch(o){console.error("\u66F4\u65B0\u8BB0\u5FC6\u5931\u8D25:",o),new h.Notice(`\u66F4\u65B0\u5931\u8D25: ${o.message}`)}}).open()}async deleteMemory(e){if(confirm(`\u786E\u5B9A\u8981\u5220\u9664\u8BB0\u5FC6 "${e}" \u5417\uFF1F`))try{let r=this.orchestrationEngine.getAvailableTools().find(o=>o.name==="long_term_memory");if(r){let o=await r.execute({action:"delete",key:e});o.success?(new h.Notice("\u8BB0\u5FC6\u5DF2\u5220\u9664"),this.loadMemories()):new h.Notice(`\u5220\u9664\u5931\u8D25: ${o.error}`)}}catch(t){console.error("\u5220\u9664\u8BB0\u5FC6\u5931\u8D25:",t),new h.Notice(`\u5220\u9664\u5931\u8D25: ${t.message}`)}}async addNewMemory(){new ne(this.app,null,async t=>{try{let o=this.orchestrationEngine.getAvailableTools().find(n=>n.name==="long_term_memory");if(o){let n=await o.execute({action:"store",key:t.key,content:t.content,metadata:t.metadata});n.success?(new h.Notice("\u8BB0\u5FC6\u5DF2\u6DFB\u52A0"),this.loadMemories()):new h.Notice(`\u6DFB\u52A0\u5931\u8D25: ${n.error}`)}}catch(r){console.error("\u6DFB\u52A0\u8BB0\u5FC6\u5931\u8D25:",r),new h.Notice(`\u6DFB\u52A0\u5931\u8D25: ${r.message}`)}}).open()}},ne=class extends h.Modal{constructor(s,e,t){super(s),this.memory=e,this.onSave=t}onOpen(){var c,l,u,p,f;let{contentEl:s}=this;s.empty(),s.createEl("h2",{text:this.memory?"\u7F16\u8F91\u8BB0\u5FC6":"\u6DFB\u52A0\u65B0\u8BB0\u5FC6"});let e=s.createDiv();e.createEl("label",{text:"\u8BB0\u5FC6\u952E\u540D:"}),this.keyInput=new h.TextComponent(e),this.keyInput.inputEl.style.width="100%",this.keyInput.inputEl.style.marginTop="5px",this.keyInput.setValue(((c=this.memory)==null?void 0:c.key)||"");let t=s.createDiv();t.style.marginTop="15px",t.createEl("label",{text:"\u8BB0\u5FC6\u5185\u5BB9:"}),this.contentInput=new h.TextAreaComponent(t),this.contentInput.inputEl.style.width="100%",this.contentInput.inputEl.style.height="150px",this.contentInput.inputEl.style.marginTop="5px",this.contentInput.setValue(((l=this.memory)==null?void 0:l.content)||((u=this.memory)==null?void 0:u.value)||"");let r=s.createDiv();r.style.marginTop="15px",r.createEl("label",{text:"\u6807\u7B7E (\u7528\u9017\u53F7\u5206\u9694):"}),this.tagsInput=new h.TextComponent(r),this.tagsInput.inputEl.style.width="100%",this.tagsInput.inputEl.style.marginTop="5px";let o=((f=(p=this.memory)==null?void 0:p.metadata)==null?void 0:f.tags)||[];this.tagsInput.setValue(Array.isArray(o)?o.join(", "):"");let n=s.createDiv();n.style.marginTop="20px",n.style.display="flex",n.style.gap="10px",n.style.justifyContent="flex-end";let i=new h.ButtonComponent(n);i.setButtonText("\u53D6\u6D88"),i.onClick(()=>this.close());let a=new h.ButtonComponent(n);a.setButtonText("\u4FDD\u5B58"),a.setCta(),a.onClick(()=>this.save())}save(){let s=this.keyInput.getValue().trim(),e=this.contentInput.getValue().trim(),t=this.tagsInput.getValue().trim();if(!s){new h.Notice("\u8BF7\u8F93\u5165\u8BB0\u5FC6\u952E\u540D");return}if(!e){new h.Notice("\u8BF7\u8F93\u5165\u8BB0\u5FC6\u5185\u5BB9");return}let r=t?t.split(",").map(n=>n.trim()).filter(n=>n):[],o={key:s,content:e,metadata:{tags:r,lastModified:new Date().toISOString(),source:"manual"}};this.onSave(o),this.close()}onClose(){let{contentEl:s}=this;s.empty()}};var g=require("obsidian");var ie=class extends g.PluginSettingTab{constructor(e,t){super(e,t);d(this,"plugin");this.plugin=t}display(){let{containerEl:e}=this;e.empty(),e.createEl("h1",{text:"AI Coach Advanced \u8BBE\u7F6E"}),this.addImportExportSection(e),this.addLLMSection(e),this.addToolsSection(e),this.addUISection(e),this.addSecuritySection(e),this.addAdvancedSection(e)}addImportExportSection(e){e.createEl("h2",{text:"\u914D\u7F6E\u7BA1\u7406"});let t=e.createDiv({cls:"ai-coach-import-export"});new g.Setting(t).setName("\u5BFC\u51FA\u914D\u7F6E").setDesc("\u5BFC\u51FA\u5F53\u524D\u914D\u7F6E\u5230\u526A\u8D34\u677F\uFF08\u4E0D\u5305\u542B\u654F\u611F\u4FE1\u606F\uFF09").addButton(r=>r.setButtonText("\u5BFC\u51FA").onClick(async()=>{try{let n=this.plugin.configManager.exportConfig();await navigator.clipboard.writeText(n),new g.Notice("\u914D\u7F6E\u5DF2\u5BFC\u51FA\u5230\u526A\u8D34\u677F")}catch(o){new g.Notice("\u5BFC\u51FA\u5931\u8D25: "+o.message)}})),new g.Setting(t).setName("\u5BFC\u5165\u914D\u7F6E").setDesc("\u4ECE\u526A\u8D34\u677F\u5BFC\u5165\u914D\u7F6E").addButton(r=>r.setButtonText("\u5BFC\u5165").onClick(async()=>{try{let o=await navigator.clipboard.readText();await this.plugin.configManager.importConfig(o),this.display()}catch(o){new g.Notice("\u5BFC\u5165\u5931\u8D25: "+o.message)}})),new g.Setting(t).setName("\u91CD\u7F6E\u914D\u7F6E").setDesc("\u5C06\u6240\u6709\u8BBE\u7F6E\u91CD\u7F6E\u4E3A\u9ED8\u8BA4\u503C").addButton(r=>r.setButtonText("\u91CD\u7F6E").setWarning().onClick(async()=>{confirm("\u786E\u5B9A\u8981\u91CD\u7F6E\u6240\u6709\u914D\u7F6E\u5417\uFF1F\u6B64\u64CD\u4F5C\u4E0D\u53EF\u64A4\u9500\u3002")&&(await this.plugin.configManager.resetConfig(),this.display())}))}addLLMSection(e){e.createEl("h2",{text:"LLM\u914D\u7F6E"});let t=this.plugin.getConfig(),r=R.getSupportedProviders();new g.Setting(e).setName("LLM\u63D0\u4F9B\u5546").setDesc("\u9009\u62E9\u8981\u4F7F\u7528\u7684LLM\u670D\u52A1\u63D0\u4F9B\u5546").addDropdown(o=>{r.forEach(n=>{o.addOption(n.id,n.name)}),o.setValue(t.llm.provider).onChange(async n=>{await this.plugin.updateConfig({llm:{...t.llm,provider:n}}),this.display()})}),new g.Setting(e).setName("API\u5BC6\u94A5").setDesc("\u8F93\u5165LLM\u670D\u52A1\u7684API\u5BC6\u94A5").addText(o=>o.setPlaceholder("\u8F93\u5165API\u5BC6\u94A5").setValue(t.llm.apiKey).onChange(async n=>{let i=this.plugin.getConfig();await this.plugin.updateConfig({llm:{...i.llm,apiKey:n}})})),t.llm.provider==="custom"&&new g.Setting(e).setName("API\u57FA\u7840URL").setDesc("\u81EA\u5B9A\u4E49API\u7684\u57FA\u7840URL").addText(o=>o.setPlaceholder("https://api.example.com/v1").setValue(t.llm.baseUrl||"").onChange(async n=>{let i=this.plugin.getConfig();await this.plugin.updateConfig({llm:{...i.llm,baseUrl:n}})})),new g.Setting(e).setName("\u6A21\u578B").setDesc("\u9009\u62E9\u8981\u4F7F\u7528\u7684\u5177\u4F53\u6A21\u578B").addText(o=>o.setPlaceholder("\u4F8B\u5982: gpt-3.5-turbo").setValue(t.llm.model).onChange(async n=>{let i=this.plugin.getConfig();await this.plugin.updateConfig({llm:{...i.llm,model:n}})})),new g.Setting(e).setName("\u6700\u5927Token\u6570").setDesc("\u5355\u6B21\u8BF7\u6C42\u7684\u6700\u5927Token\u6570\u91CF").addSlider(o=>o.setLimits(100,8e3,100).setValue(t.llm.maxTokens).setDynamicTooltip().onChange(async n=>{let i=this.plugin.getConfig();await this.plugin.updateConfig({llm:{...i.llm,maxTokens:n}})})),new g.Setting(e).setName("\u6E29\u5EA6").setDesc("\u63A7\u5236\u56DE\u7B54\u7684\u968F\u673A\u6027\uFF0C0-2\u4E4B\u95F4\uFF0C\u503C\u8D8A\u9AD8\u8D8A\u968F\u673A").addSlider(o=>o.setLimits(0,2,.1).setValue(t.llm.temperature).setDynamicTooltip().onChange(async n=>{let i=this.plugin.getConfig();await this.plugin.updateConfig({llm:{...i.llm,temperature:n}})})),new g.Setting(e).setName("\u6D4B\u8BD5\u8FDE\u63A5").setDesc("\u6D4B\u8BD5LLM API\u8FDE\u63A5\u662F\u5426\u6B63\u5E38").addButton(o=>o.setButtonText("\u6D4B\u8BD5").onClick(async()=>{o.setButtonText("\u6D4B\u8BD5\u4E2D..."),o.setDisabled(!0);try{let n=await this.plugin.configManager.validateLLMConfig();if(!n.valid){new g.Notice("\u914D\u7F6E\u9A8C\u8BC1\u5931\u8D25: "+n.errors.join(", "));return}await new Promise(i=>setTimeout(i,1e3)),new g.Notice("\u8FDE\u63A5\u6D4B\u8BD5\u6210\u529F\uFF01")}catch(n){new g.Notice("\u8FDE\u63A5\u6D4B\u8BD5\u5931\u8D25: "+n.message)}finally{o.setButtonText("\u6D4B\u8BD5"),o.setDisabled(!1)}}))}addToolsSection(e){e.createEl("h2",{text:"\u5DE5\u5177\u914D\u7F6E"});let t=this.plugin.getConfig();e.createEl("h3",{text:"Vault\u77E5\u8BC6\u5E93"}),new g.Setting(e).setName("\u542F\u7528Vault\u67E5\u8BE2").setDesc("\u5141\u8BB8AI\u52A9\u624B\u641C\u7D22\u548C\u67E5\u8BE2\u4F60\u7684\u7B14\u8BB0\u5185\u5BB9").addToggle(r=>r.setValue(t.tools.vault.enabled).onChange(async o=>{await this.plugin.updateConfig({tools:{...t.tools,vault:{...t.tools.vault,enabled:o}}})})),new g.Setting(e).setName("\u542F\u7528\u81EA\u52A8\u7D22\u5F15").setDesc("\u81EA\u52A8\u4E3AVault\u5185\u5BB9\u521B\u5EFA\u641C\u7D22\u7D22\u5F15").addToggle(r=>r.setValue(t.tools.vault.indexingEnabled).onChange(async o=>{await this.plugin.updateConfig({tools:{...t.tools,vault:{...t.tools.vault,indexingEnabled:o}}})})),e.createEl("h3",{text:"\u7F51\u7EDC\u641C\u7D22"}),new g.Setting(e).setName("\u542F\u7528\u7F51\u7EDC\u641C\u7D22").setDesc("\u5141\u8BB8AI\u52A9\u624B\u8FDB\u884C\u7F51\u7EDC\u641C\u7D22\u83B7\u53D6\u6700\u65B0\u4FE1\u606F").addToggle(r=>r.setValue(t.tools.web.enabled).onChange(async o=>{await this.plugin.updateConfig({tools:{...t.tools,web:{...t.tools.web,enabled:o}}})})),e.createEl("h3",{text:"JavaScript\u6267\u884C"}),new g.Setting(e).setName("\u542F\u7528JS\u6267\u884C").setDesc("\u5141\u8BB8AI\u52A9\u624B\u751F\u6210\u548C\u6267\u884CJavaScript\u4EE3\u7801\uFF08\u9AD8\u98CE\u9669\u529F\u80FD\uFF09").addToggle(r=>r.setValue(t.tools.javascript.enabled).onChange(async o=>{await this.plugin.updateConfig({tools:{...t.tools,javascript:{...t.tools.javascript,enabled:o}}})}))}addUISection(e){e.createEl("h2",{text:"UI\u914D\u7F6E"});let t=this.plugin.getConfig();new g.Setting(e).setName("\u8BED\u8A00").setDesc("\u9009\u62E9\u754C\u9762\u8BED\u8A00").addDropdown(r=>r.addOption("zh","\u4E2D\u6587").addOption("en","English").setValue(t.ui.language).onChange(async o=>{await this.plugin.updateConfig({ui:{...t.ui,language:o}})})),new g.Setting(e).setName("\u663E\u793A\u8FDB\u5EA6").setDesc("\u5728\u6267\u884C\u4EFB\u52A1\u65F6\u663E\u793A\u8FDB\u5EA6\u63D0\u793A").addToggle(r=>r.setValue(t.ui.showProgress).onChange(async o=>{await this.plugin.updateConfig({ui:{...t.ui,showProgress:o}})}))}addSecuritySection(e){e.createEl("h2",{text:"\u5B89\u5168\u914D\u7F6E"});let t=this.plugin.getConfig();new g.Setting(e).setName("\u52A0\u5BC6API\u5BC6\u94A5").setDesc("\u5728\u672C\u5730\u5B58\u50A8\u4E2D\u52A0\u5BC6API\u5BC6\u94A5").addToggle(r=>r.setValue(t.security.encryptApiKeys).onChange(async o=>{await this.plugin.updateConfig({security:{...t.security,encryptApiKeys:o}})})),new g.Setting(e).setName("\u8BF7\u6C42\u9891\u7387\u9650\u5236").setDesc("\u6BCF\u5206\u949F\u6700\u5927\u8BF7\u6C42\u6570\u91CF").addSlider(r=>r.setLimits(10,300,10).setValue(t.security.maxRequestsPerMinute).setDynamicTooltip().onChange(async o=>{await this.plugin.updateConfig({security:{...t.security,maxRequestsPerMinute:o}})}))}addAdvancedSection(e){e.createEl("h2",{text:"\u9AD8\u7EA7\u914D\u7F6E"}),new g.Setting(e).setName("\u91CD\u5EFAVault\u7D22\u5F15").setDesc("\u91CD\u65B0\u4E3A\u6240\u6709\u7B14\u8BB0\u521B\u5EFA\u641C\u7D22\u7D22\u5F15").addButton(t=>t.setButtonText("\u91CD\u5EFA\u7D22\u5F15").onClick(async()=>{t.setButtonText("\u91CD\u5EFA\u4E2D..."),t.setDisabled(!0);try{await new Promise(r=>setTimeout(r,2e3)),new g.Notice("\u7D22\u5F15\u91CD\u5EFA\u5B8C\u6210")}catch(r){new g.Notice("\u7D22\u5F15\u91CD\u5EFA\u5931\u8D25: "+r.message)}finally{t.setButtonText("\u91CD\u5EFA\u7D22\u5F15"),t.setDisabled(!1)}})),new g.Setting(e).setName("\u6E05\u7406\u6570\u636E").setDesc("\u6E05\u7406\u6240\u6709\u5BF9\u8BDD\u5386\u53F2\u548C\u7F13\u5B58\u6570\u636E").addButton(t=>t.setButtonText("\u6E05\u7406").setWarning().onClick(async()=>{if(confirm("\u786E\u5B9A\u8981\u6E05\u7406\u6240\u6709\u6570\u636E\u5417\uFF1F\u6B64\u64CD\u4F5C\u4E0D\u53EF\u64A4\u9500\u3002"))try{await this.plugin.getMemoryManager().clearAllConversations(),new g.Notice("\u6570\u636E\u6E05\u7406\u5B8C\u6210")}catch(o){new g.Notice("\u6570\u636E\u6E05\u7406\u5931\u8D25: "+o.message)}}))}};var ye=require("obsidian");var ae=class{constructor(s,e){d(this,"statusBarItem");d(this,"orchestrationEngine");d(this,"updateInterval",null);d(this,"isActive",!1);this.statusBarItem=s,this.orchestrationEngine=e}initialize(){this.setupStatusBar(),this.startPeriodicUpdate()}setupStatusBar(){this.statusBarItem.addClass("ai-coach-status-bar"),this.statusBarItem.style&&(this.statusBarItem.style.cursor="pointer"),this.statusBarItem.addEventListener("click",()=>{this.showDetailedStatus()}),this.updateStatus("idle","AI Coach Ready")}updateStatus(s,e,t){this.statusBarItem.empty();let r=this.statusBarItem.createSpan({cls:"ai-coach-status-icon"});r.innerHTML=this.getStatusIcon(s);let o=this.statusBarItem.createSpan({cls:"ai-coach-status-text",text:e});if(t!==void 0){let i=this.statusBarItem.createDiv({cls:"ai-coach-progress-bar"}).createDiv({cls:"ai-coach-progress-fill",attr:{style:`width: ${Math.max(0,Math.min(100,t))}%`}})}this.statusBarItem.className=`ai-coach-status-bar ai-coach-status-${s}`}getStatusIcon(s){return{idle:"\u{1F916}",processing:"\u26A1",success:"\u2705",error:"\u274C",warning:"\u26A0\uFE0F"}[s]||"\u{1F916}"}showProcessing(s="\u5904\u7406\u4E2D...",e){this.isActive=!0,this.updateStatus("processing",s,e)}showSuccess(s="\u5B8C\u6210",e=3e3){this.updateStatus("success",s),this.isActive=!1,setTimeout(()=>{this.isActive||this.showIdle()},e)}showError(s="\u9519\u8BEF",e=5e3){this.updateStatus("error",s),this.isActive=!1,setTimeout(()=>{this.isActive||this.showIdle()},e)}showWarning(s="\u8B66\u544A",e=4e3){this.updateStatus("warning",s),setTimeout(()=>{this.isActive||this.showIdle()},e)}showIdle(){this.isActive=!1,this.updateStatus("idle","AI Coach Ready")}startPeriodicUpdate(){this.updateInterval=setInterval(async()=>{this.isActive||await this.updateIdleStatus()},3e4)}async updateIdleStatus(){try{let s=await this.orchestrationEngine.getExecutionStats(),e=this.orchestrationEngine.getConversationState(),t="AI Coach Ready";e==="active"?t=`\u5BF9\u8BDD\u4E2D (${s.conversations.currentConversationLength} \u6D88\u606F)`:s.conversations.totalConversations>0&&(t=`\u5C31\u7EEA (${s.conversations.totalConversations} \u5BF9\u8BDD)`),this.updateStatus("idle",t)}catch(s){console.warn("Failed to update idle status:",s)}}async showDetailedStatus(){var s;try{let e=await this.orchestrationEngine.getExecutionStats(),t=`
AI Coach Advanced \u72B6\u6001:

\u5BF9\u8BDD\u7EDF\u8BA1:
- \u603B\u5BF9\u8BDD\u6570: ${e.conversations.totalConversations}
- \u603B\u6D88\u606F\u6570: ${e.conversations.totalMessages}
- \u5F53\u524D\u5BF9\u8BDD\u957F\u5EA6: ${e.conversations.currentConversationLength}
- \u5BF9\u8BDD\u72B6\u6001: ${this.getConversationStateText(e.conversations.conversationState)}

\u5DE5\u5177\u7EDF\u8BA1:
- \u53EF\u7528\u5DE5\u5177\u6570: ${e.tools.totalTools||0}
- \u5DE5\u5177\u8C03\u7528\u6B21\u6570: ${((s=e.coordination)==null?void 0:s.totalExecutions)||0}
- \u6210\u529F\u7387: ${e.coordination?Math.round(e.coordination.successfulExecutions/e.coordination.totalExecutions*100):0}%

LLM\u72B6\u6001:
- \u63D0\u4F9B\u5546: ${e.llm.provider}
- \u72B6\u6001: ${e.llm.initialized?"\u5DF2\u8FDE\u63A5":"\u672A\u8FDE\u63A5"}
      `.trim(),r=new ye.Notice(t,8e3)}catch(e){new ye.Notice(`\u83B7\u53D6\u72B6\u6001\u4FE1\u606F\u5931\u8D25: ${e.message}`,3e3)}}getConversationStateText(s){return{idle:"\u7A7A\u95F2",active:"\u6D3B\u8DC3",processing:"\u5904\u7406\u4E2D",waiting:"\u7B49\u5F85\u4E2D",ended:"\u5DF2\u7ED3\u675F",error:"\u9519\u8BEF"}[s]||s}showToolProgress(s,e,t){let r=e/t*100,o=`\u6267\u884C ${s} (${e}/${t})`;this.showProcessing(o,r)}showThinking(s=1){let e=".".repeat(s%3+1);this.showProcessing(`AI\u601D\u8003\u4E2D${e}`)}cleanup(){this.updateInterval&&(clearInterval(this.updateInterval),this.updateInterval=null),this.statusBarItem.empty(),this.statusBarItem.removeEventListener("click",this.showDetailedStatus)}};var Ee=require("obsidian");var ve=require("obsidian"),ce=class{constructor(s,e=1e3){this.indices=new Map;this.chunkSize=1e3;this.overlapSize=200;this.app=s,this.vault=s.vault,this.chunkSize=e,this.overlapSize=Math.min(200,Math.floor(e*.2))}async indexFile(s){try{let e=s.content||await this.readFileContent(s.path);if(!e)return[];let t=this.extractMetadata(e,s),r=this.chunkText(e),o=[];for(let n=0;n<r.length;n++){let i=r[n],a={id:this.generateIndexId(s.path,n),filePath:s.path,content:i,embedding:void 0,metadata:{...t,chunkIndex:n,totalChunks:r.length,wordCount:this.countWords(i)},createdAt:new Date,updatedAt:new Date};o.push(a)}return this.indices.set(s.path,o),o}catch(e){return console.error(`Failed to index file ${s.path}:`,e),[]}}async updateIndex(s){try{let e=this.vault.getAbstractFileByPath(s);if(!(e instanceof ve.TFile))throw new Error(`File not found: ${s}`);let t={path:s,name:e.name,extension:e.extension,size:e.stat.size,mtime:e.stat.mtime};await this.indexFile(t)}catch(e){throw console.error(`Failed to update index for ${s}:`,e),e}}async deleteIndex(s){this.indices.delete(s)}async search(s){let{query:e,maxResults:t=10,threshold:r=.5}=s,o=[];for(let[n,i]of this.indices.entries())for(let a of i){let c=this.calculateTextSimilarity(e,a.content);c>=r&&o.push({file:{path:n},content:a.content,score:c,highlights:this.extractHighlights(e,a.content),metadata:a.metadata})}return o.sort((n,i)=>i.score-n.score),o.slice(0,t)}async getStats(){let s=this.indices.size,e=0,t=0;for(let r of this.indices.values())e+=r.length,t+=r.reduce((o,n)=>o+n.content.length,0);return{totalFiles:s,indexedFiles:s,totalChunks:e,lastUpdated:new Date,indexSize:t}}async readFileContent(s){try{let e=this.vault.getAbstractFileByPath(s);if(!(e instanceof ve.TFile))throw new Error(`File not found: ${s}`);return await this.vault.read(e)}catch(e){return console.error(`Failed to read file ${s}:`,e),""}}extractMetadata(s,e){let t={title:e.name.replace(/\.[^/.]+$/,""),tags:[],links:[],headings:[]},r=s.match(/^#\s+(.+)$/m);r&&(t.title=r[1].trim());let o=s.match(/#[\w\-_]+/g);o&&(t.tags=[...new Set(o.map(a=>a.slice(1)))]);let n=s.match(/\[\[([^\]]+)\]\]/g);n&&(t.links=[...new Set(n.map(a=>a.slice(2,-2)))]);let i=s.match(/^#{1,6}\s+(.+)$/gm);return i&&(t.headings=i.map(a=>{var u;let c=((u=a.match(/^#+/))==null?void 0:u[0].length)||1,l=a.replace(/^#+\s+/,"");return`${"#".repeat(c)} ${l}`})),t}chunkText(s){let e=[],t=this.splitIntoSentences(s),r="",o=0;for(let n of t){let i=n.length;o+i>this.chunkSize&&r?(e.push(r.trim()),r=this.getOverlapText(r)+n,o=r.length):(r+=n,o+=i)}return r.trim()&&e.push(r.trim()),e.filter(n=>n.length>50)}splitIntoSentences(s){return s.split(/[.!?]+/).map(e=>e.trim()).filter(e=>e.length>0).map(e=>e+". ")}getOverlapText(s){if(s.length<=this.overlapSize)return s;let e=s.slice(-this.overlapSize),t=e.lastIndexOf(" ");return t>this.overlapSize*.5?e.slice(t+1):e}calculateTextSimilarity(s,e){let t=s.toLowerCase().split(/\s+/).filter(n=>n.length>2),r=e.toLowerCase().split(/\s+/);if(t.length===0)return 0;let o=0;for(let n of t)r.some(i=>i.includes(n))&&o++;return o/t.length}extractHighlights(s,e,t=3){let r=s.toLowerCase().split(/\s+/).filter(i=>i.length>2),o=e.split(/[.!?]+/).filter(i=>i.trim().length>0),n=[];for(let i of o){let a=i.toLowerCase();r.some(l=>a.includes(l))&&n.length<t&&n.push(i.trim()+"...")}return n}countWords(s){return s.split(/\s+/).filter(e=>e.length>0).length}generateIndexId(s,e){return`${s}#${e}`}getAllIndices(){return new Map(this.indices)}clearAllIndices(){this.indices.clear()}async indexFiles(s){for(let t=0;t<s.length;t+=10){let r=s.slice(t,t+10);await Promise.all(r.map(o=>this.indexFile(o)))}}};var le=class extends x{constructor(e){super({name:"vault_query",description:"\u641C\u7D22\u548C\u67E5\u8BE2Vault\u4E2D\u7684\u7B14\u8BB0\u5185\u5BB9",category:"vault",version:"1.0.0",parameters:{type:"object",properties:{query:{type:"string",description:"\u641C\u7D22\u67E5\u8BE2\u5B57\u7B26\u4E32"},maxResults:{type:"number",description:"\u6700\u5927\u8FD4\u56DE\u7ED3\u679C\u6570",default:10,minimum:1,maximum:50},fileTypes:{type:"array",description:"\u6587\u4EF6\u7C7B\u578B\u8FC7\u6EE4",items:{type:"string"},default:["md"]},tags:{type:"array",description:"\u6807\u7B7E\u8FC7\u6EE4",items:{type:"string"}},includeContent:{type:"boolean",description:"\u662F\u5426\u5305\u542B\u6587\u4EF6\u5185\u5BB9",default:!0},threshold:{type:"number",description:"\u76F8\u4F3C\u5EA6\u9608\u503C",default:.3,minimum:0,maximum:1}},required:["query"]},permissions:{required:["vault_read"],optional:[],dangerous:!1,requiresConfirmation:!1},metadata:{author:"AI Coach Team",tags:["vault","search","query"],documentation:"\u5728Obsidian Vault\u4E2D\u641C\u7D22\u548C\u67E5\u8BE2\u7B14\u8BB0\u5185\u5BB9",examples:[{name:"\u57FA\u7840\u641C\u7D22",description:"\u641C\u7D22\u5305\u542B\u7279\u5B9A\u5173\u952E\u8BCD\u7684\u7B14\u8BB0",input:{query:"machine learning"},expectedOutput:{results:[]}},{name:"\u6807\u7B7E\u8FC7\u6EE4\u641C\u7D22",description:"\u641C\u7D22\u7279\u5B9A\u6807\u7B7E\u7684\u7B14\u8BB0",input:{query:"neural networks",tags:["ai","research"]},expectedOutput:{results:[]}}]}});this.initialized=!1;this.app=e,this.indexer=new ce(e)}async initialize(){if(!this.initialized)try{await this.buildInitialIndex(),this.initialized=!0,console.log("VaultQueryTool initialized successfully")}catch(e){throw console.error("Failed to initialize VaultQueryTool:",e),e}}async executeInternal(e,t){try{this.initialized||await this.initialize();let{query:r,maxResults:o=10,fileTypes:n=["md"],tags:i,includeContent:a=!0,threshold:c=.3}=e,l={query:r,maxResults:o,threshold:c,includeContent:a,fileTypes:n,tags:i},u=await this.searchVault(l);return{success:!0,data:{query:r,results:u,totalResults:u.length,searchOptions:l},metadata:{source:"vault_indexer",timestamp:new Date().toISOString(),indexStats:await this.indexer.getStats()}}}catch(r){return{success:!1,error:`Vault\u67E5\u8BE2\u5931\u8D25: ${r.message}`,metadata:{errorType:"vault_query_error"}}}}async searchVault(e){let t=await this.indexer.search(e),r=[];if(e.useSemanticSearch!==!1)try{r=await this.performSemanticSearch(e)}catch(n){console.warn("Semantic search failed, falling back to basic search:",n)}return this.mergeSearchResults(t,r).map(n=>{var i;return{content:n.content,file:((i=n.file)==null?void 0:i.path)||n.filePath,score:n.score,metadata:{highlights:n.highlights||this.generateHighlights(n.content,e.query),searchType:n.searchType||"basic",...n.metadata}}})}async performSemanticSearch(e){if(!this.embeddingService)return[];try{let t=await this.embeddingService.generateEmbedding(e.query);return(await this.vectorStore.search(t,e.maxResults*2,e.threshold)).map(o=>{var n;return{...o,searchType:"semantic",filePath:((n=o.metadata)==null?void 0:n.file)||o.file}})}catch(t){return console.error("Semantic search error:",t),[]}}mergeSearchResults(e,t){var o,n;let r=new Map;for(let i of e){let a=`${((o=i.file)==null?void 0:o.path)||i.filePath}_${i.chunkIndex||0}`;r.set(a,{...i,searchType:"basic"})}for(let i of t){let a=`${i.filePath}_${((n=i.metadata)==null?void 0:n.chunkIndex)||0}`,c=r.get(a);c?(c.score=Math.max(c.score,i.score*1.1),c.searchType="hybrid"):r.set(a,i)}return Array.from(r.values()).sort((i,a)=>a.score-i.score).slice(0,20)}generateHighlights(e,t){if(!e||!t)return[];let r=[],o=t.toLowerCase().split(/\s+/),n=e.split(/[.!?]+/).filter(i=>i.trim().length>10);for(let i of n){let a=i.toLowerCase(),c=0;for(let l of o)a.includes(l)&&(c+=l.length);if(c>0&&r.push(i.trim()),r.length>=3)break}return r}async buildInitialIndex(){let e=this.app.vault.getMarkdownFiles(),t=[];for(let r of e)try{let o=await this.app.vault.read(r),n={path:r.path,name:r.name,extension:r.extension,size:r.stat.size,mtime:r.stat.mtime,content:o};t.push(n)}catch(o){console.warn(`Failed to read file ${r.path}:`,o)}await this.indexer.indexFiles(t)}async rebuildIndex(){this.indexer.clearAllIndices(),await this.buildInitialIndex()}async updateFileIndex(e){await this.indexer.updateIndex(e)}async deleteFileIndex(e){await this.indexer.deleteIndex(e)}async getRelatedFiles(e,t=5){try{let r=this.app.vault.getAbstractFileByPath(e);if(!(r instanceof Ee.TFile))throw new Error(`File not found: ${e}`);let o=await this.app.vault.read(r),a={query:this.extractKeywords(o).slice(0,5).join(" "),maxResults:t+1,threshold:.2,includeContent:!0,fileTypes:["md"]};return(await this.searchVault(a)).filter(l=>l.file!==e).slice(0,t)}catch(r){return console.error(`Failed to get related files for ${e}:`,r),[]}}extractKeywords(e){let t=e.toLowerCase().replace(/[^\w\s]/g," ").split(/\s+/).filter(o=>o.length>3),r={};return t.forEach(o=>{r[o]=(r[o]||0)+1}),Object.entries(r).sort(([,o],[,n])=>n-o).slice(0,10).map(([o])=>o)}async searchByTags(e,t=10){let r={query:e.join(" "),maxResults:t,threshold:.1,includeContent:!0,tags:e,fileTypes:["md"]};return await this.searchVault(r)}async searchByLinks(e,t=10){let r={query:`[[${e}]]`,maxResults:t,threshold:.5,includeContent:!0,fileTypes:["md"]};return await this.searchVault(r)}async getIndexStats(){return await this.indexer.getStats()}async getSearchSuggestions(e){let t=this.indexer.getAllIndices(),r=new Set;for(let o of t.values())for(let n of o){let i=n.content.toLowerCase(),a=e.toLowerCase();if(i.includes(a)){let c=i.split(/\s+/);for(let l=0;l<c.length-1;l++){let u=c.slice(l,l+3).join(" ");u.includes(a)&&u.length>e.length&&r.add(u)}}}return Array.from(r).slice(0,10)}};var ue=class extends x{constructor(){super({name:"web_search",description:"\u5728\u7F51\u7EDC\u4E0A\u641C\u7D22\u4FE1\u606F\uFF0C\u652F\u6301\u591A\u79CD\u641C\u7D22\u5F15\u64CE",category:"web",version:"1.0.0",parameters:{type:"object",properties:{query:{type:"string",description:"\u641C\u7D22\u67E5\u8BE2\u5B57\u7B26\u4E32"},engine:{type:"string",description:"\u641C\u7D22\u5F15\u64CE",enum:["duckduckgo","google","bing"],default:"duckduckgo"},maxResults:{type:"number",description:"\u6700\u5927\u8FD4\u56DE\u7ED3\u679C\u6570",default:5,minimum:1,maximum:20},language:{type:"string",description:"\u641C\u7D22\u8BED\u8A00",default:"zh-CN"},safeSearch:{type:"boolean",description:"\u542F\u7528\u5B89\u5168\u641C\u7D22",default:!0},region:{type:"string",description:"\u641C\u7D22\u5730\u533A",default:"CN"}},required:["query"]},permissions:{required:["web_access"],optional:["web_api_key"],dangerous:!1,requiresConfirmation:!1},metadata:{author:"AI Coach Team",tags:["web","search","information"],documentation:"\u5728\u7F51\u7EDC\u4E0A\u641C\u7D22\u4FE1\u606F\uFF0C\u83B7\u53D6\u6700\u65B0\u7684\u5916\u90E8\u77E5\u8BC6",examples:[{name:"\u57FA\u7840\u641C\u7D22",description:"\u641C\u7D22\u7279\u5B9A\u4E3B\u9898\u7684\u4FE1\u606F",input:{query:"artificial intelligence latest news"},expectedOutput:{results:[]}},{name:"\u6307\u5B9A\u641C\u7D22\u5F15\u64CE",description:"\u4F7F\u7528\u7279\u5B9A\u641C\u7D22\u5F15\u64CE\u8FDB\u884C\u641C\u7D22",input:{query:"machine learning",engine:"google",maxResults:10},expectedOutput:{results:[]}}]}});this.searchEngines=new Map;this.initializeSearchEngines()}async executeInternal(e,t){try{let{query:r,engine:o="duckduckgo",maxResults:n=5,language:i="zh-CN",safeSearch:a=!0,region:c="CN"}=e,l=this.searchEngines.get(o);if(!l)return{success:!1,error:`\u4E0D\u652F\u6301\u7684\u641C\u7D22\u5F15\u64CE: ${o}`};let u={query:r,maxResults:n,language:i,safeSearch:a,region:c},p=await l.search(u);return{success:!0,data:{query:r,engine:o,results:p,totalResults:p.length,searchOptions:u},metadata:{source:o,timestamp:new Date().toISOString(),searchTime:Date.now()}}}catch(r){return{success:!1,error:`\u7F51\u7EDC\u641C\u7D22\u5931\u8D25: ${r.message}`,metadata:{errorType:"web_search_error"}}}}initializeSearchEngines(){this.searchEngines.set("duckduckgo",new we),this.searchEngines.set("google",new xe),this.searchEngines.set("bing",new Te)}addSearchEngine(e,t){this.searchEngines.set(e,t)}getSupportedEngines(){return Array.from(this.searchEngines.keys())}},we=class{constructor(){this.baseUrl="https://api.duckduckgo.com/"}async search(s){try{let{query:e,maxResults:t}=s,r=`${this.baseUrl}?q=${encodeURIComponent(e)}&format=json&no_html=1&skip_disambig=1`,o=await fetch(r);if(!o.ok)throw new Error(`DuckDuckGo API error: ${o.status}`);let n=await o.json(),i=[];if(n.Abstract&&i.push({title:n.Heading||"DuckDuckGo Instant Answer",url:n.AbstractURL||"",snippet:n.Abstract,score:1}),n.RelatedTopics&&Array.isArray(n.RelatedTopics))for(let a of n.RelatedTopics.slice(0,t-i.length))a.Text&&a.FirstURL&&i.push({title:a.Text.split(" - ")[0]||"Related Topic",url:a.FirstURL,snippet:a.Text,score:.8});return i.slice(0,t)}catch(e){throw console.error("DuckDuckGo search error:",e),e}}isConfigured(){return!0}configure(s){}},xe=class{constructor(){this.apiKey="";this.searchEngineId="";this.baseUrl="https://www.googleapis.com/customsearch/v1"}async search(s){if(!this.isConfigured())throw new Error("Google Search API not configured");try{let{query:e,maxResults:t,language:r,safeSearch:o}=s,n=new URLSearchParams({key:this.apiKey,cx:this.searchEngineId,q:e,num:Math.min(t,10).toString(),hl:r,safe:o?"active":"off"}),i=`${this.baseUrl}?${n}`,a=await fetch(i);if(!a.ok)throw new Error(`Google Search API error: ${a.status}`);let c=await a.json(),l=[];if(c.items&&Array.isArray(c.items))for(let u of c.items)l.push({title:u.title||"",url:u.link||"",snippet:u.snippet||"",score:.9});return l}catch(e){throw console.error("Google search error:",e),e}}isConfigured(){return!!(this.apiKey&&this.searchEngineId)}configure(s){this.apiKey=s.apiKey||"",this.searchEngineId=s.searchEngineId||""}},Te=class{constructor(){this.apiKey="";this.baseUrl="https://api.bing.microsoft.com/v7.0/search"}async search(s){if(!this.isConfigured())throw new Error("Bing Search API not configured");try{let{query:e,maxResults:t,language:r,safeSearch:o}=s,n=new URLSearchParams({q:e,count:Math.min(t,50).toString(),mkt:r,safeSearch:o?"Strict":"Off"}),i=`${this.baseUrl}?${n}`,a=await fetch(i,{headers:{"Ocp-Apim-Subscription-Key":this.apiKey}});if(!a.ok)throw new Error(`Bing Search API error: ${a.status}`);let c=await a.json(),l=[];if(c.webPages&&c.webPages.value)for(let u of c.webPages.value)l.push({title:u.name||"",url:u.url||"",snippet:u.snippet||"",score:.85});return l}catch(e){throw console.error("Bing search error:",e),e}}isConfigured(){return!!this.apiKey}configure(s){this.apiKey=s.apiKey||""}};var B=class{constructor(s={}){this.timeout=s.timeout||5e3,this.memoryLimit=s.memoryLimit||10*1024*1024,this.allowedGlobals=new Set(s.allowedGlobals||this.getDefaultAllowedGlobals()),this.blockedPatterns=s.blockedPatterns||this.getDefaultBlockedPatterns()}async execute(s,e={}){let t=Date.now();try{let r=this.preprocessCode(s),o=this.validateCodeSecurity(r);if(!o.safe)return{success:!1,error:`\u4EE3\u7801\u5B89\u5168\u68C0\u67E5\u5931\u8D25: ${o.reason}`,executionTime:Date.now()-t};let n=this.createSandboxEnvironment(e),i=await this.executeInSandbox(r,n);return{success:!0,result:i.value,output:i.output,executionTime:Date.now()-t,memoryUsed:i.memoryUsed}}catch(r){return{success:!1,error:r.message,executionTime:Date.now()-t}}}preprocessCode(s){let e=s.replace(/\/\*[\s\S]*?\*\//g,"");return e=e.replace(/\/\/.*$/gm,""),e.includes("use strict")||(e=`"use strict";
`+e),e}validateCodeSecurity(s){for(let t of this.blockedPatterns)if(t.test(s))return{safe:!1,reason:`\u4EE3\u7801\u5305\u542B\u88AB\u7981\u6B62\u7684\u6A21\u5F0F: ${t.source}`};return s.length>5e4?{safe:!1,reason:"\u4EE3\u7801\u957F\u5EA6\u8D85\u8FC7\u9650\u5236"}:this.getMaxNestingDepth(s)>20?{safe:!1,reason:"\u4EE3\u7801\u5D4C\u5957\u6DF1\u5EA6\u8FC7\u6DF1"}:{safe:!0}}createSandboxEnvironment(s){let e=[];return{Object,Array,String,Number,Boolean,Date,Math,JSON,RegExp,Error,console:{log:(...r)=>e.push(r.map(o=>String(o)).join(" ")),warn:(...r)=>e.push("WARN: "+r.map(o=>String(o)).join(" ")),error:(...r)=>e.push("ERROR: "+r.map(o=>String(o)).join(" ")),info:(...r)=>e.push("INFO: "+r.map(o=>String(o)).join(" "))},...s,__output:e,__memoryUsage:0}}async executeInSandbox(s,e){return new Promise((t,r)=>{let o=setTimeout(()=>{r(new Error("\u4EE3\u7801\u6267\u884C\u8D85\u65F6"))},this.timeout);try{let i=new Function(...Object.keys(e),`
          try {
            ${s}
          } catch (error) {
            throw error;
          }
        `)(...Object.values(e));clearTimeout(o),t({value:i,output:e.__output.join(`
`),memoryUsed:this.estimateMemoryUsage(e)})}catch(n){clearTimeout(o),r(n)}})}estimateMemoryUsage(s){try{return JSON.stringify(s).length*2}catch(e){return 0}}getMaxNestingDepth(s){let e=0,t=0;for(let r of s)r==="{"||r==="("||r==="["?(t++,e=Math.max(e,t)):(r==="}"||r===")"||r==="]")&&t--;return e}getDefaultAllowedGlobals(){return["Object","Array","String","Number","Boolean","Date","Math","JSON","RegExp","Error","parseInt","parseFloat","isNaN","isFinite","encodeURIComponent","decodeURIComponent"]}getDefaultBlockedPatterns(){return[/eval\s*\(/,/Function\s*\(/,/setTimeout\s*\(/,/setInterval\s*\(/,/require\s*\(/,/import\s+/,/export\s+/,/process\./,/global\./,/window\./,/document\./,/fetch\s*\(/,/XMLHttpRequest/,/WebSocket/,/localStorage/,/sessionStorage/,/indexedDB/,/navigator\./,/location\./,/history\./,/alert\s*\(/,/confirm\s*\(/,/prompt\s*\(/,/while\s*\(\s*true\s*\)/,/for\s*\(\s*;\s*;\s*\)/,/\.\s*constructor/,/__proto__/,/prototype\s*\[/]}};var me=class{constructor(){this.syntaxChecker=new Ce,this.securityAnalyzer=new be,this.codeFormatter=new Me}async validateCode(s,e={}){let t={valid:!0,errors:[],warnings:[],suggestions:[],processedCode:s};try{let r=this.extractCodeFromMarkdown(s),o=await this.syntaxChecker.check(r);o.valid||(t.valid=!1,t.errors.push(...o.errors)),t.warnings.push(...o.warnings);let n=await this.securityAnalyzer.analyze(r);n.safe||(t.valid=!1,t.errors.push(...n.risks.map(i=>`\u5B89\u5168\u98CE\u9669: ${i}`))),t.warnings.push(...n.warnings),t.valid&&e.format?t.processedCode=await this.codeFormatter.format(r):t.processedCode=r,t.suggestions=this.generateSuggestions(r,o,n)}catch(r){t.valid=!1,t.errors.push(`\u9A8C\u8BC1\u8FC7\u7A0B\u51FA\u9519: ${r.message}`)}return t}extractCodeFromMarkdown(s){let e=/```(?:javascript|js)?\n?([\s\S]*?)```/gi,t=s.match(e);if(t&&t.length>0)return t[0].replace(/```(?:javascript|js)?\n?/gi,"").replace(/```$/g,"").trim();let r=/`([^`]+)`/g,o=s.match(r);return o&&o.length>0?o.map(n=>n.replace(/`/g,"")).join(`
`):s.trim()}generateSuggestions(s,e,t){let r=[];return e.suggestions&&r.push(...e.suggestions),t.suggestions&&r.push(...t.suggestions),s.includes("use strict")||r.push('\u5EFA\u8BAE\u6DFB\u52A0 "use strict" \u4EE5\u542F\u7528\u4E25\u683C\u6A21\u5F0F'),s.includes("var ")&&r.push("\u5EFA\u8BAE\u4F7F\u7528 let \u6216 const \u66FF\u4EE3 var"),s.includes("==")&&!s.includes("===")&&r.push("\u5EFA\u8BAE\u4F7F\u7528 === \u66FF\u4EE3 == \u8FDB\u884C\u4E25\u683C\u6BD4\u8F83"),r}},Ce=class{async check(s){let e={valid:!0,errors:[],warnings:[],suggestions:[]};try{new Function(s)}catch(t){e.valid=!1,e.errors.push(`\u8BED\u6CD5\u9519\u8BEF: ${t.message}`)}return this.checkCommonIssues(s,e),e}checkCommonIssues(s,e){let t={"(":")","[":"]","{":"}"},r=[];for(let u of s)if(u in t)r.push(u);else if(Object.values(t).includes(u)){let p=r.pop();if(!p||t[p]!==u){e.warnings.push("\u53EF\u80FD\u5B58\u5728\u672A\u5339\u914D\u7684\u62EC\u53F7");break}}r.length>0&&e.warnings.push("\u53EF\u80FD\u5B58\u5728\u672A\u95ED\u5408\u7684\u62EC\u53F7");let o=/\b([a-zA-Z_$][a-zA-Z0-9_$]*)\s*(?=\s*[^=]|$)/g,n=new Set,i=new Set,a=/(?:var|let|const)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,c;for(;(c=a.exec(s))!==null;)n.add(c[1]);let l=/function\s*\w*\s*\(([^)]*)\)/g;for(;(c=l.exec(s))!==null;)c[1].split(",").map(p=>p.trim().split(/\s+/)[0]).forEach(p=>{p&&n.add(p)});for(;(c=o.exec(s))!==null;){let u=c[1];this.isBuiltinOrGlobal(u)||i.add(u)}for(let u of i)n.has(u)||e.warnings.push(`\u53EF\u80FD\u4F7F\u7528\u4E86\u672A\u5B9A\u4E49\u7684\u53D8\u91CF: ${u}`)}isBuiltinOrGlobal(s){return["console","Math","Date","Array","Object","String","Number","Boolean","JSON","RegExp","Error","parseInt","parseFloat","isNaN","isFinite","undefined","null","true","false","Infinity","NaN"].includes(s)}},be=class{constructor(){this.dangerousPatterns=[{pattern:/eval\s*\(/,risk:"\u4F7F\u7528eval\u51FD\u6570\u53EF\u80FD\u5BFC\u81F4\u4EE3\u7801\u6CE8\u5165",severity:"high"},{pattern:/Function\s*\(/,risk:"\u4F7F\u7528Function\u6784\u9020\u51FD\u6570\u53EF\u80FD\u4E0D\u5B89\u5168",severity:"high"},{pattern:/setTimeout\s*\(.*string/,risk:"\u4F7F\u7528\u5B57\u7B26\u4E32\u4F5C\u4E3AsetTimeout\u53C2\u6570\u4E0D\u5B89\u5168",severity:"medium"},{pattern:/setInterval\s*\(.*string/,risk:"\u4F7F\u7528\u5B57\u7B26\u4E32\u4F5C\u4E3AsetInterval\u53C2\u6570\u4E0D\u5B89\u5168",severity:"medium"},{pattern:/document\.write/,risk:"document.write\u53EF\u80FD\u5BFC\u81F4XSS",severity:"medium"},{pattern:/innerHTML\s*=/,risk:"\u76F4\u63A5\u8BBE\u7F6EinnerHTML\u53EF\u80FD\u5BFC\u81F4XSS",severity:"medium"},{pattern:/while\s*\(\s*true\s*\)/,risk:"\u65E0\u9650\u5FAA\u73AF\u53EF\u80FD\u5BFC\u81F4\u7A0B\u5E8F\u5361\u6B7B",severity:"high"},{pattern:/for\s*\(\s*;\s*;\s*\)/,risk:"\u65E0\u9650\u5FAA\u73AF\u53EF\u80FD\u5BFC\u81F4\u7A0B\u5E8F\u5361\u6B7B",severity:"high"}]}async analyze(s){let e={safe:!0,risks:[],warnings:[],suggestions:[]};for(let{pattern:r,risk:o,severity:n}of this.dangerousPatterns)r.test(s)&&(n==="high"?(e.safe=!1,e.risks.push(o)):e.warnings.push(o));return this.calculateComplexity(s)>20&&e.warnings.push("\u4EE3\u7801\u590D\u6742\u5EA6\u8F83\u9AD8\uFF0C\u5EFA\u8BAE\u7B80\u5316"),s.includes("innerHTML")&&e.suggestions.push("\u5EFA\u8BAE\u4F7F\u7528textContent\u6216\u5B89\u5168\u7684DOM\u64CD\u4F5C\u65B9\u6CD5"),s.includes("eval")&&e.suggestions.push("\u5EFA\u8BAE\u907F\u514D\u4F7F\u7528eval\uFF0C\u8003\u8651\u4F7F\u7528JSON.parse\u6216\u5176\u4ED6\u5B89\u5168\u65B9\u6CD5"),e}calculateComplexity(s){let e=1;return e+=(s.match(/if\s*\(/g)||[]).length,e+=(s.match(/else\s+if/g)||[]).length,e+=(s.match(/switch\s*\(/g)||[]).length,e+=(s.match(/case\s+/g)||[]).length,e+=(s.match(/for\s*\(/g)||[]).length,e+=(s.match(/while\s*\(/g)||[]).length,e+=(s.match(/do\s*{/g)||[]).length,e+=(s.match(/&&/g)||[]).length,e+=(s.match(/\|\|/g)||[]).length,e+=(s.match(/try\s*{/g)||[]).length,e+=(s.match(/catch\s*\(/g)||[]).length,e}},Me=class{async format(s){let e=s;return e=this.addIndentation(e),e=this.addSemicolons(e),e}addIndentation(s){let e=s.split(`
`),t=0,r=2;return e.map(o=>{let n=o.trim();if(!n)return"";(n.startsWith("}")||n.startsWith("]")||n.startsWith(")"))&&(t=Math.max(0,t-1));let i=" ".repeat(t*r)+n;return(n.endsWith("{")||n.endsWith("[")||n.endsWith("("))&&t++,i}).join(`
`)}addSemicolons(s){return s.replace(/([^;{}\s])\s*\n/g,`$1;
`)}};var pe=class extends x{constructor(e){super({name:"javascript_executor",description:"\u5728\u5B89\u5168\u6C99\u7BB1\u4E2D\u6267\u884CJavaScript\u4EE3\u7801",category:"javascript",version:"1.0.0",parameters:{type:"object",properties:{code:{type:"string",description:"\u8981\u6267\u884C\u7684JavaScript\u4EE3\u7801"},context:{type:"object",description:"\u6267\u884C\u4E0A\u4E0B\u6587\u53D8\u91CF"},timeout:{type:"number",description:"\u6267\u884C\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09",default:5e3,minimum:1e3,maximum:3e4},validateOnly:{type:"boolean",description:"\u4EC5\u9A8C\u8BC1\u4EE3\u7801\uFF0C\u4E0D\u6267\u884C",default:!1},formatCode:{type:"boolean",description:"\u662F\u5426\u683C\u5F0F\u5316\u4EE3\u7801",default:!0},allowDangerous:{type:"boolean",description:"\u662F\u5426\u5141\u8BB8\u6F5C\u5728\u5371\u9669\u7684\u64CD\u4F5C",default:!1}},required:["code"]},permissions:{required:["javascript_execute"],optional:["javascript_dangerous"],dangerous:!0,requiresConfirmation:!0},metadata:{author:"AI Coach Team",tags:["javascript","execution","sandbox"],documentation:"\u5728\u5B89\u5168\u7684\u6C99\u7BB1\u73AF\u5883\u4E2D\u6267\u884CJavaScript\u4EE3\u7801",examples:[{name:"\u7B80\u5355\u8BA1\u7B97",description:"\u6267\u884C\u7B80\u5355\u7684\u6570\u5B66\u8BA1\u7B97",input:{code:"const result = 2 + 3; console.log(result); result;"},expectedOutput:{result:5,output:"5"}},{name:"\u6570\u7EC4\u64CD\u4F5C",description:"\u5BF9\u6570\u7EC4\u8FDB\u884C\u64CD\u4F5C",input:{code:"const arr = [1, 2, 3]; const doubled = arr.map(x => x * 2); doubled;",context:{}},expectedOutput:{result:[2,4,6]}}]}});this.executionHistory=[];this.maxHistorySize=50;this.sandbox=new B(e),this.validator=new me}async executeInternal(e,t){let r=Date.now();try{let{code:o,context:n={},timeout:i=5e3,validateOnly:a=!1,formatCode:c=!0,allowDangerous:l=!1}=e,u={format:c,allowDangerousPatterns:l},p=await this.validator.validateCode(o,u);if(!p.valid){let k=this.createExecutionRecord(o,{success:!1,error:p.errors.join("; "),executionTime:Date.now()-r},t);return this.addToHistory(k),{success:!1,error:`\u4EE3\u7801\u9A8C\u8BC1\u5931\u8D25: ${p.errors.join("; ")}`,data:{validation:p},metadata:{executionTime:Date.now()-r,validationOnly:!0}}}if(a)return{success:!0,data:{validation:p,processedCode:p.processedCode},metadata:{executionTime:Date.now()-r,validationOnly:!0}};if(p.warnings.length>0&&!l&&t&&!t.permissions.includes("javascript_dangerous"))return{success:!1,error:"\u4EE3\u7801\u5305\u542B\u6F5C\u5728\u98CE\u9669\uFF0C\u9700\u8981\u5371\u9669\u64CD\u4F5C\u6743\u9650",data:{validation:p,warnings:p.warnings}};this.sandbox=new B({timeout:i});let f=await this.sandbox.execute(p.processedCode,n),C=this.createExecutionRecord(o,f,t);return this.addToHistory(C),{success:f.success,data:{result:f.result,output:f.output,validation:p,processedCode:p.processedCode},error:f.error,metadata:{executionTime:f.executionTime,memoryUsed:f.memoryUsed,codeLength:o.length,processedCodeLength:p.processedCode.length}}}catch(o){let n=this.createExecutionRecord(e.code,{success:!1,error:o.message,executionTime:Date.now()-r},t);return this.addToHistory(n),{success:!1,error:`JavaScript\u6267\u884C\u5931\u8D25: ${o.message}`,metadata:{executionTime:Date.now()-r,errorType:"execution_error"}}}}async validateInternal(e){let t=[],r=[],{code:o,timeout:n}=e;return!o||typeof o!="string"?t.push("\u4EE3\u7801\u53C2\u6570\u5FC5\u987B\u662F\u975E\u7A7A\u5B57\u7B26\u4E32"):(o.length>1e5&&t.push("\u4EE3\u7801\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7100KB"),o.length<5&&r.push("\u4EE3\u7801\u8FC7\u77ED\uFF0C\u53EF\u80FD\u65E0\u6CD5\u6267\u884C\u6709\u610F\u4E49\u7684\u64CD\u4F5C")),n&&(n<1e3||n>3e4)&&t.push("\u8D85\u65F6\u65F6\u95F4\u5FC5\u987B\u57281000-30000\u6BEB\u79D2\u4E4B\u95F4"),{valid:t.length===0,errors:t,warnings:r}}createExecutionRecord(e,t,r){return{id:this.generateExecutionId(),code:e.substring(0,1e3),result:t,timestamp:new Date,userId:(r==null?void 0:r.userId)||"anonymous",sessionId:r==null?void 0:r.sessionId}}addToHistory(e){this.executionHistory.push(e),this.executionHistory.length>this.maxHistorySize&&(this.executionHistory=this.executionHistory.slice(-this.maxHistorySize))}generateExecutionId(){return`js_exec_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}getExecutionHistory(e){let t=[...this.executionHistory].reverse();return e?t.slice(0,e):t}getExecutionStats(){let e=this.executionHistory.length,t=this.executionHistory.filter(i=>i.result.success).length,r=e-t,o=e>0?this.executionHistory.reduce((i,a)=>i+a.result.executionTime,0)/e:0,n=this.executionHistory.reduce((i,a)=>i+a.code.length,0);return{totalExecutions:e,successfulExecutions:t,failedExecutions:r,averageExecutionTime:Math.round(o),totalCodeLength:n}}clearExecutionHistory(){this.executionHistory=[]}async getCodeSuggestions(e){return(await this.validator.validateCode(e,{format:!1})).suggestions}async formatCode(e){return(await this.validator.validateCode(e,{format:!0})).processedCode}};var T=require("obsidian");var de=class extends x{constructor(e){super("file_operation","Obsidian\u6587\u4EF6\u64CD\u4F5C","\u521B\u5EFA\u3001\u7F16\u8F91\u3001\u5220\u9664\u3001\u79FB\u52A8Obsidian\u6587\u4EF6\u548C\u6587\u4EF6\u5939","obsidian",{action:{type:"string",required:!0,description:"\u64CD\u4F5C\u7C7B\u578B: create, read, update, delete, move, list, mkdir"},path:{type:"string",required:!0,description:"\u6587\u4EF6\u6216\u6587\u4EF6\u5939\u8DEF\u5F84\uFF08\u76F8\u5BF9\u4E8Evault\u6839\u76EE\u5F55\uFF09"},content:{type:"string",required:!1,description:"\u6587\u4EF6\u5185\u5BB9\uFF08create\u548Cupdate\u64CD\u4F5C\u65F6\u9700\u8981\uFF09"},newPath:{type:"string",required:!1,description:"\u65B0\u8DEF\u5F84\uFF08move\u64CD\u4F5C\u65F6\u9700\u8981\uFF09"},recursive:{type:"boolean",required:!1,description:"\u662F\u5426\u9012\u5F52\u64CD\u4F5C\uFF08list\u548Cdelete\u64CD\u4F5C\u65F6\u53EF\u9009\uFF09"}});this.app=e}async execute(e){let{action:t,path:r,content:o,newPath:n,recursive:i=!1}=e;try{switch(console.log(`\u{1F5C2}\uFE0F FileOperationTool: \u6267\u884C${t}\u64CD\u4F5C\uFF0C\u8DEF\u5F84: ${r}`),t.toLowerCase()){case"create":return await this.createFile(r,o||"");case"read":return await this.readFile(r);case"update":return await this.updateFile(r,o||"");case"delete":return await this.deleteFile(r,i);case"move":return await this.moveFile(r,n);case"list":return await this.listFiles(r,i);case"mkdir":return await this.createFolder(r);default:return{success:!1,error:`\u4E0D\u652F\u6301\u7684\u64CD\u4F5C\u7C7B\u578B: ${t}\u3002\u652F\u6301\u7684\u64CD\u4F5C: create, read, update, delete, move, list, mkdir`}}}catch(a){return console.error("FileOperationTool\u6267\u884C\u5931\u8D25:",a),{success:!1,error:`\u6587\u4EF6\u64CD\u4F5C\u5931\u8D25: ${a.message}`}}}async createFile(e,t){try{if(e.includes(".")||(e+=".md"),this.app.vault.getAbstractFileByPath(e))return{success:!1,error:`\u6587\u4EF6\u5DF2\u5B58\u5728: ${e}`};let o=await this.app.vault.create(e,t);return console.log(`\u2705 \u6587\u4EF6\u521B\u5EFA\u6210\u529F: ${e}`),new T.Notice(`\u6587\u4EF6\u521B\u5EFA\u6210\u529F: ${o.name}`),{success:!0,data:{action:"create",path:e,name:o.name,size:o.stat.size,created:new Date(o.stat.ctime).toISOString()}}}catch(r){return{success:!1,error:`\u521B\u5EFA\u6587\u4EF6\u5931\u8D25: ${r.message}`}}}async readFile(e){try{let t=this.app.vault.getAbstractFileByPath(e);if(!t||!(t instanceof T.TFile))return{success:!1,error:`\u6587\u4EF6\u4E0D\u5B58\u5728: ${e}`};let r=await this.app.vault.read(t);return{success:!0,data:{action:"read",path:e,name:t.name,content:r,size:t.stat.size,modified:new Date(t.stat.mtime).toISOString()}}}catch(t){return{success:!1,error:`\u8BFB\u53D6\u6587\u4EF6\u5931\u8D25: ${t.message}`}}}async updateFile(e,t){try{let r=this.app.vault.getAbstractFileByPath(e);return!r||!(r instanceof T.TFile)?{success:!1,error:`\u6587\u4EF6\u4E0D\u5B58\u5728: ${e}`}:(await this.app.vault.modify(r,t),console.log(`\u2705 \u6587\u4EF6\u66F4\u65B0\u6210\u529F: ${e}`),new T.Notice(`\u6587\u4EF6\u66F4\u65B0\u6210\u529F: ${r.name}`),{success:!0,data:{action:"update",path:e,name:r.name,size:r.stat.size,modified:new Date().toISOString()}})}catch(r){return{success:!1,error:`\u66F4\u65B0\u6587\u4EF6\u5931\u8D25: ${r.message}`}}}async deleteFile(e,t=!1){try{let r=this.app.vault.getAbstractFileByPath(e);return r?r instanceof T.TFolder&&!t&&r.children.length>0?{success:!1,error:`\u6587\u4EF6\u5939\u4E0D\u4E3A\u7A7A\uFF0C\u8BF7\u4F7F\u7528recursive=true\u8FDB\u884C\u9012\u5F52\u5220\u9664: ${e}`}:(await this.app.vault.delete(r,t),console.log(`\u2705 \u5220\u9664\u6210\u529F: ${e}`),new T.Notice(`\u5220\u9664\u6210\u529F: ${r.name}`),{success:!0,data:{action:"delete",path:e,name:r.name,type:r instanceof T.TFile?"file":"folder",recursive:t}}):{success:!1,error:`\u6587\u4EF6\u6216\u6587\u4EF6\u5939\u4E0D\u5B58\u5728: ${e}`}}catch(r){return{success:!1,error:`\u5220\u9664\u5931\u8D25: ${r.message}`}}}async moveFile(e,t){try{if(!t)return{success:!1,error:"\u79FB\u52A8\u64CD\u4F5C\u9700\u8981\u63D0\u4F9BnewPath\u53C2\u6570"};let r=this.app.vault.getAbstractFileByPath(e);return r?(await this.app.vault.rename(r,t),console.log(`\u2705 \u79FB\u52A8\u6210\u529F: ${e} -> ${t}`),new T.Notice(`\u79FB\u52A8\u6210\u529F: ${r.name}`),{success:!0,data:{action:"move",oldPath:e,newPath:t,name:r.name,type:r instanceof T.TFile?"file":"folder"}}):{success:!1,error:`\u6587\u4EF6\u4E0D\u5B58\u5728: ${e}`}}catch(r){return{success:!1,error:`\u79FB\u52A8\u5931\u8D25: ${r.message}`}}}async listFiles(e="",t=!1){try{let r;if(!e||e==="/"||e===".")r=this.app.vault.getRoot();else{let n=this.app.vault.getAbstractFileByPath(e);if(!n||!(n instanceof T.TFolder))return{success:!1,error:`\u6587\u4EF6\u5939\u4E0D\u5B58\u5728: ${e}`};r=n}let o=this.collectFiles(r,t);return{success:!0,data:{action:"list",path:e||"/",recursive:t,items:o,count:o.length}}}catch(r){return{success:!1,error:`\u5217\u51FA\u6587\u4EF6\u5931\u8D25: ${r.message}`}}}async createFolder(e){try{return this.app.vault.getAbstractFileByPath(e)?{success:!1,error:`\u6587\u4EF6\u5939\u5DF2\u5B58\u5728: ${e}`}:(await this.app.vault.createFolder(e),console.log(`\u2705 \u6587\u4EF6\u5939\u521B\u5EFA\u6210\u529F: ${e}`),new T.Notice(`\u6587\u4EF6\u5939\u521B\u5EFA\u6210\u529F: ${e}`),{success:!0,data:{action:"mkdir",path:e,created:new Date().toISOString()}})}catch(t){return{success:!1,error:`\u521B\u5EFA\u6587\u4EF6\u5939\u5931\u8D25: ${t.message}`}}}collectFiles(e,t){let r=[];for(let o of e.children)if(o instanceof T.TFile)r.push({type:"file",name:o.name,path:o.path,extension:o.extension,size:o.stat.size,created:new Date(o.stat.ctime).toISOString(),modified:new Date(o.stat.mtime).toISOString()});else if(o instanceof T.TFolder){let n={type:"folder",name:o.name,path:o.path,children:t?this.collectFiles(o,!0):o.children.length};r.push(n)}return r}};var E=require("obsidian");var ge=class extends x{constructor(e){super("note_template","\u7B14\u8BB0\u6A21\u677F\u5DE5\u5177","\u4F7F\u7528\u9884\u5B9A\u4E49\u6A21\u677F\u5FEB\u901F\u521B\u5EFA\u5404\u79CD\u7C7B\u578B\u7684\u7B14\u8BB0","obsidian",{templateType:{type:"string",required:!0,description:"\u6A21\u677F\u7C7B\u578B: daily, meeting, project, book, idea, todo, journal, research"},title:{type:"string",required:!0,description:"\u7B14\u8BB0\u6807\u9898"},folder:{type:"string",required:!1,description:"\u76EE\u6807\u6587\u4EF6\u5939\u8DEF\u5F84\uFF08\u53EF\u9009\uFF09"},tags:{type:"array",required:!1,description:"\u6807\u7B7E\u5217\u8868\uFF08\u53EF\u9009\uFF09"},customFields:{type:"object",required:!1,description:"\u81EA\u5B9A\u4E49\u5B57\u6BB5\uFF08\u53EF\u9009\uFF09"}});this.app=e}async execute(e){let{templateType:t,title:r,folder:o="",tags:n=[],customFields:i={}}=e;try{console.log(`\u{1F4DD} NoteTemplateTool: \u521B\u5EFA${t}\u6A21\u677F\u7B14\u8BB0: ${r}`);let a=this.getTemplate(t,r,n,i);if(!a)return{success:!1,error:`\u4E0D\u652F\u6301\u7684\u6A21\u677F\u7C7B\u578B: ${t}\u3002\u652F\u6301\u7684\u7C7B\u578B: daily, meeting, project, book, idea, todo, journal, research`};let c=this.generateFileName(t,r),l=o?`${o}/${c}`:c;if(this.app.vault.getAbstractFileByPath(l))return{success:!1,error:`\u6587\u4EF6\u5DF2\u5B58\u5728: ${l}`};let p=await this.app.vault.create(l,a.content);return console.log(`\u2705 \u6A21\u677F\u7B14\u8BB0\u521B\u5EFA\u6210\u529F: ${l}`),{success:!0,data:{templateType:t,title:r,path:l,name:p.name,size:p.stat.size,created:new Date(p.stat.ctime).toISOString(),template:a.description}}}catch(a){return console.error("NoteTemplateTool\u6267\u884C\u5931\u8D25:",a),{success:!1,error:`\u521B\u5EFA\u6A21\u677F\u7B14\u8BB0\u5931\u8D25: ${a.message}`}}}getTemplate(e,t,r,o){let n=(0,E.moment)(),i=n.format("YYYY-MM-DD"),a=n.format("HH:mm"),c=r.length>0?r.map(l=>`#${l}`).join(" "):"";switch(e.toLowerCase()){case"daily":return{content:this.getDailyTemplate(t,i,c,o),description:"\u65E5\u8BB0\u6A21\u677F"};case"meeting":return{content:this.getMeetingTemplate(t,i,a,c,o),description:"\u4F1A\u8BAE\u8BB0\u5F55\u6A21\u677F"};case"project":return{content:this.getProjectTemplate(t,i,c,o),description:"\u9879\u76EE\u7BA1\u7406\u6A21\u677F"};case"book":return{content:this.getBookTemplate(t,i,c,o),description:"\u8BFB\u4E66\u7B14\u8BB0\u6A21\u677F"};case"idea":return{content:this.getIdeaTemplate(t,i,a,c,o),description:"\u60F3\u6CD5\u8BB0\u5F55\u6A21\u677F"};case"todo":return{content:this.getTodoTemplate(t,i,c,o),description:"\u4EFB\u52A1\u6E05\u5355\u6A21\u677F"};case"journal":return{content:this.getJournalTemplate(t,i,a,c,o),description:"\u65E5\u5FD7\u6A21\u677F"};case"research":return{content:this.getResearchTemplate(t,i,c,o),description:"\u7814\u7A76\u7B14\u8BB0\u6A21\u677F"};default:return null}}generateFileName(e,t){let o=(0,E.moment)().format("YYYY-MM-DD"),n=t.replace(/[<>:"/\\|?*]/g,"-");switch(e.toLowerCase()){case"daily":return`${o}-${n}.md`;case"meeting":return`\u4F1A\u8BAE-${o}-${n}.md`;case"project":return`\u9879\u76EE-${n}.md`;default:return`${n}.md`}}getDailyTemplate(e,t,r,o){return`# ${e}

**\u65E5\u671F**: ${t}
**\u5929\u6C14**: ${o.weather||""}
**\u5FC3\u60C5**: ${o.mood||""}

${r}

## \u4ECA\u65E5\u8BA1\u5212
- [ ] 
- [ ] 
- [ ] 

## \u91CD\u8981\u4E8B\u4EF6
- 

## \u5B66\u4E60\u6536\u83B7
- 

## \u53CD\u601D\u603B\u7ED3
- 

## \u660E\u65E5\u8BA1\u5212
- [ ] 
- [ ] 

---
*\u521B\u5EFA\u65F6\u95F4: ${(0,E.moment)().format("YYYY-MM-DD HH:mm:ss")}*`}getMeetingTemplate(e,t,r,o,n){return`# ${e}

**\u65E5\u671F**: ${t}
**\u65F6\u95F4**: ${r}
**\u5730\u70B9**: ${n.location||""}
**\u4E3B\u6301\u4EBA**: ${n.host||""}
**\u53C2\u4E0E\u8005**: ${n.participants||""}

${o}

## \u4F1A\u8BAE\u8BAE\u7A0B
1. 
2. 
3. 

## \u8BA8\u8BBA\u8981\u70B9
### \u8BAE\u9898\u4E00
- 

### \u8BAE\u9898\u4E8C
- 

## \u51B3\u7B56\u4E8B\u9879
- [ ] 
- [ ] 

## \u884C\u52A8\u8BA1\u5212
| \u4EFB\u52A1 | \u8D1F\u8D23\u4EBA | \u622A\u6B62\u65E5\u671F | \u72B6\u6001 |
|------|--------|----------|------|
|      |        |          |      |

## \u540E\u7EED\u8DDF\u8FDB
- 

---
*\u4F1A\u8BAE\u8BB0\u5F55: ${(0,E.moment)().format("YYYY-MM-DD HH:mm:ss")}*`}getProjectTemplate(e,t,r,o){return`# ${e}

**\u521B\u5EFA\u65E5\u671F**: ${t}
**\u9879\u76EE\u72B6\u6001**: ${o.status||"\u8BA1\u5212\u4E2D"}
**\u4F18\u5148\u7EA7**: ${o.priority||"\u4E2D"}
**\u8D1F\u8D23\u4EBA**: ${o.owner||""}
**\u622A\u6B62\u65E5\u671F**: ${o.deadline||""}

${r}

## \u9879\u76EE\u6982\u8FF0
${o.description||""}

## \u9879\u76EE\u76EE\u6807
- 
- 
- 

## \u5173\u952E\u91CC\u7A0B\u7891
- [ ] 
- [ ] 
- [ ] 

## \u4EFB\u52A1\u5206\u89E3
### \u9636\u6BB5\u4E00
- [ ] 
- [ ] 

### \u9636\u6BB5\u4E8C
- [ ] 
- [ ] 

## \u8D44\u6E90\u9700\u6C42
- **\u4EBA\u529B**: 
- **\u6280\u672F**: 
- **\u9884\u7B97**: 

## \u98CE\u9669\u8BC4\u4F30
| \u98CE\u9669 | \u5F71\u54CD\u7A0B\u5EA6 | \u5E94\u5BF9\u63AA\u65BD |
|------|----------|----------|
|      |          |          |

## \u9879\u76EE\u65E5\u5FD7
### ${t}
- \u9879\u76EE\u542F\u52A8

---
*\u9879\u76EE\u521B\u5EFA: ${(0,E.moment)().format("YYYY-MM-DD HH:mm:ss")}*`}getBookTemplate(e,t,r,o){return`# ${e}

**\u4F5C\u8005**: ${o.author||""}
**\u51FA\u7248\u793E**: ${o.publisher||""}
**ISBN**: ${o.isbn||""}
**\u9605\u8BFB\u65E5\u671F**: ${t}
**\u8BC4\u5206**: ${o.rating||""}/5

${r}

## \u57FA\u672C\u4FE1\u606F
- **\u9875\u6570**: ${o.pages||""}
- **\u5206\u7C7B**: ${o.category||""}
- **\u63A8\u8350\u4EBA**: ${o.recommender||""}

## \u5185\u5BB9\u6982\u8981
${o.summary||""}

## \u7AE0\u8282\u7B14\u8BB0
### \u7B2C\u4E00\u7AE0
- 

### \u7B2C\u4E8C\u7AE0
- 

## \u91CD\u8981\u89C2\u70B9
1. 
2. 
3. 

## \u91D1\u53E5\u6458\u5F55
> 

## \u4E2A\u4EBA\u601D\u8003
- 

## \u884C\u52A8\u8BA1\u5212
- [ ] 
- [ ] 

## \u76F8\u5173\u4E66\u7C4D
- 

---
*\u8BFB\u4E66\u7B14\u8BB0: ${(0,E.moment)().format("YYYY-MM-DD HH:mm:ss")}*`}getIdeaTemplate(e,t,r,o,n){return`# ${e}

**\u8BB0\u5F55\u65F6\u95F4**: ${t} ${r}
**\u7075\u611F\u6765\u6E90**: ${n.source||""}
**\u76F8\u5173\u9886\u57DF**: ${n.domain||""}

${o}

## \u6838\u5FC3\u60F3\u6CD5
${n.core||""}

## \u8BE6\u7EC6\u63CF\u8FF0
- 

## \u53EF\u884C\u6027\u5206\u6790
### \u4F18\u52BF
- 

### \u6311\u6218
- 

### \u673A\u4F1A
- 

## \u5B9E\u65BD\u6B65\u9AA4
1. [ ] 
2. [ ] 
3. [ ] 

## \u76F8\u5173\u8D44\u6E90
- 

## \u540E\u7EED\u601D\u8003
- 

---
*\u60F3\u6CD5\u8BB0\u5F55: ${(0,E.moment)().format("YYYY-MM-DD HH:mm:ss")}*`}getTodoTemplate(e,t,r,o){return`# ${e}

**\u521B\u5EFA\u65E5\u671F**: ${t}
**\u4F18\u5148\u7EA7**: ${o.priority||"\u4E2D"}
**\u5206\u7C7B**: ${o.category||""}

${r}

## \u4ECA\u65E5\u4EFB\u52A1
- [ ] 
- [ ] 
- [ ] 

## \u672C\u5468\u4EFB\u52A1
- [ ] 
- [ ] 
- [ ] 

## \u957F\u671F\u76EE\u6807
- [ ] 
- [ ] 

## \u5DF2\u5B8C\u6210
- [x] 

## \u5907\u6CE8
- 

---
*\u4EFB\u52A1\u6E05\u5355: ${(0,E.moment)().format("YYYY-MM-DD HH:mm:ss")}*`}getJournalTemplate(e,t,r,o,n){return`# ${e}

**\u65E5\u671F**: ${t} ${r}
**\u5730\u70B9**: ${n.location||""}
**\u5929\u6C14**: ${n.weather||""}

${o}

## \u4ECA\u65E5\u611F\u609F
${n.reflection||""}

## \u91CD\u8981\u4E8B\u4EF6
- 

## \u60C5\u7EEA\u8BB0\u5F55
**\u5FC3\u60C5**: ${n.mood||""}
**\u80FD\u91CF\u6C34\u5E73**: ${n.energy||""}/10

## \u5B66\u4E60\u4E0E\u6210\u957F
- 

## \u611F\u6069\u8BB0\u5F55
1. 
2. 
3. 

## \u660E\u65E5\u671F\u5F85
- 

---
*\u65E5\u5FD7\u8BB0\u5F55: ${(0,E.moment)().format("YYYY-MM-DD HH:mm:ss")}*`}getResearchTemplate(e,t,r,o){return`# ${e}

**\u7814\u7A76\u65E5\u671F**: ${t}
**\u7814\u7A76\u9886\u57DF**: ${o.field||""}
**\u7814\u7A76\u65B9\u6CD5**: ${o.method||""}
**\u6570\u636E\u6765\u6E90**: ${o.source||""}

${r}

## \u7814\u7A76\u95EE\u9898
${o.question||""}

## \u7814\u7A76\u5047\u8BBE
- 

## \u6587\u732E\u7EFC\u8FF0
### \u76F8\u5173\u7814\u7A76
- 

### \u7406\u8BBA\u57FA\u7840
- 

## \u7814\u7A76\u65B9\u6CD5
### \u6570\u636E\u6536\u96C6
- 

### \u5206\u6790\u65B9\u6CD5
- 

## \u7814\u7A76\u53D1\u73B0
### \u4E3B\u8981\u7ED3\u679C
- 

### \u6570\u636E\u5206\u6790
- 

## \u7ED3\u8BBA\u4E0E\u8BA8\u8BBA
### \u4E3B\u8981\u7ED3\u8BBA
- 

### \u5C40\u9650\u6027
- 

### \u672A\u6765\u7814\u7A76\u65B9\u5411
- 

## \u53C2\u8003\u6587\u732E
1. 

---
*\u7814\u7A76\u7B14\u8BB0: ${(0,E.moment)().format("YYYY-MM-DD HH:mm:ss")}*`}};var he=class extends w.Plugin{constructor(){super(...arguments);this.config=I;this.statusBarManager=null;this.initialized=!1}async onload(){var e,t;console.log("\u{1F680} === AI Coach Advanced Plugin \u5F00\u59CB\u52A0\u8F7D ==="),console.log("\u{1F4C5} \u52A0\u8F7D\u65F6\u95F4:",new Date().toISOString()),console.log("\u{1F527} Obsidian\u7248\u672C:",((t=(e=this.app.vault)==null?void 0:e.adapter)==null?void 0:t.version)||"unknown");try{console.log("\u2699\uFE0F \u5F00\u59CB\u521D\u59CB\u5316\u6838\u5FC3\u7EC4\u4EF6..."),await this.initializeCore(),console.log("\u2705 \u6838\u5FC3\u7EC4\u4EF6\u521D\u59CB\u5316\u5B8C\u6210"),console.log("\u{1F3A8} \u6CE8\u518C\u89C6\u56FE..."),this.registerView(N,r=>new oe(r,this.orchestrationEngine)),console.log("\u2705 \u89C6\u56FE\u6CE8\u518C\u5B8C\u6210"),console.log("\u{1F4CB} \u6CE8\u518C\u547D\u4EE4..."),this.registerCommands(),console.log("\u2705 \u547D\u4EE4\u6CE8\u518C\u5B8C\u6210"),console.log("\u2699\uFE0F \u6DFB\u52A0\u8BBE\u7F6E\u9875\u9762..."),this.addSettingTab(new ie(this.app,this)),console.log("\u2705 \u8BBE\u7F6E\u9875\u9762\u6DFB\u52A0\u5B8C\u6210"),console.log("\u{1F4CA} \u521D\u59CB\u5316\u72B6\u6001\u680F..."),this.statusBarManager=new ae(this.addStatusBarItem(),this.orchestrationEngine),this.statusBarManager.initialize(),console.log("\u2705 \u72B6\u6001\u680F\u521D\u59CB\u5316\u5B8C\u6210"),console.log("\u{1F442} \u6CE8\u518C\u6587\u4EF6\u53D8\u5316\u76D1\u542C\u5668..."),this.registerVaultEvents(),console.log("\u2705 \u6587\u4EF6\u53D8\u5316\u76D1\u542C\u5668\u6CE8\u518C\u5B8C\u6210"),console.log("\u{1F389} === AI Coach Advanced Plugin \u52A0\u8F7D\u6210\u529F ==="),new w.Notice("AI Coach Advanced \u63D2\u4EF6\u5DF2\u52A0\u8F7D")}catch(r){console.error("\u274C === AI Coach Advanced Plugin \u52A0\u8F7D\u5931\u8D25 ==="),console.error("\u274C \u9519\u8BEF\u8BE6\u60C5:",r),console.error("\u274C \u9519\u8BEF\u5806\u6808:",r.stack),new w.Notice("AI Coach Advanced \u63D2\u4EF6\u52A0\u8F7D\u5931\u8D25: "+r.message)}}async onunload(){var e;console.log("Unloading AI Coach Advanced Plugin...");try{this.orchestrationEngine&&await this.orchestrationEngine.cleanup(),this.statusBarManager&&this.statusBarManager.cleanup(),await((e=this.configManager)==null?void 0:e.saveConfig()),this.initialized=!1,console.log("AI Coach Advanced Plugin unloaded successfully")}catch(t){console.error("Error during plugin unload:",t)}}async initializeCore(){console.log("\u{1F527} initializeCore: \u5F00\u59CB\u521D\u59CB\u5316\u6838\u5FC3\u7EC4\u4EF6"),console.log("\u{1F4CB} \u521B\u5EFA\u914D\u7F6E\u7BA1\u7406\u5668..."),this.configManager=new G(this),console.log("\u2705 \u914D\u7F6E\u7BA1\u7406\u5668\u521B\u5EFA\u5B8C\u6210"),console.log("\u{1F4CB} \u52A0\u8F7D\u914D\u7F6E..."),this.config=await this.configManager.loadConfig(),console.log("\u2705 \u914D\u7F6E\u52A0\u8F7D\u5B8C\u6210:",{provider:this.config.llm.provider,model:this.config.llm.model,hasApiKey:!!this.config.llm.apiKey}),console.log("\u{1F3AF} \u521B\u5EFA\u7F16\u6392\u5F15\u64CE..."),this.orchestrationEngine=new re(this.app,this.configManager),console.log("\u2705 \u7F16\u6392\u5F15\u64CE\u521B\u5EFA\u5B8C\u6210"),console.log("\u{1F527} \u6CE8\u518C\u6240\u6709\u5DE5\u5177..."),await this.registerTools(),console.log("\u2705 \u5DE5\u5177\u6CE8\u518C\u5B8C\u6210"),console.log("\u{1F3AF} \u521D\u59CB\u5316\u7F16\u6392\u5F15\u64CE..."),await this.orchestrationEngine.initialize(),console.log("\u2705 \u7F16\u6392\u5F15\u64CE\u521D\u59CB\u5316\u5B8C\u6210"),this.initialized=!0,console.log("\u{1F389} \u6838\u5FC3\u7EC4\u4EF6\u521D\u59CB\u5316\u5168\u90E8\u5B8C\u6210")}async registerTools(){console.log("\u{1F527} registerTools: \u5F00\u59CB\u6CE8\u518C\u5DE5\u5177");try{console.log("\u{1F4DA} \u6CE8\u518CVault\u67E5\u8BE2\u5DE5\u5177...");let e=new le(this.app);await this.orchestrationEngine.registerTool(e),console.log("\u2705 Vault\u67E5\u8BE2\u5DE5\u5177\u6CE8\u518C\u5B8C\u6210"),console.log("\u{1F310} \u6CE8\u518C\u7F51\u7EDC\u641C\u7D22\u5DE5\u5177...");let t=new ue;await this.orchestrationEngine.registerTool(t),console.log("\u2705 \u7F51\u7EDC\u641C\u7D22\u5DE5\u5177\u6CE8\u518C\u5B8C\u6210"),console.log("\u26A1 \u6CE8\u518CJavaScript\u6267\u884C\u5DE5\u5177...");let r=new pe;await this.orchestrationEngine.registerTool(r),console.log("\u2705 JavaScript\u6267\u884C\u5DE5\u5177\u6CE8\u518C\u5B8C\u6210"),console.log("\u{1F9E0} \u6CE8\u518C\u8BB0\u5FC6\u7BA1\u7406\u5DE5\u5177...");let o=new O(this.app),n=new q(this.app);await this.orchestrationEngine.registerTool(o),await this.orchestrationEngine.registerTool(n),console.log("\u2705 \u8BB0\u5FC6\u7BA1\u7406\u5DE5\u5177\u6CE8\u518C\u5B8C\u6210"),console.log("\u{1F5C2}\uFE0F \u6CE8\u518C\u6587\u4EF6\u64CD\u4F5C\u5DE5\u5177...");let i=new de(this.app);await this.orchestrationEngine.registerTool(i),console.log("\u2705 \u6587\u4EF6\u64CD\u4F5C\u5DE5\u5177\u6CE8\u518C\u5B8C\u6210"),console.log("\u{1F4DD} \u6CE8\u518C\u7B14\u8BB0\u6A21\u677F\u5DE5\u5177...");let a=new ge(this.app);await this.orchestrationEngine.registerTool(a),console.log("\u2705 \u7B14\u8BB0\u6A21\u677F\u5DE5\u5177\u6CE8\u518C\u5B8C\u6210"),console.log("\u{1F389} \u6240\u6709\u5DE5\u5177\u6CE8\u518C\u6210\u529F");let c=this.orchestrationEngine.getAvailableTools();console.log("\u{1F4CB} \u5DF2\u6CE8\u518C\u5DE5\u5177\u5217\u8868:",c.map(l=>l.name))}catch(e){throw console.error("\u274C \u5DE5\u5177\u6CE8\u518C\u5931\u8D25:",e),console.error("\u274C \u9519\u8BEF\u5806\u6808:",e.stack),e}}registerCommands(){this.addCommand({id:"open-ai-chat",name:"\u6253\u5F00AI\u52A9\u624B\u4FA7\u8FB9\u680F",callback:()=>this.openChatSidebar()}),this.addCommand({id:"quick-query",name:"\u5FEB\u901F\u67E5\u8BE2",callback:()=>this.quickQuery()}),this.addCommand({id:"rebuild-vault-index",name:"\u91CD\u5EFAVault\u7D22\u5F15",callback:()=>this.rebuildVaultIndex()}),this.addCommand({id:"clear-conversation-history",name:"\u6E05\u7406\u5BF9\u8BDD\u5386\u53F2",callback:()=>this.clearConversationHistory()}),this.addCommand({id:"manage-memories",name:"\u7BA1\u7406\u957F\u671F\u8BB0\u5FC6",callback:()=>this.openMemoryManager()})}registerVaultEvents(){this.registerEvent(this.app.vault.on("create",e=>{e instanceof w.TFile&&e.extension==="md"&&this.onFileChanged(e)})),this.registerEvent(this.app.vault.on("modify",e=>{e instanceof w.TFile&&e.extension==="md"&&this.onFileChanged(e)})),this.registerEvent(this.app.vault.on("delete",e=>{e instanceof w.TFile&&e.extension==="md"&&this.onFileDeleted(e)}))}async onFileChanged(e){var t,r;if((r=(t=this.config.tools)==null?void 0:t.vault)!=null&&r.indexingEnabled&&this.initialized)try{let o=this.getVaultQueryTool();o&&(await o.updateFileIndex(e.path),console.log(`Updated index for file: ${e.path}`))}catch(o){console.error(`Failed to update index for ${e.path}:`,o)}}async onFileDeleted(e){var t,r;if((r=(t=this.config.tools)==null?void 0:t.vault)!=null&&r.indexingEnabled&&this.initialized)try{let o=this.getVaultQueryTool();o&&(await o.deleteFileIndex(e.path),console.log(`Deleted index for file: ${e.path}`))}catch(o){console.error(`Failed to delete index for ${e.path}:`,o)}}getVaultQueryTool(){return this.orchestrationEngine.getAvailableTools().find(t=>t.name==="vault_query")}async openChatSidebar(){if(console.log("\u{1F4AC} openChatSidebar: \u5C1D\u8BD5\u6253\u5F00\u804A\u5929\u4FA7\u8FB9\u680F"),!this.initialized){console.log("\u26A0\uFE0F \u63D2\u4EF6\u5C1A\u672A\u521D\u59CB\u5316\u5B8C\u6210"),new w.Notice("AI Coach \u5C1A\u672A\u521D\u59CB\u5316\u5B8C\u6210");return}console.log("\u{1F4AC} \u6FC0\u6D3B\u804A\u5929\u4FA7\u8FB9\u680F..."),await this.activateView(),console.log("\u2705 \u804A\u5929\u4FA7\u8FB9\u680F\u5DF2\u6253\u5F00")}async activateView(){let{workspace:e}=this.app,t=null,r=e.getLeavesOfType(N);r.length>0?t=r[0]:(t=e.getRightLeaf(!1),await(t==null?void 0:t.setViewState({type:N,active:!0}))),t&&e.revealLeaf(t)}async openMemoryManager(){if(console.log("\u{1F9E0} openMemoryManager: \u6253\u5F00\u8BB0\u5FC6\u7BA1\u7406\u5668"),!this.initialized){new w.Notice("AI Coach \u5C1A\u672A\u521D\u59CB\u5316\u5B8C\u6210");return}await this.activateView(),setTimeout(()=>{let e=this.app.workspace.getLeavesOfType(N);if(e.length>0){let t=e[0].view;t&&typeof t.switchTab=="function"&&t.switchTab("memory")}},100)}async quickQuery(){new w.Notice("\u5FEB\u901F\u67E5\u8BE2\u529F\u80FD\u5F00\u53D1\u4E2D...")}async rebuildVaultIndex(){try{new w.Notice("\u5F00\u59CB\u91CD\u5EFAVault\u7D22\u5F15...");let e=this.getVaultQueryTool();if(e){await e.rebuildIndex(),new w.Notice("Vault\u7D22\u5F15\u91CD\u5EFA\u5B8C\u6210");let t=await e.getIndexStats();console.log("Index stats:",t)}else throw new Error("Vault\u67E5\u8BE2\u5DE5\u5177\u672A\u627E\u5230")}catch(e){console.error("Failed to rebuild vault index:",e),new w.Notice("\u7D22\u5F15\u91CD\u5EFA\u5931\u8D25: "+e.message)}}async clearConversationHistory(){try{await this.orchestrationEngine.endCurrentConversation(),new w.Notice("\u5BF9\u8BDD\u5386\u53F2\u5DF2\u6E05\u7406")}catch(e){console.error("Failed to clear conversation history:",e),new w.Notice("\u6E05\u7406\u5BF9\u8BDD\u5386\u53F2\u5931\u8D25: "+e.message)}}getConfig(){return this.config}async updateConfig(e){this.configManager.updateConfig(e),await this.configManager.saveConfig(),this.config=this.configManager.getConfig(),e.llm&&await this.orchestrationEngine.updateConfig(e.llm)}getOrchestrationEngine(){return this.orchestrationEngine}getConfigManager(){return this.configManager}isInitialized(){return this.initialized}};
