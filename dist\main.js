"use strict";var B=Object.defineProperty;var Me=Object.getOwnPropertyDescriptor;var Se=Object.getOwnPropertyNames;var Re=Object.prototype.hasOwnProperty;var Ie=(d,s,e)=>s in d?B(d,s,{enumerable:!0,configurable:!0,writable:!0,value:e}):d[s]=e;var ke=(d,s)=>{for(var e in s)B(d,e,{get:s[e],enumerable:!0})},Le=(d,s,e,t)=>{if(s&&typeof s=="object"||typeof s=="function")for(let r of Se(s))!Re.call(d,r)&&r!==e&&B(d,r,{get:()=>s[r],enumerable:!(t=Me(s,r))||t.enumerable});return d};var Ae=d=>Le(B({},"__esModule",{value:!0}),d);var l=(d,s,e)=>(Ie(d,typeof s!="symbol"?s+"":s,e),e);var De={};ke(De,{default:()=>pe});module.exports=Ae(De);var x=require("obsidian");var M={llm:{provider:"openai",apiKey:"",model:"gpt-3.5-turbo",maxTokens:2e3,temperature:.7,timeout:3e4},tools:{vault:{enabled:!0,indexingEnabled:!0,maxResults:10,chunkSize:1e3},web:{enabled:!0,searchEngine:"duckduckgo",maxResults:5},plugin:{enabled:!1,allowedPlugins:[],restrictedCommands:[]},javascript:{enabled:!1,requireConfirmation:!0,timeout:5e3,memoryLimit:52428800}},ui:{theme:"auto",language:"zh",showProgress:!0,autoSave:!0},security:{encryptApiKeys:!0,logLevel:"info",maxRequestsPerMinute:60,enableAuditLog:!0}};var I=require("obsidian");var D=class{constructor(s,e,t,r=3e4){this.apiKey=s,this.model=e,this.baseUrl=t,this.timeout=r}updateConfig(s){s.apiKey!==void 0&&(this.apiKey=s.apiKey),s.model!==void 0&&(this.model=s.model),s.baseUrl!==void 0&&(this.baseUrl=s.baseUrl),s.timeout!==void 0&&(this.timeout=s.timeout)}getConfig(){return{apiKey:this.apiKey,model:this.model,baseUrl:this.baseUrl,timeout:this.timeout}}},h=class extends Error{constructor(s,e,t,r){super(s),this.name="LLMError",this.code=e,this.statusCode=t,this.details=r}},y={INVALID_API_KEY:"INVALID_API_KEY",RATE_LIMIT_EXCEEDED:"RATE_LIMIT_EXCEEDED",MODEL_NOT_FOUND:"MODEL_NOT_FOUND",INSUFFICIENT_QUOTA:"INSUFFICIENT_QUOTA",NETWORK_ERROR:"NETWORK_ERROR",TIMEOUT:"TIMEOUT",INVALID_REQUEST:"INVALID_REQUEST",SERVER_ERROR:"SERVER_ERROR",UNKNOWN_ERROR:"UNKNOWN_ERROR"};var R=class extends D{constructor(e,t,r,n=3e4){super(e,t,r||"https://api.openai.com/v1",n);this.defaultBaseUrl="https://api.openai.com/v1"}async generateText(e,t){try{let r=this.buildRequestBody(e,t),n=await this.makeRequest("/chat/completions",r);return this.parseResponse(n)}catch(r){throw this.handleError(r)}}async generateWithTools(e,t,r){try{let n=this.buildRequestBody(e,r,t),o=await this.makeRequest("/chat/completions",n);return this.parseResponse(o)}catch(n){throw this.handleError(n)}}async generateTextStream(e,t,r){try{let n=this.buildRequestBody(e,{...r,stream:!0});await this.makeStreamRequest("/chat/completions",n,t)}catch(n){throw this.handleError(n)}}async validateConnection(){try{return await this.makeRequest("/models"),!0}catch(e){return console.error("OpenAI connection validation failed:",e),!1}}async getModelInfo(){let t={"gpt-3.5-turbo":{maxTokens:4096,supportsFunctionCalling:!0,costPer1kTokens:{input:.0015,output:.002}},"gpt-4":{maxTokens:8192,supportsFunctionCalling:!0,costPer1kTokens:{input:.03,output:.06}},"gpt-4-turbo-preview":{maxTokens:128e3,supportsFunctionCalling:!0,costPer1kTokens:{input:.01,output:.03}}}[this.model]||{};return{name:this.model,provider:"OpenAI",maxTokens:t.maxTokens||4096,supportsFunctionCalling:t.supportsFunctionCalling||!1,supportsStreaming:!0,costPer1kTokens:t.costPer1kTokens}}estimateTokens(e){let t=(e.match(/[\u4e00-\u9fff]/g)||[]).length,r=e.length-t;return Math.ceil(t/1.5+r/4)}async getSupportedModels(){try{return(await this.makeRequest("/models")).data.filter(t=>t.id.includes("gpt")).map(t=>t.id).sort()}catch(e){return["gpt-3.5-turbo","gpt-4","gpt-4-turbo-preview"]}}buildRequestBody(e,t,r){var o;let n={model:this.model,messages:this.convertMessages(e),max_tokens:(t==null?void 0:t.maxTokens)||2e3,temperature:(t==null?void 0:t.temperature)||.7,stream:(t==null?void 0:t.stream)||!1};return(o=t==null?void 0:t.stopSequences)!=null&&o.length&&(n.stop=t.stopSequences),r!=null&&r.length&&(n.tools=r,n.tool_choice="auto"),n}convertMessages(e){return e.map(t=>{let r={role:t.role,content:t.content};return t.name&&(r.name=t.name),t.tool_calls&&(r.tool_calls=t.tool_calls),t.tool_call_id&&(r.tool_call_id=t.tool_call_id),r})}async makeRequest(e,t){var a;let r=`${this.baseUrl}${e}`,n={Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json"},o=new AbortController,i=setTimeout(()=>o.abort(),this.timeout);try{let c=await fetch(r,{method:t?"POST":"GET",headers:n,body:t?JSON.stringify(t):void 0,signal:o.signal});if(clearTimeout(i),!c.ok){let u=await c.json().catch(()=>({}));throw new Error(`HTTP ${c.status}: ${((a=u.error)==null?void 0:a.message)||c.statusText}`)}return await c.json()}catch(c){throw clearTimeout(i),c}}async makeStreamRequest(e,t,r){var c,u,m,p,f;let n=`${this.baseUrl}${e}`,o={Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json"},i=new AbortController,a=setTimeout(()=>i.abort(),this.timeout);try{let w=await fetch(n,{method:"POST",headers:o,body:JSON.stringify(t),signal:i.signal});if(clearTimeout(a),!w.ok){let $=await w.json().catch(()=>({}));throw new Error(`HTTP ${w.status}: ${((c=$.error)==null?void 0:c.message)||w.statusText}`)}let S=(u=w.body)==null?void 0:u.getReader();if(!S)throw new Error("Failed to get response reader");let P=new TextDecoder,L="";for(;;){let{done:$,value:q}=await S.read();if($)break;L+=P.decode(q,{stream:!0});let A=L.split(`
`);L=A.pop()||"";for(let H of A)if(H.startsWith("data: ")){let N=H.slice(6);if(N==="[DONE]")return;try{let U=(f=(p=(m=JSON.parse(N).choices)==null?void 0:m[0])==null?void 0:p.delta)==null?void 0:f.content;U&&r(U)}catch(z){}}}}catch(w){throw clearTimeout(a),w}}parseResponse(e){var n,o,i;let t=(n=e.choices)==null?void 0:n[0];if(!t)throw new Error("No choices in response");let r={content:((o=t.message)==null?void 0:o.content)||"",usage:e.usage?{prompt_tokens:e.usage.prompt_tokens,completion_tokens:e.usage.completion_tokens,total_tokens:e.usage.total_tokens}:void 0};return(i=t.message)!=null&&i.tool_calls&&(r.tool_calls=t.message.tool_calls),r}handleError(e){var t,r,n,o,i,a,c,u;return e.name==="AbortError"?new h("Request timeout",y.TIMEOUT):(t=e.message)!=null&&t.includes("401")?new h("Invalid API key",y.INVALID_API_KEY,401):(r=e.message)!=null&&r.includes("429")?new h("Rate limit exceeded",y.RATE_LIMIT_EXCEEDED,429):(n=e.message)!=null&&n.includes("404")?new h("Model not found",y.MODEL_NOT_FOUND,404):(o=e.message)!=null&&o.includes("insufficient_quota")?new h("Insufficient quota",y.INSUFFICIENT_QUOTA,403):(i=e.message)!=null&&i.includes("400")?new h("Invalid request",y.INVALID_REQUEST,400):(a=e.message)!=null&&a.includes("500")||(c=e.message)!=null&&c.includes("502")||(u=e.message)!=null&&u.includes("503")?new h("Server error",y.SERVER_ERROR,500):new h(e.message||"Unknown error",y.UNKNOWN_ERROR,void 0,e)}};var j=class extends D{constructor(e,t,r,n=3e4){super(e,t,r||"https://generativelanguage.googleapis.com/v1beta",n);this.defaultBaseUrl="https://generativelanguage.googleapis.com/v1beta"}async generateText(e,t){try{let r=this.buildRequestBody(e,t),n=await this.makeRequest("/generateContent",r);return this.parseResponse(n)}catch(r){throw this.handleError(r)}}async generateWithTools(e,t,r){try{let n=this.buildRequestBody(e,r,t),o=await this.makeRequest("/generateContent",n);return this.parseResponse(o)}catch(n){throw this.handleError(n)}}async generateTextStream(e,t,r){try{let n=this.buildRequestBody(e,r);await this.makeStreamRequest("/streamGenerateContent",n,t)}catch(n){throw this.handleError(n)}}async validateConnection(){try{return await this.makeRequest("/models"),!0}catch(e){return console.error("Gemini connection validation failed:",e),!1}}async getModelInfo(){let t={"gemini-pro":{maxTokens:32768,supportsFunctionCalling:!0,costPer1kTokens:{input:5e-4,output:.0015}},"gemini-pro-vision":{maxTokens:16384,supportsFunctionCalling:!1,costPer1kTokens:{input:25e-5,output:5e-4}},"gemini-1.5-pro":{maxTokens:1048576,supportsFunctionCalling:!0,costPer1kTokens:{input:.0035,output:.0105}}}[this.model]||{};return{name:this.model,provider:"Google Gemini",maxTokens:t.maxTokens||32768,supportsFunctionCalling:t.supportsFunctionCalling||!1,supportsStreaming:!0,costPer1kTokens:t.costPer1kTokens}}estimateTokens(e){let t=(e.match(/[\u4e00-\u9fff]/g)||[]).length,r=e.length-t;return Math.ceil(t/1.3+r/3.5)}async getSupportedModels(){try{return(await this.makeRequest("/models")).models.filter(t=>t.name.includes("gemini")).map(t=>t.name.split("/").pop()).sort()}catch(e){return["gemini-pro","gemini-pro-vision","gemini-1.5-pro"]}}buildRequestBody(e,t,r){var i;let o={contents:this.convertMessages(e),generationConfig:{maxOutputTokens:(t==null?void 0:t.maxTokens)||2e3,temperature:(t==null?void 0:t.temperature)||.7}};return(i=t==null?void 0:t.stopSequences)!=null&&i.length&&(o.generationConfig.stopSequences=t.stopSequences),r!=null&&r.length&&(o.tools=[{functionDeclarations:r.map(a=>({name:a.function.name,description:a.function.description,parameters:a.function.parameters}))}]),o}convertMessages(e){let t=[],r="",n=[];for(let o of e)if(o.role==="system")r==="user"?n.push({text:o.content}):(n.length>0&&t.push({role:r,parts:n}),r="user",n=[{text:o.content}]);else if(o.role==="user")r==="user"?n.push({text:o.content}):(n.length>0&&t.push({role:r,parts:n}),r="user",n=[{text:o.content}]);else if(o.role==="assistant"){if(r==="model"?n.push({text:o.content}):(n.length>0&&t.push({role:r,parts:n}),r="model",n=[{text:o.content}]),o.tool_calls)for(let i of o.tool_calls)n.push({functionCall:{name:i.function.name,args:JSON.parse(i.function.arguments)}})}else o.role==="tool"&&n.push({functionResponse:{name:o.name,response:{result:o.content}}});return n.length>0&&t.push({role:r,parts:n}),t}async makeRequest(e,t){var a;let r=`${this.baseUrl}/models/${this.model}:${e.replace("/","")}?key=${this.apiKey}`,n={"Content-Type":"application/json"},o=new AbortController,i=setTimeout(()=>o.abort(),this.timeout);try{let c=await fetch(r,{method:t?"POST":"GET",headers:n,body:t?JSON.stringify(t):void 0,signal:o.signal});if(clearTimeout(i),!c.ok){let u=await c.json().catch(()=>({}));throw new Error(`HTTP ${c.status}: ${((a=u.error)==null?void 0:a.message)||c.statusText}`)}return await c.json()}catch(c){throw clearTimeout(i),c}}async makeStreamRequest(e,t,r){var c,u,m,p,f,w,S;let n=`${this.baseUrl}/models/${this.model}:${e.replace("/","")}?key=${this.apiKey}&alt=sse`,o={"Content-Type":"application/json"},i=new AbortController,a=setTimeout(()=>i.abort(),this.timeout);try{let P=await fetch(n,{method:"POST",headers:o,body:JSON.stringify(t),signal:i.signal});if(clearTimeout(a),!P.ok){let A=await P.json().catch(()=>({}));throw new Error(`HTTP ${P.status}: ${((c=A.error)==null?void 0:c.message)||P.statusText}`)}let L=(u=P.body)==null?void 0:u.getReader();if(!L)throw new Error("Failed to get response reader");let $=new TextDecoder,q="";for(;;){let{done:A,value:H}=await L.read();if(A)break;q+=$.decode(H,{stream:!0});let N=q.split(`
`);q=N.pop()||"";for(let z of N)if(z.startsWith("data: ")){let U=z.slice(6);if(U==="[DONE]")return;try{let be=(S=(w=(f=(p=(m=JSON.parse(U).candidates)==null?void 0:m[0])==null?void 0:p.content)==null?void 0:f.parts)==null?void 0:w[0])==null?void 0:S.text;be&&r(be)}catch(Pe){}}}}catch(P){throw clearTimeout(a),P}}parseResponse(e){var i,a,c,u,m,p;let t=(i=e.candidates)==null?void 0:i[0];if(!t)throw new Error("No candidates in response");let n={content:((u=(c=(a=t.content)==null?void 0:a.parts)==null?void 0:c[0])==null?void 0:u.text)||"",usage:e.usageMetadata?{prompt_tokens:e.usageMetadata.promptTokenCount||0,completion_tokens:e.usageMetadata.candidatesTokenCount||0,total_tokens:e.usageMetadata.totalTokenCount||0}:void 0},o=(p=(m=t.content)==null?void 0:m.parts)==null?void 0:p.filter(f=>f.functionCall);return o!=null&&o.length&&(n.tool_calls=o.map((f,w)=>({id:`call_${w}`,type:"function",function:{name:f.functionCall.name,arguments:JSON.stringify(f.functionCall.args)}}))),n}handleError(e){var t,r,n,o,i,a,c,u;return e.name==="AbortError"?new h("Request timeout",y.TIMEOUT):(t=e.message)!=null&&t.includes("401")||(r=e.message)!=null&&r.includes("403")?new h("Invalid API key",y.INVALID_API_KEY,401):(n=e.message)!=null&&n.includes("429")?new h("Rate limit exceeded",y.RATE_LIMIT_EXCEEDED,429):(o=e.message)!=null&&o.includes("404")?new h("Model not found",y.MODEL_NOT_FOUND,404):(i=e.message)!=null&&i.includes("400")?new h("Invalid request",y.INVALID_REQUEST,400):(a=e.message)!=null&&a.includes("500")||(c=e.message)!=null&&c.includes("502")||(u=e.message)!=null&&u.includes("503")?new h("Server error",y.SERVER_ERROR,500):new h(e.message||"Unknown error",y.UNKNOWN_ERROR,void 0,e)}};var K=class extends R{constructor(s,e,t,r=3e4){super(s,e,t||"https://api.deepseek.com/v1",r)}async getModelInfo(){let e={"deepseek-chat":{maxTokens:32768,supportsFunctionCalling:!0,costPer1kTokens:{input:.0014,output:.0028}},"deepseek-coder":{maxTokens:16384,supportsFunctionCalling:!0,costPer1kTokens:{input:.0014,output:.0028}},"deepseek-math":{maxTokens:4096,supportsFunctionCalling:!1,costPer1kTokens:{input:.0014,output:.0028}}}[this.model]||{};return{name:this.model,provider:"DeepSeek",maxTokens:e.maxTokens||32768,supportsFunctionCalling:e.supportsFunctionCalling||!1,supportsStreaming:!0,costPer1kTokens:e.costPer1kTokens}}async getSupportedModels(){try{return(await this.makeRequest("/models")).data.filter(e=>e.id.includes("deepseek")).map(e=>e.id).sort()}catch(s){return["deepseek-chat","deepseek-coder","deepseek-math"]}}estimateTokens(s){let e=(s.match(/[\u4e00-\u9fff]/g)||[]).length,t=s.length-e;return Math.ceil(e/1.5+t/4)}async makeRequest(s,e){return super.makeRequest(s,e)}};var E=class{static createLLM(s){let e=this.generateKey(s);if(this.instances.has(e)){let r=this.instances.get(e);return r.updateConfig({apiKey:s.apiKey,model:s.model,baseUrl:s.baseUrl,timeout:s.timeout}),r}let t;switch(s.provider){case"openai":t=new R(s.apiKey,s.model,s.baseUrl,s.timeout);break;case"gemini":t=new j(s.apiKey,s.model,s.baseUrl,s.timeout);break;case"deepseek":t=new K(s.apiKey,s.model,s.baseUrl||"https://api.deepseek.com",s.timeout);break;case"custom":if(!s.baseUrl)throw new h("Custom provider requires baseUrl",y.INVALID_REQUEST);t=new R(s.apiKey,s.model,s.baseUrl,s.timeout);break;default:throw new h(`Unsupported LLM provider: ${s.provider}`,y.INVALID_REQUEST)}return this.instances.set(e,t),t}static getLLM(s){let e=this.generateKey(s);return this.instances.get(e)}static removeLLM(s){let e=this.generateKey(s);this.instances.delete(e)}static clearAll(){this.instances.clear()}static getSupportedProviders(){return[{id:"openai",name:"OpenAI",description:"OpenAI GPT\u7CFB\u5217\u6A21\u578B",requiresApiKey:!0,requiresBaseUrl:!1,defaultModels:["gpt-3.5-turbo","gpt-4","gpt-4-turbo-preview"]},{id:"gemini",name:"Google Gemini",description:"Google Gemini\u7CFB\u5217\u6A21\u578B",requiresApiKey:!0,requiresBaseUrl:!1,defaultModels:["gemini-pro","gemini-pro-vision"]},{id:"deepseek",name:"DeepSeek",description:"DeepSeek\u7CFB\u5217\u6A21\u578B",requiresApiKey:!0,requiresBaseUrl:!1,defaultModels:["deepseek-chat","deepseek-coder"]},{id:"custom",name:"\u81EA\u5B9A\u4E49",description:"OpenAI\u517C\u5BB9\u7684\u81EA\u5B9A\u4E49API",requiresApiKey:!0,requiresBaseUrl:!0,defaultModels:[]}]}static validateConfig(s){var t,r,n;let e=[];return(t=s.apiKey)!=null&&t.trim()||e.push("API\u5BC6\u94A5\u4E0D\u80FD\u4E3A\u7A7A"),(r=s.model)!=null&&r.trim()||e.push("\u6A21\u578B\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A"),s.provider==="custom"&&!((n=s.baseUrl)!=null&&n.trim())&&e.push("\u81EA\u5B9A\u4E49\u63D0\u4F9B\u5546\u9700\u8981\u6307\u5B9AbaseUrl"),s.timeout&&s.timeout<1e3&&e.push("\u8D85\u65F6\u65F6\u95F4\u4E0D\u80FD\u5C11\u4E8E1\u79D2"),s.maxTokens&&s.maxTokens<1&&e.push("\u6700\u5927token\u6570\u5FC5\u987B\u5927\u4E8E0"),s.temperature&&(s.temperature<0||s.temperature>2)&&e.push("\u6E29\u5EA6\u503C\u5FC5\u987B\u57280-2\u4E4B\u95F4"),{valid:e.length===0,errors:e}}static generateKey(s){return`${s.provider}:${s.model}:${s.baseUrl||"default"}`}};l(E,"instances",new Map);var Q=class{constructor(s){l(this,"plugin");l(this,"config");l(this,"configVersion","1.0.0");l(this,"encryptionKey",null);this.plugin=s,this.config=this.deepClone(M)}async loadConfig(){try{let s=await this.plugin.loadData();s&&(s.version&&s.version!==this.configVersion?await this.migrateConfig(s):this.config=this.mergeConfig(M,s),this.config.security.encryptApiKeys&&await this.decryptSensitiveData(),this.validateConfig())}catch(s){console.error("Failed to load config:",s),new I.Notice("\u914D\u7F6E\u52A0\u8F7D\u5931\u8D25\uFF0C\u4F7F\u7528\u9ED8\u8BA4\u914D\u7F6E"),this.config=this.deepClone(M)}return this.config}async saveConfig(){try{this.validateConfig();let s=this.deepClone(this.config);s.version=this.configVersion,s.lastUpdated=new Date().toISOString(),this.config.security.encryptApiKeys&&await this.encryptSensitiveData(s),await this.plugin.saveData(s)}catch(s){throw console.error("Failed to save config:",s),new I.Notice("\u914D\u7F6E\u4FDD\u5B58\u5931\u8D25: "+s.message),s}}getConfig(){return this.deepClone(this.config)}updateConfig(s){this.config=this.mergeConfig(this.config,s)}updateLLMConfig(s){this.config.llm={...this.config.llm,...s}}getLLMConfig(){return this.deepClone(this.config.llm)}async validateLLMConfig(){return E.validateConfig(this.config.llm)}updateToolsConfig(s){this.config.tools=this.mergeConfig(this.config.tools,s)}getToolsConfig(){return this.deepClone(this.config.tools)}isToolEnabled(s){var e;return((e=this.config.tools[s])==null?void 0:e.enabled)||!1}updateUIConfig(s){this.config.ui={...this.config.ui,...s}}getUIConfig(){return this.deepClone(this.config.ui)}updateSecurityConfig(s){this.config.security={...this.config.security,...s}}getSecurityConfig(){return this.deepClone(this.config.security)}async resetConfig(){this.config=this.deepClone(M),await this.saveConfig(),new I.Notice("\u914D\u7F6E\u5DF2\u91CD\u7F6E\u4E3A\u9ED8\u8BA4\u503C")}exportConfig(){let s=this.deepClone(this.config);return s.llm.apiKey="",s.tools.web.apiKey="",JSON.stringify(s,null,2)}async importConfig(s){try{let e=JSON.parse(s);if(!this.isValidConfigStructure(e))throw new Error("\u65E0\u6548\u7684\u914D\u7F6E\u683C\u5F0F");let t=this.config.llm.apiKey,r=this.config.tools.web.apiKey;this.config=this.mergeConfig(M,e),this.config.llm.apiKey||(this.config.llm.apiKey=t),this.config.tools.web.apiKey||(this.config.tools.web.apiKey=r),await this.saveConfig(),new I.Notice("\u914D\u7F6E\u5BFC\u5165\u6210\u529F")}catch(e){throw console.error("Failed to import config:",e),new I.Notice("\u914D\u7F6E\u5BFC\u5165\u5931\u8D25: "+e.message),e}}validateConfig(){if(this.config.llm.apiKey&&this.config.llm.apiKey.trim()){let s=E.validateConfig(this.config.llm);s.valid||console.warn("LLM\u914D\u7F6E\u9A8C\u8BC1\u5931\u8D25:",s.errors)}this.config.tools.vault.chunkSize<100&&(this.config.tools.vault.chunkSize=1e3),this.config.tools.javascript.timeout<1e3&&(this.config.tools.javascript.timeout=5e3),this.config.security.maxRequestsPerMinute<1&&(this.config.security.maxRequestsPerMinute=60)}async migrateConfig(s){console.log("Migrating config from version",s.version,"to",this.configVersion),this.config=this.mergeConfig(M,s),new I.Notice("\u914D\u7F6E\u5DF2\u5347\u7EA7\u5230\u65B0\u7248\u672C")}async encryptSensitiveData(s){s.llm.apiKey&&(s.llm.apiKey=btoa(s.llm.apiKey)),s.tools.web.apiKey&&(s.tools.web.apiKey=btoa(s.tools.web.apiKey))}async decryptSensitiveData(){try{this.config.llm.apiKey&&(this.config.llm.apiKey=atob(this.config.llm.apiKey)),this.config.tools.web.apiKey&&(this.config.tools.web.apiKey=atob(this.config.tools.web.apiKey))}catch(s){console.error("Failed to decrypt sensitive data:",s)}}isValidConfigStructure(s){return s&&typeof s=="object"&&s.llm&&s.tools&&s.ui&&s.security}deepClone(s){return JSON.parse(JSON.stringify(s))}mergeConfig(s,e){let t={...s};for(let r in e)e[r]!==void 0&&(typeof e[r]=="object"&&e[r]!==null&&!Array.isArray(e[r])?t[r]=this.mergeConfig(t[r]||{},e[r]):t[r]=e[r]);return t}};var W=class{constructor(){this.templates=new Map}registerTemplate(s){this.templates.set(s.id,s)}getTemplate(s){return this.templates.get(s)}listTemplates(s){let e=Array.from(this.templates.values());return s?e.filter(t=>t.category===s):e}render(s,e){let t=this.templates.get(s);if(!t)throw new Error(`Template not found: ${s}`);return this.validateContext(t,e),this.renderTemplate(t.template,e)}renderMultiple(s,e){return s.map(t=>this.render(t,e))}validateContext(s,e){for(let t of s.variables){if(t.required&&!(t.name in e))throw new Error(`Required variable missing: ${t.name}`);if(t.name in e){let r=e[t.name];if(!this.validateVariableType(r,t.type))throw new Error(`Invalid type for variable ${t.name}: expected ${t.type}`)}}}validateVariableType(s,e){switch(e){case"string":return typeof s=="string";case"number":return typeof s=="number";case"boolean":return typeof s=="boolean";case"array":return Array.isArray(s);case"object":return typeof s=="object"&&s!==null&&!Array.isArray(s);default:return!0}}renderTemplate(s,e){let t=s;return t=this.processConditionals(t,e),t=this.processLoops(t,e),t=this.processVariables(t,e),t.trim()}processConditionals(s,e){let t=/\{\{#if\s+(\w+)\}\}([\s\S]*?)\{\{\/if\}\}/g;return s.replace(t,(r,n,o)=>e[n]?o:"")}processLoops(s,e){let t=/\{\{#each\s+(\w+)\}\}([\s\S]*?)\{\{\/each\}\}/g;return s.replace(t,(r,n,o)=>{let i=e[n];return Array.isArray(i)?i.map((a,c)=>{let u=o;if(u=u.replace(/\{\{this\}\}/g,String(a)),u=u.replace(/\{\{@index\}\}/g,String(c)),typeof a=="object"&&a!==null)for(let[m,p]of Object.entries(a)){let f=new RegExp(`\\{\\{${m}\\}\\}`,"g");u=u.replace(f,String(p))}return u}).join(""):""})}processVariables(s,e){let t=/\{\{(\w+)\}\}/g;return s.replace(t,(r,n)=>{let o=e[n];return o!==void 0?String(o):r})}static createBuilder(){return new T}},T=class{constructor(){this.template={variables:[],version:"1.0.0",createdAt:new Date,updatedAt:new Date}}id(s){return this.template.id=s,this}name(s){return this.template.name=s,this}description(s){return this.template.description=s,this}category(s){return this.template.category=s,this}content(s){return this.template.template=s,this}variable(s){return this.template.variables.push(s),this}version(s){return this.template.version=s,this}build(){if(!this.template.id||!this.template.name||!this.template.template)throw new Error("Template id, name, and content are required");return this.template}},C={USER_INPUT:{name:"userInput",type:"string",required:!0,description:"\u7528\u6237\u8F93\u5165\u7684\u5185\u5BB9"},CONTEXT:{name:"context",type:"string",required:!1,description:"\u4E0A\u4E0B\u6587\u4FE1\u606F"},TOOLS:{name:"tools",type:"array",required:!1,description:"\u53EF\u7528\u5DE5\u5177\u5217\u8868"},RESULTS:{name:"results",type:"array",required:!1,description:"\u5DE5\u5177\u6267\u884C\u7ED3\u679C"},ERROR:{name:"error",type:"string",required:!0,description:"\u9519\u8BEF\u4FE1\u606F"}};var b=class{static getAllTemplates(){return[this.SYSTEM_PROMPT,this.TASK_ANALYSIS,this.TOOL_SELECTION,this.VAULT_QUERY,this.CONTENT_SUMMARY,this.ERROR_HANDLING,this.TOOL_RESULT_PROCESSING]}};b.SYSTEM_PROMPT=new T().id("system_prompt").name("\u7CFB\u7EDF\u63D0\u793A").description("\u5B9A\u4E49AI\u52A9\u624B\u7684\u89D2\u8272\u548C\u57FA\u672C\u884C\u4E3A\u89C4\u8303").category("system").content(`\u4F60\u662F\u4E00\u4E2A\u667A\u80FD\u7684Obsidian\u52A9\u624B\uFF0C\u540D\u4E3AAI Coach Advanced\u3002\u4F60\u7684\u4E3B\u8981\u804C\u8D23\u662F\uFF1A

1. **\u7406\u89E3\u7528\u6237\u610F\u56FE**\uFF1A\u51C6\u786E\u7406\u89E3\u7528\u6237\u7684\u81EA\u7136\u8BED\u8A00\u6307\u4EE4\uFF0C\u8BC6\u522B\u7528\u6237\u60F3\u8981\u5B8C\u6210\u7684\u4EFB\u52A1\u3002

2. **\u79EF\u6781\u4F7F\u7528\u5DE5\u5177**\uFF1A\u5F53\u7528\u6237\u7684\u8BF7\u6C42\u9700\u8981\u83B7\u53D6\u4FE1\u606F\u3001\u6267\u884C\u64CD\u4F5C\u6216\u5904\u7406\u6570\u636E\u65F6\uFF0C\u5FC5\u987B\u4E3B\u52A8\u8C03\u7528\u76F8\u5E94\u7684\u5DE5\u5177\u3002

3. **\u77E5\u8BC6\u5E93\u67E5\u8BE2**\uFF1A\u5F53\u9700\u8981\u67E5\u627E\u4FE1\u606F\u65F6\uFF0C\u4F18\u5148\u641C\u7D22\u7528\u6237\u7684Obsidian Vault\u7B14\u8BB0\u5185\u5BB9\u3002

4. **\u5185\u5BB9\u751F\u6210**\uFF1A\u57FA\u4E8E\u67E5\u8BE2\u7ED3\u679C\u548C\u7528\u6237\u9700\u6C42\uFF0C\u751F\u6210\u6709\u7528\u3001\u51C6\u786E\u7684\u56DE\u7B54\u6216\u5185\u5BB9\u3002

**\u91CD\u8981\uFF1A\u5DE5\u5177\u8C03\u7528\u6307\u5BFC**
- \u5F53\u7528\u6237\u8BE2\u95EE\u7B14\u8BB0\u5185\u5BB9\u3001\u67E5\u627E\u4FE1\u606F\u65F6 \u2192 \u4F7F\u7528 vault_query \u5DE5\u5177
- \u5F53\u9700\u8981\u6700\u65B0\u4FE1\u606F\u3001\u7F51\u7EDC\u641C\u7D22\u65F6 \u2192 \u4F7F\u7528 web_search \u5DE5\u5177
- \u5F53\u9700\u8981\u8BA1\u7B97\u3001\u6570\u636E\u5904\u7406\u3001\u4EE3\u7801\u6267\u884C\u65F6 \u2192 \u4F7F\u7528 javascript_executor \u5DE5\u5177
- \u5F53\u9700\u8981\u7BA1\u7406\u63D2\u4EF6\u3001\u6267\u884C\u547D\u4EE4\u65F6 \u2192 \u4F7F\u7528 plugin_manager \u5DE5\u5177
- \u4E0D\u8981\u4EC5\u51ED\u8BB0\u5FC6\u56DE\u7B54\uFF0C\u8981\u901A\u8FC7\u5DE5\u5177\u83B7\u53D6\u51C6\u786E\u4FE1\u606F

**\u884C\u4E3A\u51C6\u5219**\uFF1A
- \u59CB\u7EC8\u4EE5\u7528\u6237\u7684\u9700\u6C42\u4E3A\u4E2D\u5FC3
- \u4E3B\u52A8\u4F7F\u7528\u5DE5\u5177\u83B7\u53D6\u51C6\u786E\u4FE1\u606F
- \u5728\u4E0D\u786E\u5B9A\u65F6\u4E3B\u52A8\u8BE2\u95EE\u6F84\u6E05
- \u4FDD\u62A4\u7528\u6237\u9690\u79C1\u548C\u6570\u636E\u5B89\u5168
- \u627F\u8BA4\u81EA\u5DF1\u7684\u5C40\u9650\u6027

**\u5F53\u524D\u53EF\u7528\u5DE5\u5177**\uFF1A
{{#if tools}}
{{#each tools}}
- {{name}}: {{description}}
{{/each}}
{{/if}}

\u8BF7\u6839\u636E\u7528\u6237\u7684\u6307\u4EE4\uFF0C\u79EF\u6781\u9009\u62E9\u5408\u9002\u7684\u5DE5\u5177\u5E76\u63D0\u4F9B\u5E2E\u52A9\u3002`).variable(C.TOOLS).build(),b.TASK_ANALYSIS=new T().id("task_analysis").name("\u4EFB\u52A1\u5206\u6790").description("\u5206\u6790\u7528\u6237\u8F93\u5165\uFF0C\u8BC6\u522B\u4EFB\u52A1\u7C7B\u578B\u548C\u6240\u9700\u5DE5\u5177").category("task").content(`\u8BF7\u5206\u6790\u4EE5\u4E0B\u7528\u6237\u8F93\u5165\uFF0C\u786E\u5B9A\u9700\u8981\u6267\u884C\u7684\u4EFB\u52A1\uFF1A

\u7528\u6237\u8F93\u5165\uFF1A{{userInput}}

{{#if context}}
\u4E0A\u4E0B\u6587\u4FE1\u606F\uFF1A{{context}}
{{/if}}

\u8BF7\u6309\u4EE5\u4E0B\u683C\u5F0F\u5206\u6790\uFF1A

**\u4EFB\u52A1\u7C7B\u578B**\uFF1A[\u67E5\u8BE2\u4FE1\u606F/\u521B\u5EFA\u5185\u5BB9/\u6267\u884C\u64CD\u4F5C/\u5176\u4ED6]

**\u4E3B\u8981\u76EE\u6807**\uFF1A[\u7B80\u8981\u63CF\u8FF0\u7528\u6237\u60F3\u8981\u8FBE\u6210\u7684\u76EE\u6807]

**\u6240\u9700\u4FE1\u606F**\uFF1A[\u5217\u51FA\u5B8C\u6210\u4EFB\u52A1\u9700\u8981\u7684\u4FE1\u606F]

**\u5EFA\u8BAE\u5DE5\u5177**\uFF1A[\u6839\u636E\u4EFB\u52A1\u9700\u6C42\u63A8\u8350\u4F7F\u7528\u7684\u5DE5\u5177]

**\u6267\u884C\u6B65\u9AA4**\uFF1A
1. [\u7B2C\u4E00\u6B65]
2. [\u7B2C\u4E8C\u6B65]
3. [\u540E\u7EED\u6B65\u9AA4...]

\u8BF7\u57FA\u4E8E\u8FD9\u4E2A\u5206\u6790\u5236\u5B9A\u6267\u884C\u8BA1\u5212\u3002`).variable(C.USER_INPUT).variable(C.CONTEXT).build(),b.TOOL_SELECTION=new T().id("tool_selection").name("\u5DE5\u5177\u9009\u62E9").description("\u6839\u636E\u4EFB\u52A1\u9700\u6C42\u9009\u62E9\u5408\u9002\u7684\u5DE5\u5177").category("tool").content(`\u6839\u636E\u4EE5\u4E0B\u4EFB\u52A1\u9700\u6C42\uFF0C\u9009\u62E9\u6700\u5408\u9002\u7684\u5DE5\u5177\uFF1A

**\u4EFB\u52A1\u63CF\u8FF0**\uFF1A{{taskDescription}}

**\u53EF\u7528\u5DE5\u5177**\uFF1A
{{#each tools}}
- **{{name}}**\uFF1A{{description}}
  \u53C2\u6570\uFF1A{{#each parameters}}{{name}} ({{type}}){{#unless @last}}, {{/unless}}{{/each}}
{{/each}}

**\u9009\u62E9\u6807\u51C6**\uFF1A
1. \u5DE5\u5177\u529F\u80FD\u662F\u5426\u5339\u914D\u4EFB\u52A1\u9700\u6C42
2. \u5DE5\u5177\u7684\u53EF\u9760\u6027\u548C\u5B89\u5168\u6027
3. \u6267\u884C\u6548\u7387\u548C\u7528\u6237\u4F53\u9A8C

\u8BF7\u9009\u62E9\u6700\u5408\u9002\u7684\u5DE5\u5177\u5E76\u8BF4\u660E\u7406\u7531\u3002\u5982\u679C\u9700\u8981\u591A\u4E2A\u5DE5\u5177\u914D\u5408\uFF0C\u8BF7\u8BF4\u660E\u8C03\u7528\u987A\u5E8F\u3002

**\u9009\u62E9\u7ED3\u679C**\uFF1A
\u5DE5\u5177\u540D\u79F0\uFF1A[\u9009\u62E9\u7684\u5DE5\u5177]
\u8C03\u7528\u53C2\u6570\uFF1A[\u5177\u4F53\u53C2\u6570\u503C]
\u9009\u62E9\u7406\u7531\uFF1A[\u4E3A\u4EC0\u4E48\u9009\u62E9\u8FD9\u4E2A\u5DE5\u5177]`).variable({name:"taskDescription",type:"string",required:!0,description:"\u4EFB\u52A1\u63CF\u8FF0"}).variable(C.TOOLS).build(),b.VAULT_QUERY=new T().id("vault_query").name("Vault\u67E5\u8BE2").description("\u6784\u9020Vault\u77E5\u8BC6\u5E93\u67E5\u8BE2").category("tool").content(`\u57FA\u4E8E\u7528\u6237\u95EE\u9898\u6784\u9020Vault\u67E5\u8BE2\uFF1A

**\u7528\u6237\u95EE\u9898**\uFF1A{{userInput}}

**\u67E5\u8BE2\u7B56\u7565**\uFF1A
1. \u63D0\u53D6\u5173\u952E\u8BCD\u548C\u6982\u5FF5
2. \u8003\u8651\u540C\u4E49\u8BCD\u548C\u76F8\u5173\u672F\u8BED
3. \u6784\u9020\u6709\u6548\u7684\u641C\u7D22\u67E5\u8BE2

**\u67E5\u8BE2\u5173\u952E\u8BCD**\uFF1A[\u63D0\u53D6\u7684\u5173\u952E\u8BCD]

**\u641C\u7D22\u67E5\u8BE2**\uFF1A[\u4F18\u5316\u540E\u7684\u67E5\u8BE2\u8BED\u53E5]

**\u9884\u671F\u7ED3\u679C**\uFF1A[\u671F\u671B\u627E\u5230\u4EC0\u4E48\u7C7B\u578B\u7684\u4FE1\u606F]`).variable(C.USER_INPUT).build(),b.CONTENT_SUMMARY=new T().id("content_summary").name("\u5185\u5BB9\u603B\u7ED3").description("\u603B\u7ED3\u67E5\u8BE2\u7ED3\u679C\u6216\u751F\u6210\u5185\u5BB9").category("response").content(`\u57FA\u4E8E\u4EE5\u4E0B\u4FE1\u606F\u4E3A\u7528\u6237\u63D0\u4F9B\u56DE\u7B54\uFF1A

**\u7528\u6237\u95EE\u9898**\uFF1A{{userInput}}

**\u67E5\u8BE2\u7ED3\u679C**\uFF1A
{{#each results}}
**\u6765\u6E90**\uFF1A{{source}}
**\u5185\u5BB9**\uFF1A{{content}}
**\u76F8\u5173\u6027**\uFF1A{{score}}

{{/each}}

\u8BF7\u57FA\u4E8E\u8FD9\u4E9B\u4FE1\u606F\u63D0\u4F9B\u4E00\u4E2A\u51C6\u786E\u3001\u6709\u7528\u7684\u56DE\u7B54\u3002\u8981\u6C42\uFF1A

1. **\u76F4\u63A5\u56DE\u7B54**\uFF1A\u9996\u5148\u76F4\u63A5\u56DE\u7B54\u7528\u6237\u7684\u95EE\u9898
2. **\u652F\u6491\u4FE1\u606F**\uFF1A\u5F15\u7528\u76F8\u5173\u7684\u7B14\u8BB0\u5185\u5BB9\u4F5C\u4E3A\u652F\u6491
3. **\u6765\u6E90\u6807\u6CE8**\uFF1A\u6807\u660E\u4FE1\u606F\u6765\u6E90\u7684\u7B14\u8BB0\u6587\u4EF6
4. **\u8865\u5145\u5EFA\u8BAE**\uFF1A\u5982\u679C\u6709\u76F8\u5173\u7684\u8865\u5145\u4FE1\u606F\u6216\u5EFA\u8BAE\uFF0C\u8BF7\u63D0\u4F9B

**\u56DE\u7B54\u683C\u5F0F**\uFF1A
[\u76F4\u63A5\u56DE\u7B54\u7528\u6237\u95EE\u9898]

**\u8BE6\u7EC6\u4FE1\u606F**\uFF1A
[\u57FA\u4E8E\u67E5\u8BE2\u7ED3\u679C\u7684\u8BE6\u7EC6\u8BF4\u660E]

**\u76F8\u5173\u7B14\u8BB0**\uFF1A
{{#each results}}
- [[{{source}}]]\uFF1A{{content}}
{{/each}}

{{#if suggestions}}
**\u5EFA\u8BAE**\uFF1A
{{suggestions}}
{{/if}}`).variable(C.USER_INPUT).variable(C.RESULTS).variable({name:"suggestions",type:"string",required:!1,description:"\u8865\u5145\u5EFA\u8BAE"}).build(),b.ERROR_HANDLING=new T().id("error_handling").name("\u9519\u8BEF\u5904\u7406").description("\u5904\u7406\u5DE5\u5177\u8C03\u7528\u9519\u8BEF\u6216\u5F02\u5E38\u60C5\u51B5").category("error").content(`\u5904\u7406\u6267\u884C\u8FC7\u7A0B\u4E2D\u7684\u9519\u8BEF\uFF1A

**\u9519\u8BEF\u4FE1\u606F**\uFF1A{{error}}

**\u7528\u6237\u8BF7\u6C42**\uFF1A{{userInput}}

**\u9519\u8BEF\u5206\u6790**\uFF1A
[\u5206\u6790\u9519\u8BEF\u539F\u56E0\u548C\u53EF\u80FD\u7684\u89E3\u51B3\u65B9\u6848]

**\u7528\u6237\u53CD\u9988**\uFF1A
\u62B1\u6B49\uFF0C\u5728\u5904\u7406\u60A8\u7684\u8BF7\u6C42\u65F6\u9047\u5230\u4E86\u95EE\u9898\uFF1A{{error}}

**\u53EF\u80FD\u7684\u89E3\u51B3\u65B9\u6848**\uFF1A
1. [\u5EFA\u8BAE\u7684\u89E3\u51B3\u65B9\u68481]
2. [\u5EFA\u8BAE\u7684\u89E3\u51B3\u65B9\u68482]
3. [\u5176\u4ED6\u66FF\u4EE3\u65B9\u6848]

\u60A8\u53EF\u4EE5\u5C1D\u8BD5\uFF1A
- \u91CD\u65B0\u8868\u8FF0\u60A8\u7684\u95EE\u9898
- \u68C0\u67E5\u76F8\u5173\u8BBE\u7F6E
- \u6216\u8005\u6211\u53EF\u4EE5\u7528\u5176\u4ED6\u65B9\u5F0F\u5E2E\u52A9\u60A8

\u8BF7\u544A\u8BC9\u6211\u60A8\u5E0C\u671B\u5982\u4F55\u7EE7\u7EED\u3002`).variable(C.ERROR).variable(C.USER_INPUT).build(),b.TOOL_RESULT_PROCESSING=new T().id("tool_result_processing").name("\u5DE5\u5177\u7ED3\u679C\u5904\u7406").description("\u5904\u7406\u548C\u89E3\u91CA\u5DE5\u5177\u8C03\u7528\u7ED3\u679C").category("response").content(`\u5904\u7406\u5DE5\u5177\u8C03\u7528\u7ED3\u679C\uFF1A

**\u5DE5\u5177\u540D\u79F0**\uFF1A{{toolName}}
**\u8C03\u7528\u53C2\u6570**\uFF1A{{parameters}}
**\u6267\u884C\u7ED3\u679C**\uFF1A{{result}}
**\u7528\u6237\u539F\u59CB\u8BF7\u6C42**\uFF1A{{userInput}}

**\u7ED3\u679C\u5206\u6790**\uFF1A
{{#if result.success}}
\u5DE5\u5177\u6267\u884C\u6210\u529F\u3002\u7ED3\u679C\u6570\u636E\uFF1A{{result.data}}

**\u7528\u6237\u56DE\u590D**\uFF1A
[\u57FA\u4E8E\u7ED3\u679C\u4E3A\u7528\u6237\u63D0\u4F9B\u6709\u7528\u7684\u56DE\u7B54]
{{else}}
\u5DE5\u5177\u6267\u884C\u5931\u8D25\u3002\u9519\u8BEF\u4FE1\u606F\uFF1A{{result.error}}

**\u7528\u6237\u56DE\u590D**\uFF1A
\u5F88\u62B1\u6B49\uFF0C{{toolName}}\u5DE5\u5177\u6267\u884C\u65F6\u51FA\u73B0\u95EE\u9898\uFF1A{{result.error}}

\u8BA9\u6211\u5C1D\u8BD5\u5176\u4ED6\u65B9\u6CD5\u6765\u5E2E\u52A9\u60A8\u3002
{{/if}}`).variable({name:"toolName",type:"string",required:!0,description:"\u5DE5\u5177\u540D\u79F0"}).variable({name:"parameters",type:"object",required:!0,description:"\u8C03\u7528\u53C2\u6570"}).variable({name:"result",type:"object",required:!0,description:"\u6267\u884C\u7ED3\u679C"}).variable(C.USER_INPUT).build();var G=class{constructor(){l(this,"templateEngine");l(this,"availableTools",[]);l(this,"conversationHistory",[]);this.templateEngine=new W,this.initializeBaseTemplates()}initializeBaseTemplates(){b.getAllTemplates().forEach(e=>{this.templateEngine.registerTemplate(e)})}setAvailableTools(s){this.availableTools=s}setConversationHistory(s){this.conversationHistory=s}generateSystemPrompt(){let s=this.availableTools.map(e=>({name:e.name,description:e.description}));return this.templateEngine.render("system_prompt",{tools:s})}generateTaskAnalysisPrompt(s,e){return this.templateEngine.render("task_analysis",{userInput:s,context:e||this.buildContextFromHistory()})}generateToolSelectionPrompt(s){let e=this.availableTools.map(t=>({name:t.name,description:t.description,parameters:Object.entries(t.parameters.properties||{}).map(([r,n])=>({name:r,type:n.type,description:n.description}))}));return this.templateEngine.render("tool_selection",{taskDescription:s,tools:e})}generateVaultQueryPrompt(s){return this.templateEngine.render("vault_query",{userInput:s})}generateContentSummaryPrompt(s,e,t){return this.templateEngine.render("content_summary",{userInput:s,results:e,suggestions:t})}generateErrorHandlingPrompt(s,e){return this.templateEngine.render("error_handling",{userInput:s,error:e})}generateToolResultProcessingPrompt(s,e,t,r){return this.templateEngine.render("tool_result_processing",{userInput:s,toolName:e,parameters:t,result:r})}generateConversationPrompt(s,e=!0,t=5){let r=this.generateSystemPrompt(),n=[];return e&&this.conversationHistory.length>0&&this.conversationHistory.slice(-t).flatMap(i=>i.messages.slice(-2)).forEach(i=>{n.push({role:i.role,content:i.content})}),n.push({role:"user",content:s}),{systemPrompt:r,messages:n}}generateTaskSpecificPrompt(s,e,t){switch(s){case"query":return this.generateVaultQueryPrompt(e);case"create":return this.generateCreationPrompt(e,t);case"analyze":return this.generateAnalysisPrompt(e,t);case"execute":return this.generateExecutionPrompt(e,t);default:return this.generateTaskAnalysisPrompt(e)}}generateCreationPrompt(s,e){return`\u57FA\u4E8E\u4EE5\u4E0B\u8981\u6C42\u521B\u5EFA\u5185\u5BB9\uFF1A

\u7528\u6237\u9700\u6C42\uFF1A${s}

${e?`\u53C2\u8003\u4FE1\u606F\uFF1A${JSON.stringify(e,null,2)}`:""}

\u8BF7\u521B\u5EFA\u7B26\u5408\u8981\u6C42\u7684\u5185\u5BB9\uFF0C\u786E\u4FDD\uFF1A
1. \u5185\u5BB9\u7ED3\u6784\u6E05\u6670
2. \u4FE1\u606F\u51C6\u786E\u5B8C\u6574
3. \u683C\u5F0F\u7B26\u5408Obsidian Markdown\u89C4\u8303
4. \u5305\u542B\u9002\u5F53\u7684\u94FE\u63A5\u548C\u6807\u7B7E`}generateAnalysisPrompt(s,e){return`\u8BF7\u5206\u6790\u4EE5\u4E0B\u5185\u5BB9\uFF1A

\u5206\u6790\u76EE\u6807\uFF1A${s}

${e?`\u76F8\u5173\u6570\u636E\uFF1A${JSON.stringify(e,null,2)}`:""}

\u8BF7\u63D0\u4F9B\uFF1A
1. \u5173\u952E\u53D1\u73B0\u548C\u6D1E\u5BDF
2. \u6570\u636E\u8D8B\u52BF\u548C\u6A21\u5F0F
3. \u7ED3\u8BBA\u548C\u5EFA\u8BAE
4. \u652F\u6491\u8BC1\u636E`}generateExecutionPrompt(s,e){return`\u6267\u884C\u4EE5\u4E0B\u4EFB\u52A1\uFF1A

\u4EFB\u52A1\u63CF\u8FF0\uFF1A${s}

${e?`\u6267\u884C\u73AF\u5883\uFF1A${JSON.stringify(e,null,2)}`:""}

\u8BF7\uFF1A
1. \u786E\u8BA4\u4EFB\u52A1\u7406\u89E3\u6B63\u786E
2. \u9009\u62E9\u5408\u9002\u7684\u5DE5\u5177\u548C\u65B9\u6CD5
3. \u6309\u6B65\u9AA4\u6267\u884C
4. \u62A5\u544A\u6267\u884C\u7ED3\u679C`}buildContextFromHistory(){return this.conversationHistory.length===0?"":this.conversationHistory.slice(-1)[0].messages.slice(-3).map(t=>`${t.role}: ${t.content}`).join(`
`)}registerCustomTemplate(s){this.templateEngine.registerTemplate(s)}getTemplateEngine(){return this.templateEngine}optimizePromptLength(s,e=4e3){if(s.length<=e)return s;let t=Math.floor(e*.6),r=Math.floor(e*.3),n=s.substring(0,t),o=s.substring(s.length-r);return`${n}

[... \u5185\u5BB9\u5DF2\u622A\u65AD ...]

${o}`}validatePromptQuality(s){let e=[],t=[],r=100;return s.length<50?(e.push("\u63D0\u793A\u8FC7\u77ED\uFF0C\u53EF\u80FD\u7F3A\u5C11\u5FC5\u8981\u4FE1\u606F"),r-=20):s.length>8e3&&(e.push("\u63D0\u793A\u8FC7\u957F\uFF0C\u53EF\u80FD\u5F71\u54CD\u5904\u7406\u6548\u7387"),r-=10),!s.includes("\uFF1A")&&!s.includes(":")&&(t.push("\u5EFA\u8BAE\u6DFB\u52A0\u660E\u786E\u7684\u6307\u4EE4\u7ED3\u6784"),r-=5),(s.includes("{{")||s.includes("}}"))&&(e.push("\u5B58\u5728\u672A\u66FF\u6362\u7684\u6A21\u677F\u53D8\u91CF"),r-=15),{score:Math.max(0,r),issues:e,suggestions:t}}};var J=class{constructor(){l(this,"tools",new Map);l(this,"toolsByCategory",new Map);l(this,"executionHistory",[]);l(this,"maxHistorySize",1e3);["vault","web","plugin","javascript","memory","utility","ai","custom"].forEach(e=>{this.toolsByCategory.set(e,new Set)})}async register(s){try{if(this.tools.has(s.name))throw new Error(`Tool with name '${s.name}' already exists`);this.validateToolConfig(s),s.initialize&&await s.initialize(),this.tools.set(s.name,s);let e=this.toolsByCategory.get(s.category);e&&e.add(s.name),console.log(`Tool registered: ${s.name} (${s.category})`)}catch(e){throw console.error(`Failed to register tool ${s.name}:`,e),e}}async unregister(s){let e=this.tools.get(s);if(!e)throw new Error(`Tool not found: ${s}`);try{e.cleanup&&await e.cleanup(),this.tools.delete(s);let t=this.toolsByCategory.get(e.category);t&&t.delete(s),console.log(`Tool unregistered: ${s}`)}catch(t){throw console.error(`Failed to unregister tool ${s}:`,t),t}}get(s){return this.tools.get(s)}list(s){if(s){let e=this.toolsByCategory.get(s);return e?Array.from(e).map(t=>this.tools.get(t)).filter(t=>t!==void 0):[]}return Array.from(this.tools.values())}async execute(s,e,t){let r=Date.now(),n=this.tools.get(s);if(!n){let o={success:!1,error:`Tool not found: ${s}`,metadata:{executionTime:Date.now()-r}};return this.recordExecution(s,e,o,t),o}try{let o=await n.execute(e,t);return this.recordExecution(s,e,o,t),o}catch(o){let i={success:!1,error:o instanceof Error?o.message:String(o),metadata:{executionTime:Date.now()-r}};return this.recordExecution(s,e,i,t),i}}async validate(s,e){let t=this.tools.get(s);return t?t.validate?await t.validate(e):{valid:!0,errors:[],warnings:[]}:{valid:!1,errors:[`Tool not found: ${s}`],warnings:[]}}getToolDefinition(s){let e=this.tools.get(s);if(e)return{name:e.name,description:e.description,category:e.category,parameters:e.parameters,permissions:e.permissions,metadata:e.metadata}}getToolDefinitions(s){return this.list(s).map(t=>({name:t.name,description:t.description,category:t.category,parameters:t.parameters,permissions:t.permissions,metadata:t.metadata}))}async registerBatch(s){let t=(await Promise.allSettled(s.map(r=>this.register(r)))).map((r,n)=>({result:r,tool:s[n]})).filter(({result:r})=>r.status==="rejected").map(({result:r,tool:n})=>({tool:n.name,error:r.reason}));t.length>0&&console.warn("Some tools failed to register:",t)}getStats(){let s=this.tools.size,e={};for(let[r,n]of this.toolsByCategory.entries())e[r]=n.size;let t=this.executionHistory.filter(r=>Date.now()-r.timestamp.getTime()<24*60*60*1e3).length;return{totalTools:s,toolsByCategory:e,recentExecutions:t,totalExecutions:this.executionHistory.length}}getExecutionHistory(s){let e=[...this.executionHistory].reverse();return s?e.slice(0,s):e}clearExecutionHistory(){this.executionHistory=[]}validateToolConfig(s){if(!s.name||typeof s.name!="string")throw new Error("Tool name is required and must be a string");if(!s.description||typeof s.description!="string")throw new Error("Tool description is required and must be a string");if(!s.category)throw new Error("Tool category is required");if(!s.parameters||typeof s.parameters!="object")throw new Error("Tool parameters are required");if(!s.permissions||typeof s.permissions!="object")throw new Error("Tool permissions are required");if(!s.metadata||typeof s.metadata!="object")throw new Error("Tool metadata is required")}recordExecution(s,e,t,r){let n={toolName:s,args:e,result:t,context:r,timestamp:new Date};this.executionHistory.push(n),this.executionHistory.length>this.maxHistorySize&&(this.executionHistory=this.executionHistory.slice(-this.maxHistorySize))}};var Y=class{constructor(s,e){l(this,"registry");l(this,"executionQueue",[]);l(this,"isProcessing",!1);l(this,"maxConcurrentExecutions",3);l(this,"currentExecutions",new Set);l(this,"rateLimiter");this.registry=s,this.maxConcurrentExecutions=(e==null?void 0:e.maxConcurrentExecutions)||3,this.rateLimiter=new ge((e==null?void 0:e.rateLimit)||{maxRequests:60,windowMs:6e4})}async executeTool(s,e,t){var n;let r=this.generateExecutionId();try{if(!this.rateLimiter.checkLimit((t==null?void 0:t.userId)||"anonymous"))return{success:!1,error:"\u8BF7\u6C42\u9891\u7387\u8FC7\u9AD8\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5",metadata:{executionId:r,rateLimited:!0}};let o=this.registry.get(s);if(!o)return{success:!1,error:`\u5DE5\u5177\u4E0D\u5B58\u5728: ${s}`,metadata:{executionId:r}};if(this.currentExecutions.size>=this.maxConcurrentExecutions)return{success:!1,error:"\u7CFB\u7EDF\u7E41\u5FD9\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5",metadata:{executionId:r,concurrencyLimited:!0}};let i=await this.registry.validate(s,e);if(!i.valid)return{success:!1,error:`\u53C2\u6570\u9A8C\u8BC1\u5931\u8D25: ${i.errors.join(", ")}`,metadata:{executionId:r,validationErrors:i.errors,validationWarnings:i.warnings}};if(t){let a=this.checkToolPermissions(o,t);if(!a.allowed)return{success:!1,error:`\u6743\u9650\u4E0D\u8DB3: ${a.reason}`,metadata:{executionId:r,permissionDenied:!0}};if(o.permissions.requiresConfirmation&&!((n=t.config)!=null&&n.confirmed))return{success:!1,error:"\u6B64\u64CD\u4F5C\u9700\u8981\u7528\u6237\u786E\u8BA4",metadata:{executionId:r,requiresConfirmation:!0,toolName:s,args:e}}}this.currentExecutions.add(r);try{let a=await this.registry.execute(s,e,{...t,requestId:r});return this.postProcessResult(a,o,e)}finally{this.currentExecutions.delete(r)}}catch(o){return this.currentExecutions.delete(r),{success:!1,error:o instanceof Error?o.message:String(o),metadata:{executionId:r,unexpectedError:!0}}}}async executeToolsBatch(s,e){let t=[];for(let r of s){let n=await this.executeTool(r.toolName,r.args,e);if(t.push(n),!n.success&&r.stopOnFailure)break}return t}async queueToolExecution(s,e,t,r="normal"){let n=this.generateExecutionId(),o={id:n,toolName:s,args:e,context:t,priority:r,createdAt:new Date,status:"queued"};return this.executionQueue.push(o),this.sortQueue(),this.isProcessing||this.processQueue(),n}getExecutionStatus(s){if(this.currentExecutions.has(s))return{status:"running",executionId:s};let e=this.executionQueue.find(t=>t.id===s);return e?{status:e.status,executionId:s,queuePosition:this.executionQueue.indexOf(e)+1}:null}cancelExecution(s){let e=this.executionQueue.findIndex(t=>t.id===s);return e!==-1?(this.executionQueue.splice(e,1),!0):!1}getQueueStatus(){return{queueLength:this.executionQueue.length,currentExecutions:this.currentExecutions.size,maxConcurrentExecutions:this.maxConcurrentExecutions,isProcessing:this.isProcessing}}checkToolPermissions(s,e){for(let r of s.permissions.required)if(!e.permissions.includes(r))return{allowed:!1,reason:`\u7F3A\u5C11\u5FC5\u9700\u6743\u9650: ${r}`};if(s.permissions.dangerous&&!e.permissions.includes("dangerous_operations"))return{allowed:!1,reason:"\u6B64\u5DE5\u5177\u6267\u884C\u5371\u9669\u64CD\u4F5C\uFF0C\u9700\u8981\u7279\u6B8A\u6743\u9650"};let t=`tool_${s.category}`;return!e.permissions.includes(t)&&!e.permissions.includes("tool_all")?{allowed:!1,reason:`\u7F3A\u5C11\u5DE5\u5177\u5206\u7C7B\u6743\u9650: ${t}`}:{allowed:!0}}postProcessResult(s,e,t){let r={...s.metadata,toolName:e.name,toolCategory:e.category,toolVersion:e.version};return e.permissions.dangerous&&s.success&&(r.dangerousOperation=!0),{...s,metadata:r}}async processQueue(){if(!this.isProcessing){this.isProcessing=!0;try{for(;this.executionQueue.length>0&&this.currentExecutions.size<this.maxConcurrentExecutions;){let s=this.executionQueue.shift();if(!s)break;s.status="running",this.executeQueuedTask(s).catch(e=>{console.error("Queued task execution failed:",e)})}}finally{this.isProcessing=!1}this.executionQueue.length>0&&setTimeout(()=>this.processQueue(),100)}}async executeQueuedTask(s){try{let e=await this.executeTool(s.toolName,s.args,s.context);s.status="completed",s.result=e}catch(e){s.status="failed",s.error=e instanceof Error?e.message:String(e)}}sortQueue(){let s={high:3,normal:2,low:1};this.executionQueue.sort((e,t)=>{let r=s[e.priority];return s[t.priority]-r})}generateExecutionId(){return`exec_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}},ge=class{constructor(s){l(this,"requests",new Map);l(this,"maxRequests");l(this,"windowMs");this.maxRequests=s.maxRequests,this.windowMs=s.windowMs}checkLimit(s){let e=Date.now(),r=(this.requests.get(s)||[]).filter(n=>e-n<this.windowMs);return r.length>=this.maxRequests?!1:(r.push(e),this.requests.set(s,r),!0)}};var F=class{constructor(s,e,t){this.maxIterations=10;this.maxThinkingSteps=3;this.currentUserInput="";this.llm=s,this.promptManager=e,this.toolRegistry=t}async planAndExecute(s,e){let t=Date.now(),r=[];this.currentUserInput=s;try{let n=await this.analyzeTask(s,e);if(r.push({type:"analysis",content:n.analysis,timestamp:new Date}),!n.executable)return{success:!1,result:n.analysis,executionLog:r,executionTime:Date.now()-t,error:"\u4EFB\u52A1\u65E0\u6CD5\u6267\u884C"};let o=await this.executeReActLoop(s,n,r);return{success:o.success,result:o.finalAnswer,executionLog:r,executionTime:Date.now()-t,toolsUsed:o.toolsUsed,iterations:o.iterations}}catch(n){return r.push({type:"error",content:`\u6267\u884C\u9519\u8BEF: ${n.message}`,timestamp:new Date}),{success:!1,result:"\u4EFB\u52A1\u6267\u884C\u8FC7\u7A0B\u4E2D\u53D1\u751F\u9519\u8BEF",executionLog:r,executionTime:Date.now()-t,error:n.message}}}async analyzeTask(s,e){let r=[{role:"system",content:this.promptManager.generateTaskAnalysisPrompt(s,e==null?void 0:e.conversationHistory)},{role:"user",content:s}],n=await this.llm.generateText(r);return this.parseTaskAnalysis(n.content)}async executeReActLoop(s,e,t){let r=this.toolRegistry.list(),n=[],o=s,i="",a=0;for(let c=0;c<this.maxIterations;c++){a++;let u=await this.generateThought(o,r,t);t.push({type:"thought",content:u,timestamp:new Date});let m=await this.generateAction(u,r);if(t.push({type:"action",content:`\u8BA1\u5212\u6267\u884C: ${m.type} - ${m.description}`,timestamp:new Date}),m.type==="tool_call"){let p=await this.executeTool(m.toolName,m.parameters);t.push({type:"tool_result",content:`\u5DE5\u5177 ${m.toolName} \u6267\u884C\u7ED3\u679C: ${JSON.stringify(p.data)}`,timestamp:new Date,toolName:m.toolName,toolResult:p}),n.includes(m.toolName)||n.push(m.toolName),o+=`

\u5DE5\u5177\u6267\u884C\u7ED3\u679C:
${JSON.stringify(p.data)}`}else if(m.type==="final_answer"){i=m.answer,t.push({type:"final_answer",content:i,timestamp:new Date});break}else if(m.type==="need_more_info"){i=m.question,t.push({type:"clarification",content:i,timestamp:new Date});break}if(await this.shouldStop(o,t)){i=await this.generateFinalAnswer(o,t);break}}return a>=this.maxIterations&&!i&&(i="\u4EFB\u52A1\u6267\u884C\u8D85\u8FC7\u6700\u5927\u8FED\u4EE3\u6B21\u6570\uFF0C\u8BF7\u7B80\u5316\u4EFB\u52A1\u6216\u63D0\u4F9B\u66F4\u660E\u786E\u7684\u6307\u4EE4\u3002"),{success:!!i,finalAnswer:i,toolsUsed:n,iterations:a}}async generateThought(s,e,t){let r=this.promptManager.generateSystemPrompt(),n=e.map(u=>`${u.name}: ${u.description}`).join(`
`),o=t.slice(-5).map(u=>`${u.type}: ${u.content}`).join(`
`),i=`
\u4F60\u662F\u4E00\u4E2A\u667A\u80FD\u52A9\u624B\uFF0C\u9700\u8981\u5206\u6790\u7528\u6237\u8BF7\u6C42\u5E76\u51B3\u5B9A\u5982\u4F55\u5E2E\u52A9\u7528\u6237\u3002

\u7528\u6237\u539F\u59CB\u8BF7\u6C42\uFF1A${this.currentUserInput}

\u5F53\u524D\u4E0A\u4E0B\u6587\uFF1A
${s}

\u53EF\u7528\u5DE5\u5177\uFF1A
${n}

\u6700\u8FD1\u7684\u6267\u884C\u6B65\u9AA4\uFF1A
${o}

\u8BF7\u4ED4\u7EC6\u5206\u6790\u7528\u6237\u7684\u8BF7\u6C42\u7C7B\u578B\uFF1A
- \u5982\u679C\u662F\u8BA1\u7B97\u3001\u6570\u5B66\u95EE\u9898 \u2192 \u9700\u8981\u4F7F\u7528 javascript_executor \u5DE5\u5177
- \u5982\u679C\u662F\u641C\u7D22\u7B14\u8BB0\u3001\u67E5\u627E\u6587\u6863 \u2192 \u9700\u8981\u4F7F\u7528 vault_query \u5DE5\u5177
- \u5982\u679C\u662F\u7F51\u7EDC\u641C\u7D22\u3001\u6700\u65B0\u4FE1\u606F \u2192 \u9700\u8981\u4F7F\u7528 web_search \u5DE5\u5177
- \u5982\u679C\u662F\u63D2\u4EF6\u64CD\u4F5C \u2192 \u9700\u8981\u4F7F\u7528 plugin_manager \u5DE5\u5177

\u8BF7\u601D\u8003\uFF1A\u7528\u6237\u60F3\u8981\u4EC0\u4E48\uFF1F\u9700\u8981\u4F7F\u7528\u54EA\u4E2A\u5DE5\u5177\uFF1F\u4E3A\u4EC0\u4E48\uFF1F`,a=[{role:"system",content:r},{role:"user",content:i}];return(await this.llm.generateText(a)).content}async generateAction(s,e){let t=e.map(i=>({name:i.name,description:i.description,parameters:i.parameters})),n=[{role:"user",content:`
\u4F60\u9700\u8981\u6839\u636E\u7528\u6237\u7684\u8BF7\u6C42\u548C\u5F53\u524D\u601D\u8003\uFF0C\u51B3\u5B9A\u4E0B\u4E00\u6B65\u7684\u5177\u4F53\u884C\u52A8\u3002

\u7528\u6237\u539F\u59CB\u8BF7\u6C42\uFF1A${this.currentUserInput||""}

\u5F53\u524D\u601D\u8003\uFF1A
${s}

\u53EF\u7528\u5DE5\u5177\uFF1A
${JSON.stringify(t,null,2)}

**\u5DE5\u5177\u9009\u62E9\u6307\u5357**\uFF1A
1. **vault_query** - \u7528\u4E8E\u641C\u7D22Obsidian\u7B14\u8BB0\u5185\u5BB9
   - \u5173\u952E\u8BCD\uFF1A\u641C\u7D22\u7B14\u8BB0\u3001\u67E5\u627E\u6587\u6863\u3001\u6211\u7684\u7B14\u8BB0\u3001vault\u5185\u5BB9

2. **web_search** - \u7528\u4E8E\u7F51\u7EDC\u641C\u7D22\u6700\u65B0\u4FE1\u606F
   - \u5173\u952E\u8BCD\uFF1A\u6700\u65B0\u4FE1\u606F\u3001\u7F51\u7EDC\u641C\u7D22\u3001\u5B9E\u65F6\u6570\u636E\u3001\u65B0\u95FB

3. **javascript_executor** - \u7528\u4E8E\u6267\u884C\u8BA1\u7B97\u548C\u4EE3\u7801
   - \u5173\u952E\u8BCD\uFF1A\u8BA1\u7B97\u3001\u6570\u5B66\u3001\u4EE3\u7801\u6267\u884C\u3001\u6570\u636E\u5904\u7406\u3001\u7F16\u7A0B

4. **plugin_manager** - \u7528\u4E8E\u7BA1\u7406Obsidian\u63D2\u4EF6
   - \u5173\u952E\u8BCD\uFF1A\u63D2\u4EF6\u3001\u547D\u4EE4\u3001Obsidian\u529F\u80FD

**\u51B3\u7B56\u89C4\u5219**\uFF1A
- \u5982\u679C\u7528\u6237\u8981\u6C42\u8BA1\u7B97\u3001\u6570\u5B66\u8FD0\u7B97 \u2192 \u4F7F\u7528 javascript_executor
- \u5982\u679C\u7528\u6237\u8981\u6C42\u641C\u7D22\u7B14\u8BB0\u3001\u67E5\u627E\u6587\u6863 \u2192 \u4F7F\u7528 vault_query
- \u5982\u679C\u7528\u6237\u8981\u6C42\u7F51\u7EDC\u641C\u7D22\u3001\u6700\u65B0\u4FE1\u606F \u2192 \u4F7F\u7528 web_search
- \u5982\u679C\u7528\u6237\u8981\u6C42\u63D2\u4EF6\u64CD\u4F5C \u2192 \u4F7F\u7528 plugin_manager
- \u5982\u679C\u5DF2\u7ECF\u83B7\u5F97\u8DB3\u591F\u4FE1\u606F\u53EF\u4EE5\u56DE\u7B54 \u2192 \u4F7F\u7528 final_answer

\u8BF7\u4E25\u683C\u6309\u7167JSON\u683C\u5F0F\u8FD4\u56DE\uFF0C\u4E0D\u8981\u6DFB\u52A0\u4EFB\u4F55\u5176\u4ED6\u6587\u672C\uFF1A
{
  "type": "tool_call|final_answer|need_more_info",
  "description": "\u884C\u52A8\u63CF\u8FF0",
  "toolName": "\u5DE5\u5177\u540D\u79F0\uFF08\u4EC5tool_call\u65F6\u9700\u8981\uFF09",
  "parameters": {"\u53C2\u6570\u540D": "\u53C2\u6570\u503C"},
  "answer": "\u6700\u7EC8\u7B54\u6848\uFF08\u4EC5final_answer\u65F6\u9700\u8981\uFF09",
  "question": "\u9700\u8981\u8BE2\u95EE\u7684\u95EE\u9898\uFF08\u4EC5need_more_info\u65F6\u9700\u8981\uFF09"
}`}],o=await this.llm.generateText(n);try{return JSON.parse(o.content)}catch(i){return{type:"final_answer",description:"\u89E3\u6790\u884C\u52A8\u8BA1\u5212\u5931\u8D25\uFF0C\u63D0\u4F9B\u57FA\u4E8E\u601D\u8003\u7684\u7B54\u6848",answer:s}}}async executeTool(s,e){try{return await this.toolRegistry.execute(s,e)}catch(t){return{success:!1,error:`\u5DE5\u5177\u6267\u884C\u5931\u8D25: ${t.message}`}}}async shouldStop(s,e){let t=e.slice(-3);if(t.some(n=>n.type==="final_answer"))return!0;let r=t.filter(n=>n.type==="tool_result");return!!(r.length>=2&&r.every(n=>{var o;return!((o=n.toolResult)!=null&&o.success)}))}async generateFinalAnswer(s,e){let r=[{role:"user",content:this.promptManager.generateContentSummaryPrompt(s,e.map(o=>({source:o.type,content:o.content,score:1})))}];return(await this.llm.generateText(r)).content}parseTaskAnalysis(s){let e=s.toLowerCase(),r=["\u65E0\u6CD5","\u4E0D\u80FD","\u65E0\u6548","\u4E0D\u652F\u6301","\u65E0\u6743\u9650"].some(a=>e.includes(a)),o=["\u641C\u7D22","\u67E5\u627E","\u67E5\u8BE2","\u8BA1\u7B97","\u6267\u884C","\u8FD0\u884C","\u5206\u6790","\u7B14\u8BB0","\u6587\u6863","\u7F51\u7EDC","\u4EE3\u7801","\u63D2\u4EF6","\u547D\u4EE4"].some(a=>e.includes(a));return{analysis:s,executable:!r||o,complexity:this.estimateComplexity(s),requiredTools:this.extractRequiredTools(s)}}estimateComplexity(s){let t=["\u591A\u6B65\u9AA4","\u590D\u6742","\u9700\u8981","\u5206\u6790","\u5904\u7406","\u751F\u6210","\u641C\u7D22","\u8BA1\u7B97"].filter(r=>s.includes(r)).length;return t<=2?"low":t<=4?"medium":"high"}extractRequiredTools(s){let e=this.toolRegistry.list(),t=[];for(let r of e)(s.toLowerCase().includes(r.name.toLowerCase())||s.toLowerCase().includes(r.description.toLowerCase()))&&t.push(r.name);return t}};var X=class{constructor(s,e){this.currentConversationId=null;this.conversationState="idle";this.contextWindow=10;this.shortTermMemory=s,this.longTermMemory=e}async startConversation(s){let e=await this.shortTermMemory.execute({action:"create",metadata:{title:(s==null?void 0:s.title)||`\u5BF9\u8BDD ${new Date().toLocaleString()}`,tags:(s==null?void 0:s.tags)||[],summary:s==null?void 0:s.summary}});if(e.success)return this.currentConversationId=e.data.conversationId,this.conversationState="active",this.currentConversationId;throw new Error("\u521B\u5EFA\u5BF9\u8BDD\u5931\u8D25")}async addUserMessage(s,e){this.currentConversationId||await this.startConversation();let t={id:this.generateMessageId(),role:"user",content:s,timestamp:new Date,metadata:e||{}};await this.shortTermMemory.execute({action:"add_message",conversationId:this.currentConversationId,message:t}),this.conversationState="processing"}async addAssistantMessage(s,e,t){if(!this.currentConversationId)throw new Error("\u6CA1\u6709\u6D3B\u52A8\u7684\u5BF9\u8BDD");let r={id:this.generateMessageId(),role:"assistant",content:s,timestamp:new Date,metadata:{...t,executionLog:e==null?void 0:e.map(n=>({type:n.type,content:n.content,timestamp:n.timestamp,toolName:n.toolName}))}};await this.shortTermMemory.execute({action:"add_message",conversationId:this.currentConversationId,message:r}),this.conversationState="active",await this.updateConversationMetadata(e)}async getConversationContext(s){if(!this.currentConversationId)return null;let e=await this.shortTermMemory.execute({action:"get",conversationId:this.currentConversationId});if(e.success){let t=e.data.conversation,r=s||this.contextWindow;return t.messages.length>r&&(t.messages=t.messages.slice(-r)),t}return null}async getRecentMessages(s=5){let e=await this.getConversationContext(s);return(e==null?void 0:e.messages)||[]}async buildLLMContext(s=!0){let e=await this.getConversationContext();if(!e)return{messages:[]};let r={messages:e.messages.map(n=>({role:n.role,content:n.content}))};return s&&(r.systemPrompt=this.buildSystemPrompt(e)),r}setConversationState(s){this.conversationState=s}getConversationState(){return this.conversationState}async endConversation(s){this.currentConversationId&&(s&&await this.shortTermMemory.execute({action:"update",conversationId:this.currentConversationId,metadata:{summary:s}}),await this.saveImportantInformation(),this.conversationState="ended",this.currentConversationId=null)}async searchConversationHistory(s,e=5){return this.shortTermMemory.searchConversations(s,e)}async getConversationStats(){let s=this.shortTermMemory.getStats(),e=await this.getConversationContext();return{totalConversations:s.totalConversations,totalMessages:s.totalMessages,currentConversationLength:(e==null?void 0:e.messages.length)||0,averageMessagesPerConversation:s.averageMessagesPerConversation,conversationState:this.conversationState}}async cleanupOldConversations(s=30){return 0}async updateConversationMetadata(s){if(!this.currentConversationId||!s)return;let e=s.filter(t=>t.toolName).map(t=>t.toolName).filter((t,r,n)=>n.indexOf(t)===r);await this.shortTermMemory.execute({action:"update",conversationId:this.currentConversationId,metadata:{toolsUsed:e,lastActivity:new Date().toISOString()}})}buildSystemPrompt(s){let e=s.metadata.toolsUsed||[],t=s.messages.length,r="\u4F60\u662F\u4E00\u4E2A\u667A\u80FD\u7684Obsidian\u52A9\u624B\u3002";return e.length>0&&(r+=`

\u5728\u8FD9\u6B21\u5BF9\u8BDD\u4E2D\uFF0C\u4F60\u5DF2\u7ECF\u4F7F\u7528\u4E86\u4EE5\u4E0B\u5DE5\u5177\uFF1A${e.join(", ")}\u3002`),t>5&&(r+=`

\u8FD9\u662F\u4E00\u4E2A\u8F83\u957F\u7684\u5BF9\u8BDD\uFF0C\u8BF7\u4FDD\u6301\u4E0A\u4E0B\u6587\u7684\u8FDE\u8D2F\u6027\u3002`),r}async saveImportantInformation(){let s=await this.getConversationContext();if(!s||s.messages.length<3)return;let e=s.messages.filter(t=>t.content.length>50&&(t.content.includes("\u91CD\u8981")||t.content.includes("\u8BB0\u4F4F")||t.content.includes("\u504F\u597D")));for(let t of e)try{await this.longTermMemory.execute({action:"save_memory",content:t.content,tags:["conversation","important"]})}catch(r){console.warn("\u4FDD\u5B58\u91CD\u8981\u4FE1\u606F\u5931\u8D25:",r)}}generateMessageId(){return`msg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}getCurrentConversationId(){return this.currentConversationId}setContextWindow(s){this.contextWindow=Math.max(1,Math.min(50,s))}};var Z=class{constructor(s){this.executionHistory=[];this.maxHistorySize=100;this.toolRegistry=s}async coordinateExecution(s,e){let t=Date.now(),r=[],n=[],o=[];try{let i=this.planExecution(s);r.push({type:"action",content:`\u89C4\u5212\u6267\u884C ${s.length} \u4E2A\u5DE5\u5177\u8C03\u7528`,timestamp:new Date});for(let a of i){let c=await this.executePhase(a,e,r);n.push(...c),e&&(e.previousResults=c)}return s.forEach(a=>{o.includes(a.toolName)||o.push(a.toolName)}),this.recordExecution({toolCalls:s,results:n,executionTime:Date.now()-t,success:n.every(a=>a.success),timestamp:new Date}),{success:!0,results:n,executionLog:r,toolsUsed:o,executionTime:Date.now()-t,coordination:{totalPhases:i.length,parallelExecutions:i.reduce((a,c)=>a+c.length,0)}}}catch(i){return r.push({type:"error",content:`\u5DE5\u5177\u534F\u8C03\u6267\u884C\u5931\u8D25: ${i.message}`,timestamp:new Date}),{success:!1,results:n,executionLog:r,toolsUsed:o,executionTime:Date.now()-t,error:i.message}}}planExecution(s){let e=[],t=new Set,r=[...s];for(;r.length>0;){let n=[];for(let o=r.length-1;o>=0;o--){let i=r[o];this.canExecuteNow(i,t)&&(n.push(i),r.splice(o,1),t.add(i.id))}if(n.length===0)throw new Error("\u68C0\u6D4B\u5230\u5FAA\u73AF\u4F9D\u8D56\u6216\u65E0\u6CD5\u89E3\u51B3\u7684\u5DE5\u5177\u4F9D\u8D56\u5173\u7CFB");e.push(n)}return e}canExecuteNow(s,e){return!s.dependencies||s.dependencies.length===0?!0:s.dependencies.every(t=>e.has(t))}async executePhase(s,e,t){let r=[],n=s.map(async i=>{try{t==null||t.push({type:"action",content:`\u6267\u884C\u5DE5\u5177: ${i.toolName}`,timestamp:new Date,toolName:i.toolName});let a=this.processArguments(i.arguments,e),c=await this.toolRegistry.execute(i.toolName,a,e);return t==null||t.push({type:"tool_result",content:`\u5DE5\u5177 ${i.toolName} \u6267\u884C${c.success?"\u6210\u529F":"\u5931\u8D25"}`,timestamp:new Date,toolName:i.toolName,toolResult:c}),c}catch(a){let c={success:!1,error:`\u5DE5\u5177 ${i.toolName} \u6267\u884C\u5931\u8D25: ${a.message}`};return t==null||t.push({type:"error",content:c.error,timestamp:new Date,toolName:i.toolName}),c}}),o=await Promise.all(n);return r.push(...o),r}processArguments(s,e){if(!(e!=null&&e.previousResults)||!s)return s;let t=JSON.parse(JSON.stringify(s));return this.resolveReferences(t,e.previousResults),t}resolveReferences(s,e){if(typeof s=="string"&&s.startsWith("$ref:")){let t=s.substring(5),[r,...n]=t.split("."),o=e.find(i=>{var a;return((a=i.metadata)==null?void 0:a.toolName)===r});if(o&&o.data){let i=o.data;for(let a of n)if(i=i[a],i===void 0)break;return i}}else if(typeof s=="object"&&s!==null){for(let t in s)if(s.hasOwnProperty(t)){let r=this.resolveReferences(s[t],e);r!==void 0&&(s[t]=r)}}}recordExecution(s){this.executionHistory.push(s),this.executionHistory.length>this.maxHistorySize&&(this.executionHistory=this.executionHistory.slice(-this.maxHistorySize))}getExecutionStats(){var n;let s=this.executionHistory.length,e=this.executionHistory.filter(o=>o.success).length,t=s>0?this.executionHistory.reduce((o,i)=>o+i.executionTime,0)/s:0,r={};return this.executionHistory.forEach(o=>{o.toolCalls.forEach(i=>{r[i.toolName]=(r[i.toolName]||0)+1})}),{totalExecutions:s,successfulExecutions:e,failedExecutions:s-e,averageExecutionTime:Math.round(t),toolUsage:r,mostUsedTool:((n=Object.entries(r).sort(([,o],[,i])=>i-o)[0])==null?void 0:n[0])||"none"}}clearExecutionHistory(){this.executionHistory=[]}};var v=class{constructor(s){l(this,"name");l(this,"description");l(this,"category");l(this,"version");l(this,"parameters");l(this,"permissions");l(this,"metadata");l(this,"initialized",!1);l(this,"executionCount",0);l(this,"lastExecutionTime",null);this.name=s.name,this.description=s.description,this.category=s.category,this.version=s.version||"1.0.0",this.parameters=s.parameters,this.permissions=s.permissions,this.metadata=s.metadata}async execute(s,e){let t=Date.now(),r=(e==null?void 0:e.requestId)||this.generateRequestId();try{let n=await this.validate(s);if(!n.valid)return{success:!1,error:`\u53C2\u6570\u9A8C\u8BC1\u5931\u8D25: ${n.errors.join(", ")}`,metadata:{executionTime:Date.now()-t,requestId:r}};if(e){let a=this.checkPermissions(e);if(!a.allowed)return{success:!1,error:`\u6743\u9650\u4E0D\u8DB3: ${a.reason}`,metadata:{executionTime:Date.now()-t,requestId:r}}}this.initialized||(await this.initialize(),this.initialized=!0);let o=await this.executeInternal(s,e);this.executionCount++,this.lastExecutionTime=new Date;let i={executionTime:Date.now()-t,requestId:r,...o.metadata};return{...o,metadata:i}}catch(n){return{success:!1,error:n instanceof Error?n.message:String(n),metadata:{executionTime:Date.now()-t,requestId:r}}}}async validate(s){let e=[],t=[];if(this.parameters.required)for(let n of this.parameters.required)n in s||e.push(`\u7F3A\u5C11\u5FC5\u9700\u53C2\u6570: ${n}`);for(let[n,o]of Object.entries(this.parameters.properties))if(n in s){let i=s[n],a=this.validateParameterType(i,o);a.valid||e.push(`\u53C2\u6570 ${n} ${a.error}`)}let r=await this.validateInternal(s);return e.push(...r.errors),t.push(...r.warnings),{valid:e.length===0,errors:e,warnings:t}}async initialize(){}async cleanup(){this.initialized=!1}getStats(){return{executionCount:this.executionCount,lastExecutionTime:this.lastExecutionTime,initialized:this.initialized}}checkPermissions(s){for(let e of this.permissions.required)if(!s.permissions.includes(e))return{allowed:!1,reason:`\u7F3A\u5C11\u5FC5\u9700\u6743\u9650: ${e}`};return this.permissions.dangerous&&!s.permissions.includes("dangerous_operations")?{allowed:!1,reason:"\u6B64\u5DE5\u5177\u6267\u884C\u5371\u9669\u64CD\u4F5C\uFF0C\u9700\u8981\u7279\u6B8A\u6743\u9650"}:{allowed:!0}}validateParameterType(s,e){switch(e.type){case"string":if(typeof s!="string")return{valid:!1,error:"\u7C7B\u578B\u9519\u8BEF\uFF0C\u671F\u671B\u5B57\u7B26\u4E32"};if(e.pattern&&!new RegExp(e.pattern).test(s))return{valid:!1,error:"\u683C\u5F0F\u4E0D\u5339\u914D"};break;case"number":if(typeof s!="number")return{valid:!1,error:"\u7C7B\u578B\u9519\u8BEF\uFF0C\u671F\u671B\u6570\u5B57"};if(e.minimum!==void 0&&s<e.minimum)return{valid:!1,error:`\u503C\u8FC7\u5C0F\uFF0C\u6700\u5C0F\u503C\u4E3A ${e.minimum}`};if(e.maximum!==void 0&&s>e.maximum)return{valid:!1,error:`\u503C\u8FC7\u5927\uFF0C\u6700\u5927\u503C\u4E3A ${e.maximum}`};break;case"boolean":if(typeof s!="boolean")return{valid:!1,error:"\u7C7B\u578B\u9519\u8BEF\uFF0C\u671F\u671B\u5E03\u5C14\u503C"};break;case"array":if(!Array.isArray(s))return{valid:!1,error:"\u7C7B\u578B\u9519\u8BEF\uFF0C\u671F\u671B\u6570\u7EC4"};break;case"object":if(typeof s!="object"||s===null||Array.isArray(s))return{valid:!1,error:"\u7C7B\u578B\u9519\u8BEF\uFF0C\u671F\u671B\u5BF9\u8C61"};break}return e.enum&&!e.enum.includes(s)?{valid:!1,error:`\u503C\u4E0D\u5728\u5141\u8BB8\u8303\u56F4\u5185: ${e.enum.join(", ")}`}:{valid:!0}}generateRequestId(){return`${this.name}_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}async validateInternal(s){return{valid:!0,errors:[],warnings:[]}}};var _=class extends v{constructor(e){super({name:"short_term_memory",description:"\u7BA1\u7406\u77ED\u671F\u5BF9\u8BDD\u8BB0\u5FC6\u548C\u4F1A\u8BDD\u4E0A\u4E0B\u6587",category:"memory",version:"1.0.0",parameters:{type:"object",properties:{action:{type:"string",description:"\u64CD\u4F5C\u7C7B\u578B",enum:["create","get","update","delete","list","add_message","get_context"]},conversationId:{type:"string",description:"\u5BF9\u8BDDID"},message:{type:"object",description:"\u6D88\u606F\u5BF9\u8C61",properties:{role:{type:"string",enum:["user","assistant","system"]},content:{type:"string"},metadata:{type:"object"}}},metadata:{type:"object",description:"\u5BF9\u8BDD\u5143\u6570\u636E"},limit:{type:"number",description:"\u9650\u5236\u8FD4\u56DE\u6570\u91CF",default:10,minimum:1,maximum:100}},required:["action"]},permissions:{required:["memory_read","memory_write"],optional:[],dangerous:!1,requiresConfirmation:!1},metadata:{author:"AI Coach Team",tags:["memory","conversation","context"],documentation:"\u7BA1\u7406\u77ED\u671F\u5BF9\u8BDD\u8BB0\u5FC6\uFF0C\u7EF4\u62A4\u4F1A\u8BDD\u4E0A\u4E0B\u6587",examples:[{name:"\u521B\u5EFA\u65B0\u5BF9\u8BDD",description:"\u521B\u5EFA\u4E00\u4E2A\u65B0\u7684\u5BF9\u8BDD\u4F1A\u8BDD",input:{action:"create",metadata:{title:"New Chat"}},expectedOutput:{conversationId:"uuid"}},{name:"\u6DFB\u52A0\u6D88\u606F",description:"\u5411\u5BF9\u8BDD\u4E2D\u6DFB\u52A0\u65B0\u6D88\u606F",input:{action:"add_message",conversationId:"uuid",message:{role:"user",content:"Hello"}},expectedOutput:{success:!0}}]}});l(this,"app");l(this,"conversations",new Map);l(this,"maxConversations",100);l(this,"maxMessagesPerConversation",50);this.app=e}async executeInternal(e,t){let{action:r}=e;try{switch(r){case"create":return await this.createConversation(e);case"get":return await this.getConversation(e);case"update":return await this.updateConversation(e);case"delete":return await this.deleteConversation(e);case"list":return await this.listConversations(e);case"add_message":return await this.addMessage(e);case"get_context":return await this.getContext(e);default:return{success:!1,error:`\u672A\u77E5\u64CD\u4F5C: ${r}`}}}catch(n){return{success:!1,error:`\u77ED\u671F\u8BB0\u5FC6\u64CD\u4F5C\u5931\u8D25: ${n.message}`}}}async createConversation(e){let{metadata:t={}}=e,r=this.generateConversationId(),n=new Date,o={id:r,messages:[],metadata:{title:t.title||`\u5BF9\u8BDD ${n.toLocaleString()}`,tags:t.tags||[],summary:t.summary,toolsUsed:[],filesReferenced:[]},createdAt:n,updatedAt:n};return this.conversations.set(r,o),this.cleanupOldConversations(),{success:!0,data:{conversationId:r,conversation:o}}}async getConversation(e){let{conversationId:t}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u5BF9\u8BDDID"};let r=this.conversations.get(t);return r?{success:!0,data:{conversation:r}}:{success:!1,error:`\u5BF9\u8BDD\u4E0D\u5B58\u5728: ${t}`}}async updateConversation(e){let{conversationId:t,metadata:r}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u5BF9\u8BDDID"};let n=this.conversations.get(t);return n?(r&&(n.metadata={...n.metadata,...r},n.updatedAt=new Date),{success:!0,data:{conversation:n}}):{success:!1,error:`\u5BF9\u8BDD\u4E0D\u5B58\u5728: ${t}`}}async deleteConversation(e){let{conversationId:t}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u5BF9\u8BDDID"};let r=this.conversations.delete(t);return{success:r,data:{deleted:r}}}async listConversations(e){let{limit:t=10}=e;return{success:!0,data:{conversations:Array.from(this.conversations.values()).sort((n,o)=>o.updatedAt.getTime()-n.updatedAt.getTime()).slice(0,t),total:this.conversations.size}}}async addMessage(e){let{conversationId:t,message:r}=e;if(!t||!r)return{success:!1,error:"\u7F3A\u5C11\u5BF9\u8BDDID\u6216\u6D88\u606F\u5185\u5BB9"};let n=this.conversations.get(t);if(!n)return{success:!1,error:`\u5BF9\u8BDD\u4E0D\u5B58\u5728: ${t}`};let o={id:this.generateMessageId(),role:r.role,content:r.content,timestamp:new Date,metadata:r.metadata||{}};return n.messages.push(o),n.updatedAt=new Date,n.messages.length>this.maxMessagesPerConversation&&(n.messages=n.messages.slice(-this.maxMessagesPerConversation)),{success:!0,data:{messageId:o.id,conversation:n}}}async getContext(e){let{conversationId:t,limit:r=10}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u5BF9\u8BDDID"};let n=this.conversations.get(t);if(!n)return{success:!1,error:`\u5BF9\u8BDD\u4E0D\u5B58\u5728: ${t}`};let o=n.messages.slice(-r);return{success:!0,data:{conversationId:t,messages:o,metadata:n.metadata,messageCount:n.messages.length}}}generateConversationId(){return`conv_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}generateMessageId(){return`msg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}cleanupOldConversations(){if(this.conversations.size<=this.maxConversations)return;let e=Array.from(this.conversations.entries()).sort(([,r],[,n])=>r.updatedAt.getTime()-n.updatedAt.getTime());e.slice(0,e.length-this.maxConversations).forEach(([r])=>{this.conversations.delete(r)})}getStats(){let e=Array.from(this.conversations.values()),t=e.reduce((i,a)=>i+a.messages.length,0),r=e.map(i=>i.updatedAt),n=r.length>0?new Date(Math.min(...r.map(i=>i.getTime()))):null,o=r.length>0?new Date(Math.max(...r.map(i=>i.getTime()))):null;return{totalConversations:e.length,totalMessages:t,averageMessagesPerConversation:e.length>0?t/e.length:0,oldestConversation:n,newestConversation:o}}clearAll(){this.conversations.clear()}searchConversations(e,t=10){var o;let r=e.toLowerCase(),n=[];for(let i of this.conversations.values()){let a=0;(o=i.metadata.title)!=null&&o.toLowerCase().includes(r)&&(a+=10);for(let c of i.messages)c.content.toLowerCase().includes(r)&&(a+=1);i.metadata.tags.some(c=>c.toLowerCase().includes(r))&&(a+=5),a>0&&n.push({conversation:i,score:a})}return n.sort((i,a)=>a.score-i.score).slice(0,t).map(i=>i.conversation)}};var O=class extends v{constructor(e){super({name:"long_term_memory",description:"\u7BA1\u7406\u957F\u671F\u7528\u6237\u504F\u597D\u548C\u6301\u4E45\u5316\u8BB0\u5FC6\u5B58\u50A8",category:"memory",version:"1.0.0",parameters:{type:"object",properties:{action:{type:"string",description:"\u64CD\u4F5C\u7C7B\u578B",enum:["set","get","delete","list","save_memory","get_memory","search_memory"]},key:{type:"string",description:"\u504F\u597D\u8BBE\u7F6E\u7684\u952E\u540D"},value:{description:"\u504F\u597D\u8BBE\u7F6E\u7684\u503C"},type:{type:"string",description:"\u503C\u7684\u7C7B\u578B",enum:["string","number","boolean","object","array"]},description:{type:"string",description:"\u504F\u597D\u8BBE\u7F6E\u7684\u63CF\u8FF0"},memoryId:{type:"string",description:"\u8BB0\u5FC6\u6761\u76EE\u7684ID"},content:{type:"string",description:"\u8BB0\u5FC6\u5185\u5BB9"},tags:{type:"array",description:"\u8BB0\u5FC6\u6807\u7B7E",items:{type:"string"}},query:{type:"string",description:"\u641C\u7D22\u67E5\u8BE2"}},required:["action"]},permissions:{required:["vault_write","vault_read"],optional:[],dangerous:!1,requiresConfirmation:!1},metadata:{author:"AI Coach Team",tags:["memory","preferences","storage"],documentation:"\u7BA1\u7406\u957F\u671F\u7528\u6237\u504F\u597D\u548C\u8BB0\u5FC6\u5B58\u50A8",examples:[{name:"\u8BBE\u7F6E\u7528\u6237\u504F\u597D",description:"\u4FDD\u5B58\u7528\u6237\u7684\u504F\u597D\u8BBE\u7F6E",input:{action:"set",key:"preferred_language",value:"zh",type:"string",description:"\u7528\u6237\u9996\u9009\u8BED\u8A00"},expectedOutput:{success:!0}},{name:"\u4FDD\u5B58\u8BB0\u5FC6",description:"\u4FDD\u5B58\u91CD\u8981\u7684\u8BB0\u5FC6\u4FE1\u606F",input:{action:"save_memory",content:"\u7528\u6237\u559C\u6B22\u4F7F\u7528Markdown\u683C\u5F0F",tags:["preference","format"]},expectedOutput:{memoryId:"uuid"}}]}});l(this,"app");l(this,"preferencesFile",".ai-coach/preferences.json");l(this,"memoryFolder",".ai-coach/memory");l(this,"preferences",new Map);l(this,"initialized",!1);this.app=e}async initialize(){if(!this.initialized)try{await this.ensureDirectories(),await this.loadPreferences(),this.initialized=!0,console.log("LongTermMemoryTool initialized successfully")}catch(e){throw console.error("Failed to initialize LongTermMemoryTool:",e),e}}async executeInternal(e,t){try{this.initialized||await this.initialize();let{action:r}=e;switch(r){case"set":return await this.setPreference(e);case"get":return await this.getPreference(e);case"delete":return await this.deletePreference(e);case"list":return await this.listPreferences(e);case"save_memory":return await this.saveMemory(e);case"get_memory":return await this.getMemory(e);case"search_memory":return await this.searchMemory(e);default:return{success:!1,error:`\u672A\u77E5\u64CD\u4F5C: ${r}`}}}catch(r){return{success:!1,error:`\u957F\u671F\u8BB0\u5FC6\u64CD\u4F5C\u5931\u8D25: ${r.message}`}}}async setPreference(e){let{key:t,value:r,type:n,description:o}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u504F\u597D\u952E\u540D"};let i={key:t,value:r,type:n||typeof r,description:o,updatedAt:new Date};return this.preferences.set(t,i),await this.savePreferences(),{success:!0,data:{preference:i}}}async getPreference(e){let{key:t}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u504F\u597D\u952E\u540D"};let r=this.preferences.get(t);return{success:!0,data:{preference:r,exists:!!r}}}async deletePreference(e){let{key:t}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u504F\u597D\u952E\u540D"};let r=this.preferences.delete(t);return r&&await this.savePreferences(),{success:!0,data:{deleted:r}}}async listPreferences(){let e=Array.from(this.preferences.values());return{success:!0,data:{preferences:e,count:e.length}}}async saveMemory(e){let{content:t,tags:r=[],memoryId:n}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u8BB0\u5FC6\u5185\u5BB9"};let o=n||this.generateMemoryId(),i=`${this.memoryFolder}/${o}.md`,a=this.formatMemoryContent(t,r);try{return await this.app.vault.adapter.write(i,a),{success:!0,data:{memoryId:o,fileName:i}}}catch(c){return{success:!1,error:`\u4FDD\u5B58\u8BB0\u5FC6\u5931\u8D25: ${c.message}`}}}async getMemory(e){let{memoryId:t}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u8BB0\u5FC6ID"};let r=`${this.memoryFolder}/${t}.md`;try{let n=await this.app.vault.adapter.read(r),o=this.parseMemoryContent(n);return{success:!0,data:{memoryId:t,...o}}}catch(n){return{success:!1,error:`\u83B7\u53D6\u8BB0\u5FC6\u5931\u8D25: ${n.message}`}}}async searchMemory(e){let{query:t}=e;if(!t)return{success:!1,error:"\u7F3A\u5C11\u641C\u7D22\u67E5\u8BE2"};try{let r=await this.getMemoryFiles(),n=[];for(let o of r){let i=await this.app.vault.adapter.read(o);if(i.toLowerCase().includes(t.toLowerCase())){let a=this.parseMemoryContent(i),c=o.replace(`${this.memoryFolder}/`,"").replace(".md","");n.push({memoryId:c,...a,fileName:o})}}return{success:!0,data:{query:t,results:n,count:n.length}}}catch(r){return{success:!1,error:`\u641C\u7D22\u8BB0\u5FC6\u5931\u8D25: ${r.message}`}}}async ensureDirectories(){let e=this.app.vault.adapter;await e.exists(".ai-coach")||await e.mkdir(".ai-coach"),await e.exists(this.memoryFolder)||await e.mkdir(this.memoryFolder)}async loadPreferences(){try{let e=this.app.vault.adapter;if(await e.exists(this.preferencesFile)){let t=await e.read(this.preferencesFile),r=JSON.parse(t);for(let[n,o]of Object.entries(r))this.preferences.set(n,o)}}catch(e){console.warn("Failed to load preferences:",e)}}async savePreferences(){try{let e={};for(let[r,n]of this.preferences.entries())e[r]=n;let t=JSON.stringify(e,null,2);await this.app.vault.adapter.write(this.preferencesFile,t)}catch(e){throw console.error("Failed to save preferences:",e),e}}formatMemoryContent(e,t){let r=new Date().toISOString(),n=t.map(o=>`#${o}`).join(" ");return`---
created: ${r}
tags: [${t.map(o=>`"${o}"`).join(", ")}]
type: memory
---

# AI Coach Memory

${n}

${e}

---
*Created by AI Coach Advanced on ${new Date().toLocaleString()}*`}parseMemoryContent(e){let t=e.match(/^---\n([\s\S]*?)\n---/),r=[],n="",o=e;if(t){let i=t[1],a=i.match(/tags:\s*\[(.*?)\]/),c=i.match(/created:\s*(.+)/);a&&(r=a[1].split(",").map(u=>u.trim().replace(/"/g,""))),c&&(n=c[1].trim()),o=e.replace(t[0],"").trim()}return{content:o,tags:r,created:n}}async getMemoryFiles(){try{return(await this.app.vault.adapter.list(this.memoryFolder)).files.filter(r=>r.endsWith(".md"))}catch(e){return console.error("Failed to get memory files:",e),[]}}generateMemoryId(){return`memory_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}async getStats(){let e=await this.getMemoryFiles(),t=0;for(let r of e)try{let n=await this.app.vault.adapter.stat(r);t+=(n==null?void 0:n.size)||0}catch(n){}return{preferencesCount:this.preferences.size,memoriesCount:e.length,totalSize:t}}async cleanupOldMemories(e=90){let t=await this.getMemoryFiles(),r=new Date;r.setDate(r.getDate()-e);let n=0;for(let o of t)try{let i=await this.app.vault.adapter.stat(o);i&&i.mtime<r.getTime()&&(await this.app.vault.adapter.remove(o),n++)}catch(i){console.warn(`Failed to process file ${o}:`,i)}return n}};var ee=class{constructor(s,e){l(this,"app");l(this,"configManager");l(this,"llm",null);l(this,"promptManager");l(this,"toolRegistry");l(this,"toolExecutor");l(this,"toolCoordinator");l(this,"taskPlanner",null);l(this,"conversationManager");l(this,"shortTermMemory");l(this,"longTermMemory");l(this,"initialized",!1);this.app=s,this.configManager=e,this.promptManager=new G,this.toolRegistry=new J,this.toolExecutor=new Y(this.toolRegistry),this.toolCoordinator=new Z(this.toolRegistry),this.shortTermMemory=new _(s),this.longTermMemory=new O(s),this.conversationManager=new X(this.shortTermMemory,this.longTermMemory)}async initialize(){if(!this.initialized)try{await this.initializeLLM(),this.llm&&(this.taskPlanner=new F(this.llm,this.promptManager,this.toolRegistry)),await this.shortTermMemory.initialize(),await this.longTermMemory.initialize(),this.promptManager.setAvailableTools(this.toolRegistry.list()),this.initialized=!0,console.log("OrchestrationEngine initialized successfully")}catch(s){throw console.error("Failed to initialize OrchestrationEngine:",s),s}}async processUserInput(s,e){if(this.initialized||await this.initialize(),!this.taskPlanner)throw new Error("Task planner not initialized");let t=Date.now();try{await this.conversationManager.addUserMessage(s,e==null?void 0:e.messageMetadata);let r={conversationHistory:await this.buildConversationHistory(),userPreferences:e==null?void 0:e.userPreferences,availableTools:this.toolRegistry.list().map(o=>o.name)},n=await this.taskPlanner.planAndExecute(s,r);return await this.conversationManager.addAssistantMessage(n.result,n.executionLog,{toolsUsed:n.toolsUsed,iterations:n.iterations}),{success:n.success,response:n.result,executionLog:n.executionLog,processingTime:Date.now()-t,toolsUsed:n.toolsUsed||[],conversationId:this.conversationManager.getCurrentConversationId(),metadata:{iterations:n.iterations,llmCalls:this.countLLMCalls(n.executionLog),complexity:this.assessComplexity(n.executionLog)}}}catch(r){let n=`\u5904\u7406\u8BF7\u6C42\u65F6\u53D1\u751F\u9519\u8BEF: ${r.message}`;return await this.conversationManager.addAssistantMessage(n,[{type:"error",content:r.message,timestamp:new Date}]),{success:!1,response:n,executionLog:[],processingTime:Date.now()-t,toolsUsed:[],conversationId:this.conversationManager.getCurrentConversationId(),error:r.message}}}async startNewConversation(s){return await this.conversationManager.startConversation({title:s})}async endCurrentConversation(s){await this.conversationManager.endConversation(s)}getConversationState(){return this.conversationManager.getConversationState()}async registerTool(s){await this.toolRegistry.register(s),this.promptManager.setAvailableTools(this.toolRegistry.list())}getAvailableTools(){return this.toolRegistry.list()}async getExecutionStats(){var r;let s=await this.conversationManager.getConversationStats(),e=this.toolRegistry.getStats(),t=this.toolCoordinator.getExecutionStats();return{conversations:s,tools:e,coordination:t,llm:{provider:((r=this.llm)==null?void 0:r.constructor.name)||"Unknown",initialized:!!this.llm}}}async updateConfig(s){await this.configManager.updateLLMConfig(s),await this.initializeLLM(),this.llm&&(this.taskPlanner=new F(this.llm,this.promptManager,this.toolRegistry))}async initializeLLM(){let s=this.configManager.getLLMConfig();this.llm=await E.createLLM(s)}async buildConversationHistory(){return(await this.conversationManager.getRecentMessages(5)).map(e=>`${e.role}: ${e.content}`).join(`
`)}countLLMCalls(s){return s.filter(e=>e.type==="thought"||e.type==="action"||e.type==="final_answer").length}assessComplexity(s){let e=s.filter(r=>r.type==="tool_result").length,t=s.filter(r=>r.type==="thought").length;return e===0&&t<=1?"low":e<=2&&t<=3?"medium":"high"}async cleanup(){await this.conversationManager.endConversation(),this.initialized=!1}};var k=require("obsidian");var te=class extends k.Modal{constructor(e,t){super(e);l(this,"orchestrationEngine");l(this,"inputArea");l(this,"outputArea");l(this,"sendButton");l(this,"isProcessing",!1);this.orchestrationEngine=t}onOpen(){let{contentEl:e}=this;e.empty(),e.createEl("h2",{text:"AI Coach Advanced"}),this.createChatArea(e),this.createInputArea(e),this.createControlButtons(e),setTimeout(()=>{this.inputArea.inputEl.focus()},100)}onClose(){let{contentEl:e}=this;e.empty()}createChatArea(e){let t=e.createDiv({cls:"ai-coach-chat-container"});this.outputArea=t.createDiv({cls:"ai-coach-chat-output",attr:{style:"height: 400px; overflow-y: auto; border: 1px solid var(--background-modifier-border); padding: 10px; margin-bottom: 10px; background: var(--background-primary);"}}),this.addMessage("assistant","\u4F60\u597D\uFF01\u6211\u662FAI Coach Advanced\uFF0C\u4F60\u7684\u667A\u80FD\u52A9\u624B\u3002\u6709\u4EC0\u4E48\u53EF\u4EE5\u5E2E\u52A9\u4F60\u7684\u5417\uFF1F")}createInputArea(e){let t=e.createDiv({cls:"ai-coach-input-container"});new k.Setting(t).setName("\u8F93\u5165\u6D88\u606F").setDesc("\u8F93\u5165\u4F60\u7684\u95EE\u9898\u6216\u6307\u4EE4").addTextArea(r=>{this.inputArea=r,r.inputEl.style.width="100%",r.inputEl.style.minHeight="80px",r.inputEl.placeholder="\u8BF7\u8F93\u5165\u4F60\u7684\u95EE\u9898...",r.inputEl.addEventListener("keydown",n=>{n.key==="Enter"&&(n.ctrlKey||n.metaKey)&&(n.preventDefault(),this.sendMessage())})})}createControlButtons(e){let t=e.createDiv({cls:"ai-coach-button-container",attr:{style:"display: flex; gap: 10px; justify-content: flex-end; margin-top: 10px;"}});new k.Setting(t).addButton(r=>{this.sendButton=r,r.setButtonText("\u53D1\u9001 (Ctrl+Enter)").setCta().onClick(()=>this.sendMessage())}),new k.Setting(t).addButton(r=>{r.setButtonText("\u6E05\u7A7A\u5BF9\u8BDD").onClick(()=>this.clearConversation())}),new k.Setting(t).addButton(r=>{r.setButtonText("\u65B0\u5BF9\u8BDD").onClick(()=>this.startNewConversation())})}async sendMessage(){let e=this.inputArea.getValue().trim();if(!(!e||this.isProcessing)){this.isProcessing=!0,this.updateSendButton();try{this.addMessage("user",e),this.inputArea.setValue("");let t=this.addMessage("assistant","\u6B63\u5728\u601D\u8003\u4E2D..."),r=await this.orchestrationEngine.processUserInput(e);t&&t.remove(),r.success?(this.addMessage("assistant",r.response),r.executionLog&&r.executionLog.length>0&&this.addExecutionDetails(r)):this.addMessage("assistant",`\u62B1\u6B49\uFF0C\u5904\u7406\u60A8\u7684\u8BF7\u6C42\u65F6\u51FA\u73B0\u4E86\u95EE\u9898\uFF1A${r.error||"\u672A\u77E5\u9519\u8BEF"}`)}catch(t){console.error("Error processing message:",t),this.addMessage("assistant",`\u5904\u7406\u6D88\u606F\u65F6\u53D1\u751F\u9519\u8BEF\uFF1A${t.message}`)}finally{this.isProcessing=!1,this.updateSendButton(),this.inputArea.inputEl.focus()}}}addMessage(e,t){let r=this.outputArea.createDiv({cls:`ai-coach-message ai-coach-message-${e}`,attr:{style:`margin-bottom: 15px; padding: 10px; border-radius: 8px; ${e==="user"?"background: var(--interactive-accent); color: var(--text-on-accent); margin-left: 20%; text-align: right;":"background: var(--background-secondary); margin-right: 20%;"}`}}),n=r.createDiv({cls:"ai-coach-message-role",text:e==="user"?"\u4F60":"AI\u52A9\u624B",attr:{style:"font-weight: bold; margin-bottom: 5px; font-size: 0.9em; opacity: 0.8;"}}),o=r.createDiv({cls:"ai-coach-message-content",attr:{style:"white-space: pre-wrap; line-height: 1.5;"}});o.textContent=t;let i=new Date,a=r.createDiv({cls:"ai-coach-message-time",text:i.toLocaleTimeString(),attr:{style:"font-size: 0.8em; opacity: 0.6; margin-top: 5px;"}});return this.outputArea.scrollTop=this.outputArea.scrollHeight,r}addExecutionDetails(e){var r;if(!e.executionLog||e.executionLog.length===0)return;let t=this.outputArea.createDiv({cls:"ai-coach-execution-details",attr:{style:"margin-bottom: 15px; padding: 10px; background: var(--background-modifier-border); border-radius: 8px; font-size: 0.9em;"}});t.createEl("div",{text:"\u6267\u884C\u8BE6\u60C5\uFF1A",attr:{style:"font-weight: bold; margin-bottom: 5px;"}}),e.toolsUsed&&e.toolsUsed.length>0&&t.createEl("div",{text:`\u4F7F\u7528\u5DE5\u5177\uFF1A${e.toolsUsed.join(", ")}`,attr:{style:"margin-bottom: 3px;"}}),e.processingTime&&t.createEl("div",{text:`\u5904\u7406\u65F6\u95F4\uFF1A${e.processingTime}ms`,attr:{style:"margin-bottom: 3px;"}}),(r=e.metadata)!=null&&r.complexity&&t.createEl("div",{text:`\u4EFB\u52A1\u590D\u6742\u5EA6\uFF1A${e.metadata.complexity}`,attr:{style:"margin-bottom: 3px;"}})}updateSendButton(){this.sendButton&&(this.sendButton.setButtonText(this.isProcessing?"\u5904\u7406\u4E2D...":"\u53D1\u9001 (Ctrl+Enter)"),this.sendButton.setDisabled(this.isProcessing))}clearConversation(){this.outputArea.empty(),this.addMessage("assistant","\u5BF9\u8BDD\u5DF2\u6E05\u7A7A\u3002\u6709\u4EC0\u4E48\u65B0\u7684\u95EE\u9898\u5417\uFF1F")}async startNewConversation(){try{await this.orchestrationEngine.startNewConversation(),this.clearConversation(),this.addMessage("assistant","\u5DF2\u5F00\u59CB\u65B0\u5BF9\u8BDD\u3002\u6709\u4EC0\u4E48\u53EF\u4EE5\u5E2E\u52A9\u4F60\u7684\u5417\uFF1F")}catch(e){console.error("Failed to start new conversation:",e),this.addMessage("assistant",`\u5F00\u59CB\u65B0\u5BF9\u8BDD\u5931\u8D25\uFF1A${e.message}`)}}};var g=require("obsidian");var se=class extends g.PluginSettingTab{constructor(e,t){super(e,t);l(this,"plugin");this.plugin=t}display(){let{containerEl:e}=this;e.empty(),e.createEl("h1",{text:"AI Coach Advanced \u8BBE\u7F6E"}),this.addImportExportSection(e),this.addLLMSection(e),this.addToolsSection(e),this.addUISection(e),this.addSecuritySection(e),this.addAdvancedSection(e)}addImportExportSection(e){e.createEl("h2",{text:"\u914D\u7F6E\u7BA1\u7406"});let t=e.createDiv({cls:"ai-coach-import-export"});new g.Setting(t).setName("\u5BFC\u51FA\u914D\u7F6E").setDesc("\u5BFC\u51FA\u5F53\u524D\u914D\u7F6E\u5230\u526A\u8D34\u677F\uFF08\u4E0D\u5305\u542B\u654F\u611F\u4FE1\u606F\uFF09").addButton(r=>r.setButtonText("\u5BFC\u51FA").onClick(async()=>{try{let o=this.plugin.configManager.exportConfig();await navigator.clipboard.writeText(o),new g.Notice("\u914D\u7F6E\u5DF2\u5BFC\u51FA\u5230\u526A\u8D34\u677F")}catch(n){new g.Notice("\u5BFC\u51FA\u5931\u8D25: "+n.message)}})),new g.Setting(t).setName("\u5BFC\u5165\u914D\u7F6E").setDesc("\u4ECE\u526A\u8D34\u677F\u5BFC\u5165\u914D\u7F6E").addButton(r=>r.setButtonText("\u5BFC\u5165").onClick(async()=>{try{let n=await navigator.clipboard.readText();await this.plugin.configManager.importConfig(n),this.display()}catch(n){new g.Notice("\u5BFC\u5165\u5931\u8D25: "+n.message)}})),new g.Setting(t).setName("\u91CD\u7F6E\u914D\u7F6E").setDesc("\u5C06\u6240\u6709\u8BBE\u7F6E\u91CD\u7F6E\u4E3A\u9ED8\u8BA4\u503C").addButton(r=>r.setButtonText("\u91CD\u7F6E").setWarning().onClick(async()=>{confirm("\u786E\u5B9A\u8981\u91CD\u7F6E\u6240\u6709\u914D\u7F6E\u5417\uFF1F\u6B64\u64CD\u4F5C\u4E0D\u53EF\u64A4\u9500\u3002")&&(await this.plugin.configManager.resetConfig(),this.display())}))}addLLMSection(e){e.createEl("h2",{text:"LLM\u914D\u7F6E"});let t=this.plugin.getConfig(),r=E.getSupportedProviders();new g.Setting(e).setName("LLM\u63D0\u4F9B\u5546").setDesc("\u9009\u62E9\u8981\u4F7F\u7528\u7684LLM\u670D\u52A1\u63D0\u4F9B\u5546").addDropdown(n=>{r.forEach(o=>{n.addOption(o.id,o.name)}),n.setValue(t.llm.provider).onChange(async o=>{await this.plugin.updateConfig({llm:{...t.llm,provider:o}}),this.display()})}),new g.Setting(e).setName("API\u5BC6\u94A5").setDesc("\u8F93\u5165LLM\u670D\u52A1\u7684API\u5BC6\u94A5").addText(n=>n.setPlaceholder("\u8F93\u5165API\u5BC6\u94A5").setValue(t.llm.apiKey).onChange(async o=>{let i=this.plugin.getConfig();await this.plugin.updateConfig({llm:{...i.llm,apiKey:o}})})),t.llm.provider==="custom"&&new g.Setting(e).setName("API\u57FA\u7840URL").setDesc("\u81EA\u5B9A\u4E49API\u7684\u57FA\u7840URL").addText(n=>n.setPlaceholder("https://api.example.com/v1").setValue(t.llm.baseUrl||"").onChange(async o=>{let i=this.plugin.getConfig();await this.plugin.updateConfig({llm:{...i.llm,baseUrl:o}})})),new g.Setting(e).setName("\u6A21\u578B").setDesc("\u9009\u62E9\u8981\u4F7F\u7528\u7684\u5177\u4F53\u6A21\u578B").addText(n=>n.setPlaceholder("\u4F8B\u5982: gpt-3.5-turbo").setValue(t.llm.model).onChange(async o=>{let i=this.plugin.getConfig();await this.plugin.updateConfig({llm:{...i.llm,model:o}})})),new g.Setting(e).setName("\u6700\u5927Token\u6570").setDesc("\u5355\u6B21\u8BF7\u6C42\u7684\u6700\u5927Token\u6570\u91CF").addSlider(n=>n.setLimits(100,8e3,100).setValue(t.llm.maxTokens).setDynamicTooltip().onChange(async o=>{let i=this.plugin.getConfig();await this.plugin.updateConfig({llm:{...i.llm,maxTokens:o}})})),new g.Setting(e).setName("\u6E29\u5EA6").setDesc("\u63A7\u5236\u56DE\u7B54\u7684\u968F\u673A\u6027\uFF0C0-2\u4E4B\u95F4\uFF0C\u503C\u8D8A\u9AD8\u8D8A\u968F\u673A").addSlider(n=>n.setLimits(0,2,.1).setValue(t.llm.temperature).setDynamicTooltip().onChange(async o=>{let i=this.plugin.getConfig();await this.plugin.updateConfig({llm:{...i.llm,temperature:o}})})),new g.Setting(e).setName("\u6D4B\u8BD5\u8FDE\u63A5").setDesc("\u6D4B\u8BD5LLM API\u8FDE\u63A5\u662F\u5426\u6B63\u5E38").addButton(n=>n.setButtonText("\u6D4B\u8BD5").onClick(async()=>{n.setButtonText("\u6D4B\u8BD5\u4E2D..."),n.setDisabled(!0);try{let o=await this.plugin.configManager.validateLLMConfig();if(!o.valid){new g.Notice("\u914D\u7F6E\u9A8C\u8BC1\u5931\u8D25: "+o.errors.join(", "));return}await new Promise(i=>setTimeout(i,1e3)),new g.Notice("\u8FDE\u63A5\u6D4B\u8BD5\u6210\u529F\uFF01")}catch(o){new g.Notice("\u8FDE\u63A5\u6D4B\u8BD5\u5931\u8D25: "+o.message)}finally{n.setButtonText("\u6D4B\u8BD5"),n.setDisabled(!1)}}))}addToolsSection(e){e.createEl("h2",{text:"\u5DE5\u5177\u914D\u7F6E"});let t=this.plugin.getConfig();e.createEl("h3",{text:"Vault\u77E5\u8BC6\u5E93"}),new g.Setting(e).setName("\u542F\u7528Vault\u67E5\u8BE2").setDesc("\u5141\u8BB8AI\u52A9\u624B\u641C\u7D22\u548C\u67E5\u8BE2\u4F60\u7684\u7B14\u8BB0\u5185\u5BB9").addToggle(r=>r.setValue(t.tools.vault.enabled).onChange(async n=>{await this.plugin.updateConfig({tools:{...t.tools,vault:{...t.tools.vault,enabled:n}}})})),new g.Setting(e).setName("\u542F\u7528\u81EA\u52A8\u7D22\u5F15").setDesc("\u81EA\u52A8\u4E3AVault\u5185\u5BB9\u521B\u5EFA\u641C\u7D22\u7D22\u5F15").addToggle(r=>r.setValue(t.tools.vault.indexingEnabled).onChange(async n=>{await this.plugin.updateConfig({tools:{...t.tools,vault:{...t.tools.vault,indexingEnabled:n}}})})),e.createEl("h3",{text:"\u7F51\u7EDC\u641C\u7D22"}),new g.Setting(e).setName("\u542F\u7528\u7F51\u7EDC\u641C\u7D22").setDesc("\u5141\u8BB8AI\u52A9\u624B\u8FDB\u884C\u7F51\u7EDC\u641C\u7D22\u83B7\u53D6\u6700\u65B0\u4FE1\u606F").addToggle(r=>r.setValue(t.tools.web.enabled).onChange(async n=>{await this.plugin.updateConfig({tools:{...t.tools,web:{...t.tools.web,enabled:n}}})})),e.createEl("h3",{text:"JavaScript\u6267\u884C"}),new g.Setting(e).setName("\u542F\u7528JS\u6267\u884C").setDesc("\u5141\u8BB8AI\u52A9\u624B\u751F\u6210\u548C\u6267\u884CJavaScript\u4EE3\u7801\uFF08\u9AD8\u98CE\u9669\u529F\u80FD\uFF09").addToggle(r=>r.setValue(t.tools.javascript.enabled).onChange(async n=>{await this.plugin.updateConfig({tools:{...t.tools,javascript:{...t.tools.javascript,enabled:n}}})}))}addUISection(e){e.createEl("h2",{text:"UI\u914D\u7F6E"});let t=this.plugin.getConfig();new g.Setting(e).setName("\u8BED\u8A00").setDesc("\u9009\u62E9\u754C\u9762\u8BED\u8A00").addDropdown(r=>r.addOption("zh","\u4E2D\u6587").addOption("en","English").setValue(t.ui.language).onChange(async n=>{await this.plugin.updateConfig({ui:{...t.ui,language:n}})})),new g.Setting(e).setName("\u663E\u793A\u8FDB\u5EA6").setDesc("\u5728\u6267\u884C\u4EFB\u52A1\u65F6\u663E\u793A\u8FDB\u5EA6\u63D0\u793A").addToggle(r=>r.setValue(t.ui.showProgress).onChange(async n=>{await this.plugin.updateConfig({ui:{...t.ui,showProgress:n}})}))}addSecuritySection(e){e.createEl("h2",{text:"\u5B89\u5168\u914D\u7F6E"});let t=this.plugin.getConfig();new g.Setting(e).setName("\u52A0\u5BC6API\u5BC6\u94A5").setDesc("\u5728\u672C\u5730\u5B58\u50A8\u4E2D\u52A0\u5BC6API\u5BC6\u94A5").addToggle(r=>r.setValue(t.security.encryptApiKeys).onChange(async n=>{await this.plugin.updateConfig({security:{...t.security,encryptApiKeys:n}})})),new g.Setting(e).setName("\u8BF7\u6C42\u9891\u7387\u9650\u5236").setDesc("\u6BCF\u5206\u949F\u6700\u5927\u8BF7\u6C42\u6570\u91CF").addSlider(r=>r.setLimits(10,300,10).setValue(t.security.maxRequestsPerMinute).setDynamicTooltip().onChange(async n=>{await this.plugin.updateConfig({security:{...t.security,maxRequestsPerMinute:n}})}))}addAdvancedSection(e){e.createEl("h2",{text:"\u9AD8\u7EA7\u914D\u7F6E"}),new g.Setting(e).setName("\u91CD\u5EFAVault\u7D22\u5F15").setDesc("\u91CD\u65B0\u4E3A\u6240\u6709\u7B14\u8BB0\u521B\u5EFA\u641C\u7D22\u7D22\u5F15").addButton(t=>t.setButtonText("\u91CD\u5EFA\u7D22\u5F15").onClick(async()=>{t.setButtonText("\u91CD\u5EFA\u4E2D..."),t.setDisabled(!0);try{await new Promise(r=>setTimeout(r,2e3)),new g.Notice("\u7D22\u5F15\u91CD\u5EFA\u5B8C\u6210")}catch(r){new g.Notice("\u7D22\u5F15\u91CD\u5EFA\u5931\u8D25: "+r.message)}finally{t.setButtonText("\u91CD\u5EFA\u7D22\u5F15"),t.setDisabled(!1)}})),new g.Setting(e).setName("\u6E05\u7406\u6570\u636E").setDesc("\u6E05\u7406\u6240\u6709\u5BF9\u8BDD\u5386\u53F2\u548C\u7F13\u5B58\u6570\u636E").addButton(t=>t.setButtonText("\u6E05\u7406").setWarning().onClick(async()=>{if(confirm("\u786E\u5B9A\u8981\u6E05\u7406\u6240\u6709\u6570\u636E\u5417\uFF1F\u6B64\u64CD\u4F5C\u4E0D\u53EF\u64A4\u9500\u3002"))try{await this.plugin.getMemoryManager().clearAllConversations(),new g.Notice("\u6570\u636E\u6E05\u7406\u5B8C\u6210")}catch(n){new g.Notice("\u6570\u636E\u6E05\u7406\u5931\u8D25: "+n.message)}}))}};var he=require("obsidian");var re=class{constructor(s,e){l(this,"statusBarItem");l(this,"orchestrationEngine");l(this,"updateInterval",null);l(this,"isActive",!1);this.statusBarItem=s,this.orchestrationEngine=e}initialize(){this.setupStatusBar(),this.startPeriodicUpdate()}setupStatusBar(){this.statusBarItem.addClass("ai-coach-status-bar"),this.statusBarItem.style&&(this.statusBarItem.style.cursor="pointer"),this.statusBarItem.addEventListener("click",()=>{this.showDetailedStatus()}),this.updateStatus("idle","AI Coach Ready")}updateStatus(s,e,t){this.statusBarItem.empty();let r=this.statusBarItem.createSpan({cls:"ai-coach-status-icon"});r.innerHTML=this.getStatusIcon(s);let n=this.statusBarItem.createSpan({cls:"ai-coach-status-text",text:e});if(t!==void 0){let i=this.statusBarItem.createDiv({cls:"ai-coach-progress-bar"}).createDiv({cls:"ai-coach-progress-fill",attr:{style:`width: ${Math.max(0,Math.min(100,t))}%`}})}this.statusBarItem.className=`ai-coach-status-bar ai-coach-status-${s}`}getStatusIcon(s){return{idle:"\u{1F916}",processing:"\u26A1",success:"\u2705",error:"\u274C",warning:"\u26A0\uFE0F"}[s]||"\u{1F916}"}showProcessing(s="\u5904\u7406\u4E2D...",e){this.isActive=!0,this.updateStatus("processing",s,e)}showSuccess(s="\u5B8C\u6210",e=3e3){this.updateStatus("success",s),this.isActive=!1,setTimeout(()=>{this.isActive||this.showIdle()},e)}showError(s="\u9519\u8BEF",e=5e3){this.updateStatus("error",s),this.isActive=!1,setTimeout(()=>{this.isActive||this.showIdle()},e)}showWarning(s="\u8B66\u544A",e=4e3){this.updateStatus("warning",s),setTimeout(()=>{this.isActive||this.showIdle()},e)}showIdle(){this.isActive=!1,this.updateStatus("idle","AI Coach Ready")}startPeriodicUpdate(){this.updateInterval=setInterval(async()=>{this.isActive||await this.updateIdleStatus()},3e4)}async updateIdleStatus(){try{let s=await this.orchestrationEngine.getExecutionStats(),e=this.orchestrationEngine.getConversationState(),t="AI Coach Ready";e==="active"?t=`\u5BF9\u8BDD\u4E2D (${s.conversations.currentConversationLength} \u6D88\u606F)`:s.conversations.totalConversations>0&&(t=`\u5C31\u7EEA (${s.conversations.totalConversations} \u5BF9\u8BDD)`),this.updateStatus("idle",t)}catch(s){console.warn("Failed to update idle status:",s)}}async showDetailedStatus(){var s;try{let e=await this.orchestrationEngine.getExecutionStats(),t=`
AI Coach Advanced \u72B6\u6001:

\u5BF9\u8BDD\u7EDF\u8BA1:
- \u603B\u5BF9\u8BDD\u6570: ${e.conversations.totalConversations}
- \u603B\u6D88\u606F\u6570: ${e.conversations.totalMessages}
- \u5F53\u524D\u5BF9\u8BDD\u957F\u5EA6: ${e.conversations.currentConversationLength}
- \u5BF9\u8BDD\u72B6\u6001: ${this.getConversationStateText(e.conversations.conversationState)}

\u5DE5\u5177\u7EDF\u8BA1:
- \u53EF\u7528\u5DE5\u5177\u6570: ${e.tools.totalTools||0}
- \u5DE5\u5177\u8C03\u7528\u6B21\u6570: ${((s=e.coordination)==null?void 0:s.totalExecutions)||0}
- \u6210\u529F\u7387: ${e.coordination?Math.round(e.coordination.successfulExecutions/e.coordination.totalExecutions*100):0}%

LLM\u72B6\u6001:
- \u63D0\u4F9B\u5546: ${e.llm.provider}
- \u72B6\u6001: ${e.llm.initialized?"\u5DF2\u8FDE\u63A5":"\u672A\u8FDE\u63A5"}
      `.trim(),r=new he.Notice(t,8e3)}catch(e){new he.Notice(`\u83B7\u53D6\u72B6\u6001\u4FE1\u606F\u5931\u8D25: ${e.message}`,3e3)}}getConversationStateText(s){return{idle:"\u7A7A\u95F2",active:"\u6D3B\u8DC3",processing:"\u5904\u7406\u4E2D",waiting:"\u7B49\u5F85\u4E2D",ended:"\u5DF2\u7ED3\u675F",error:"\u9519\u8BEF"}[s]||s}showToolProgress(s,e,t){let r=e/t*100,n=`\u6267\u884C ${s} (${e}/${t})`;this.showProcessing(n,r)}showThinking(s=1){let e=".".repeat(s%3+1);this.showProcessing(`AI\u601D\u8003\u4E2D${e}`)}cleanup(){this.updateInterval&&(clearInterval(this.updateInterval),this.updateInterval=null),this.statusBarItem.empty(),this.statusBarItem.removeEventListener("click",this.showDetailedStatus)}};var ne=class extends v{constructor(s){let e={name:"plugin_discovery",description:"\u53D1\u73B0\u548C\u5217\u51FA\u5DF2\u5B89\u88C5\u7684Obsidian\u63D2\u4EF6",category:"plugin",version:"1.0.0",parameters:{type:"object",properties:{includeDisabled:{type:"boolean",description:"\u662F\u5426\u5305\u542B\u5DF2\u7981\u7528\u7684\u63D2\u4EF6",default:!1},filterByName:{type:"string",description:"\u6309\u63D2\u4EF6\u540D\u79F0\u8FC7\u6EE4\uFF08\u652F\u6301\u90E8\u5206\u5339\u914D\uFF09"},sortBy:{type:"string",description:"\u6392\u5E8F\u65B9\u5F0F",enum:["name","id","enabled","version"],default:"name"}},required:[]},permissions:{required:["plugin_read"],optional:[],dangerous:!1,requiresConfirmation:!1},metadata:{author:"AI Coach Team",tags:["plugin","discovery","management"],documentation:"\u7528\u4E8E\u53D1\u73B0\u548C\u5217\u51FAObsidian\u4E2D\u5DF2\u5B89\u88C5\u7684\u63D2\u4EF6",examples:[{name:"\u5217\u51FA\u6240\u6709\u542F\u7528\u7684\u63D2\u4EF6",description:"\u83B7\u53D6\u6240\u6709\u5F53\u524D\u542F\u7528\u7684\u63D2\u4EF6\u5217\u8868",input:{},expectedOutput:{plugins:[]}},{name:"\u641C\u7D22\u7279\u5B9A\u63D2\u4EF6",description:"\u641C\u7D22\u540D\u79F0\u5305\u542B\u7279\u5B9A\u5173\u952E\u8BCD\u7684\u63D2\u4EF6",input:{filterByName:"calendar"},expectedOutput:{plugins:[]}}]}};super(e),this.app=s}async executeInternal(s,e){try{let{includeDisabled:t=!1,filterByName:r="",sortBy:n="name"}=s,o=this.app.plugins,i=[];for(let[a,c]of Object.entries(o.plugins)){let u=c.manifest,m=o.enabledPlugins.has(a);if(!t&&!m||r&&!u.name.toLowerCase().includes(r.toLowerCase()))continue;let p={id:a,name:u.name,version:u.version,enabled:m,description:u.description,author:u.author,authorUrl:u.authorUrl,minAppVersion:u.minAppVersion,commands:this.getPluginCommands(a)};i.push(p)}return this.sortPlugins(i,n),{success:!0,data:{plugins:i,totalCount:i.length,enabledCount:i.filter(a=>a.enabled).length,disabledCount:i.filter(a=>!a.enabled).length},metadata:{source:"obsidian_plugin_manager",timestamp:new Date().toISOString()}}}catch(t){return{success:!1,error:`\u63D2\u4EF6\u53D1\u73B0\u5931\u8D25: ${t.message}`,metadata:{errorType:"plugin_discovery_error"}}}}getPluginCommands(s){let e=[];try{let t=this.app.commands;for(let[r,n]of Object.entries(t.commands))r.startsWith(`${s}:`)&&e.push({id:r,name:n.name||r})}catch(t){console.warn(`Failed to get commands for plugin ${s}:`,t)}return e}sortPlugins(s,e){s.sort((t,r)=>{switch(e){case"name":return t.name.localeCompare(r.name);case"id":return t.id.localeCompare(r.id);case"enabled":return Number(r.enabled)-Number(t.enabled);case"version":return this.compareVersions(t.version,r.version);default:return t.name.localeCompare(r.name)}})}compareVersions(s,e){let t=s.split(".").map(Number),r=e.split(".").map(Number),n=Math.max(t.length,r.length);for(let o=0;o<n;o++){let i=t[o]||0,a=r[o]||0;if(i!==a)return a-i}return 0}async getPluginDetails(s){try{let e=this.app.plugins.plugins[s];if(!e)return null;let t=e.manifest,r=this.app.plugins.enabledPlugins.has(s);return{id:s,name:t.name,version:t.version,enabled:r,description:t.description,author:t.author,authorUrl:t.authorUrl,minAppVersion:t.minAppVersion,commands:this.getPluginCommands(s)}}catch(e){return console.error(`Failed to get plugin details for ${s}:`,e),null}}pluginExists(s){return s in this.app.plugins.plugins}isPluginEnabled(s){return this.app.plugins.enabledPlugins.has(s)}getPluginStats(){let s=Object.keys(this.app.plugins.plugins),e=Array.from(this.app.plugins.enabledPlugins),t=new Set(["file-explorer","global-search","switcher","graph","backlink","canvas","outgoing-link","tag-pane","page-preview","daily-notes","templates","note-composer","command-palette","slash-command","editor-status","starred","markdown-importer","zk-prefixer","random-note","outline","word-count","slides","audio-recorder","workspaces","file-recovery","publish","sync"]),r=s.filter(o=>t.has(o)),n=s.filter(o=>!t.has(o));return{totalPlugins:s.length,enabledPlugins:e.length,disabledPlugins:s.length-e.length,corePlugins:r.length,communityPlugins:n.length}}};var oe=class extends v{constructor(s){let e={name:"plugin_command_query",description:"\u67E5\u8BE2\u6307\u5B9A\u63D2\u4EF6\u7684\u53EF\u6267\u884C\u547D\u4EE4",category:"plugin",version:"1.0.0",parameters:{type:"object",properties:{pluginId:{type:"string",description:"\u63D2\u4EF6ID\uFF08\u53EF\u9009\uFF0C\u5982\u679C\u4E0D\u63D0\u4F9B\u5219\u67E5\u8BE2\u6240\u6709\u547D\u4EE4\uFF09"},commandFilter:{type:"string",description:"\u547D\u4EE4\u540D\u79F0\u8FC7\u6EE4\u5668\uFF08\u652F\u6301\u90E8\u5206\u5339\u914D\uFF09"},includeHotkeys:{type:"boolean",description:"\u662F\u5426\u5305\u542B\u5FEB\u6377\u952E\u4FE1\u606F",default:!0},sortBy:{type:"string",description:"\u6392\u5E8F\u65B9\u5F0F",enum:["name","id","plugin"],default:"name"}},required:[]},permissions:{required:["plugin_read"],optional:[],dangerous:!1,requiresConfirmation:!1},metadata:{author:"AI Coach Team",tags:["plugin","command","query"],documentation:"\u67E5\u8BE2Obsidian\u63D2\u4EF6\u7684\u53EF\u6267\u884C\u547D\u4EE4\u5217\u8868",examples:[{name:"\u67E5\u8BE2\u7279\u5B9A\u63D2\u4EF6\u7684\u547D\u4EE4",description:"\u83B7\u53D6\u6307\u5B9A\u63D2\u4EF6\u7684\u6240\u6709\u53EF\u7528\u547D\u4EE4",input:{pluginId:"calendar"},expectedOutput:{commands:[]}},{name:"\u641C\u7D22\u547D\u4EE4",description:"\u641C\u7D22\u5305\u542B\u7279\u5B9A\u5173\u952E\u8BCD\u7684\u547D\u4EE4",input:{commandFilter:"create"},expectedOutput:{commands:[]}}]}};super(e),this.app=s}async executeInternal(s,e){try{let{pluginId:t,commandFilter:r="",includeHotkeys:n=!0,sortBy:o="name"}=s,i=this.getCommands(t,r,n);return this.sortCommands(i,o),{success:!0,data:{commands:i,totalCount:i.length,pluginId:t||"all",filter:r},metadata:{source:"obsidian_command_manager",timestamp:new Date().toISOString(),queryType:t?"plugin_specific":"global"}}}catch(t){return{success:!1,error:`\u547D\u4EE4\u67E5\u8BE2\u5931\u8D25: ${t.message}`,metadata:{errorType:"command_query_error"}}}}getCommands(s,e="",t=!0){let r=[],n=this.app.commands;for(let[o,i]of Object.entries(n.commands)){if(s&&!o.startsWith(`${s}:`)||e&&!i.name.toLowerCase().includes(e.toLowerCase()))continue;let a={id:o,name:i.name,description:this.getCommandDescription(i),callback:i.callback?"function":void 0};t&&(a.hotkeys=this.getCommandHotkeys(o)),r.push(a)}return r}getCommandDescription(s){return s.description||void 0}getCommandHotkeys(s){let e=[];try{let r=this.app.hotkeyManager.getHotkeys(s);if(r&&r.length>0)for(let n of r)e.push({modifiers:n.modifiers||[],key:n.key||""})}catch(t){console.warn(`Failed to get hotkeys for command ${s}:`,t)}return e}sortCommands(s,e){s.sort((t,r)=>{switch(e){case"name":return t.name.localeCompare(r.name);case"id":return t.id.localeCompare(r.id);case"plugin":let n=this.extractPluginFromCommandId(t.id),o=this.extractPluginFromCommandId(r.id);return n.localeCompare(o);default:return t.name.localeCompare(r.name)}})}extractPluginFromCommandId(s){let e=s.split(":");return e.length>1?e[0]:"core"}getPluginCommandStats(s){let e=this.getCommands(s,"",!0),t=e.filter(r=>r.hotkeys&&r.hotkeys.length>0);return{totalCommands:e.length,commandsWithHotkeys:t.length,commandsWithoutHotkeys:e.length-t.length}}async searchCommands(s,e){let{pluginId:t,exactMatch:r=!1,includeDescription:n=!0}=e||{},o=this.getCommands(t,"",!0),i=s.toLowerCase();return o.filter(a=>{let c=r?a.name.toLowerCase()===i:a.name.toLowerCase().includes(i),u=n&&a.description?a.description.toLowerCase().includes(i):!1,m=a.id.toLowerCase().includes(i);return c||u||m})}commandExists(s){return s in this.app.commands.commands}getCommandDetails(s){let e=this.app.commands.commands[s];return e?{id:s,name:e.name,description:this.getCommandDescription(e),callback:e.callback?"function":void 0,hotkeys:this.getCommandHotkeys(s)}:null}getAllPluginCommandStats(){let s={},e=this.app.commands;for(let t of Object.keys(e.commands)){let r=this.extractPluginFromCommandId(t);s[r]=(s[r]||0)+1}return s}getMostUsedCommands(s=10){return this.getCommands(void 0,"",!0).sort((r,n)=>{var a,c;let o=((a=r.hotkeys)==null?void 0:a.length)||0;return(((c=n.hotkeys)==null?void 0:c.length)||0)-o}).slice(0,s)}};var ie=class extends v{constructor(e){super({name:"plugin_command_executor",description:"\u6267\u884CObsidian\u63D2\u4EF6\u547D\u4EE4",category:"plugin",version:"1.0.0",parameters:{type:"object",properties:{commandId:{type:"string",description:"\u8981\u6267\u884C\u7684\u547D\u4EE4ID"},confirmExecution:{type:"boolean",description:"\u662F\u5426\u9700\u8981\u7528\u6237\u786E\u8BA4\u6267\u884C",default:!1},timeout:{type:"number",description:"\u547D\u4EE4\u6267\u884C\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09",default:5e3,minimum:1e3,maximum:3e4}},required:["commandId"]},permissions:{required:["plugin_execute"],optional:["plugin_dangerous"],dangerous:!0,requiresConfirmation:!0},metadata:{author:"AI Coach Team",tags:["plugin","command","execution"],documentation:"\u5B89\u5168\u5730\u6267\u884CObsidian\u63D2\u4EF6\u547D\u4EE4",examples:[{name:"\u6267\u884C\u7B80\u5355\u547D\u4EE4",description:"\u6267\u884C\u4E00\u4E2A\u4E0D\u9700\u8981\u53C2\u6570\u7684\u63D2\u4EF6\u547D\u4EE4",input:{commandId:"daily-notes:open-today"},expectedOutput:{success:!0}}]}});this.executionHistory=[];this.maxHistorySize=100;this.app=e}async executeInternal(e,t){var a;let r=Date.now(),{commandId:n,confirmExecution:o=!1,timeout:i=5e3}=e;try{let c=this.app.commands.commands[n];if(!c)return{success:!1,error:`\u547D\u4EE4\u4E0D\u5B58\u5728: ${n}`,metadata:{commandId:n,executionTime:Date.now()-r,errorType:"command_not_found"}};if(!c.callback)return{success:!1,error:`\u547D\u4EE4\u4E0D\u53EF\u6267\u884C: ${n}`,metadata:{commandId:n,executionTime:Date.now()-r,errorType:"command_not_executable"}};if(o&&t&&!((a=t.config)!=null&&a.confirmed))return{success:!1,error:"\u9700\u8981\u7528\u6237\u786E\u8BA4\u624D\u80FD\u6267\u884C\u6B64\u547D\u4EE4",metadata:{commandId:n,commandName:c.name,requiresConfirmation:!0,executionTime:Date.now()-r}};let u=await this.executeCommand(n,i);return this.recordExecution({commandId:n,commandName:c.name,success:u.success,executionTime:Date.now()-r,timestamp:new Date,error:u.error,context:(t==null?void 0:t.userId)||"unknown"}),{success:u.success,data:{commandId:n,commandName:c.name,executed:!0,result:u.result},error:u.error,metadata:{commandId:n,commandName:c.name,executionTime:Date.now()-r,timestamp:new Date().toISOString()}}}catch(c){let u=Date.now()-r;return this.recordExecution({commandId:n,commandName:"unknown",success:!1,executionTime:u,timestamp:new Date,error:c.message,context:(t==null?void 0:t.userId)||"unknown"}),{success:!1,error:`\u547D\u4EE4\u6267\u884C\u5931\u8D25: ${c.message}`,metadata:{commandId:n,executionTime:u,errorType:"execution_error"}}}}async validateInternal(e){let t=[],r=[],{commandId:n,timeout:o}=e;return!n||typeof n!="string"?t.push("\u547D\u4EE4ID\u5FC5\u987B\u662F\u975E\u7A7A\u5B57\u7B26\u4E32"):this.app.commands.commands[n]?(this.app.commands.commands[n].callback||t.push(`\u547D\u4EE4\u4E0D\u53EF\u6267\u884C: ${n}`),this.isDangerousCommand(n)&&r.push(`\u8FD9\u662F\u4E00\u4E2A\u6F5C\u5728\u5371\u9669\u7684\u547D\u4EE4: ${n}`)):t.push(`\u547D\u4EE4\u4E0D\u5B58\u5728: ${n}`),o&&(o<1e3||o>3e4)&&t.push("\u8D85\u65F6\u65F6\u95F4\u5FC5\u987B\u57281000-30000\u6BEB\u79D2\u4E4B\u95F4"),{valid:t.length===0,errors:t,warnings:r}}async executeCommand(e,t){return new Promise(r=>{let n=setTimeout(()=>{r({success:!1,error:"\u547D\u4EE4\u6267\u884C\u8D85\u65F6"})},t);try{let o=this.app.commands.commands[e],i=o.callback,a;typeof i=="function"&&(a=i.call(o)),clearTimeout(n),a&&typeof a.then=="function"?a.then(c=>{r({success:!0,result:c})}).catch(c=>{r({success:!1,error:c.message||"\u547D\u4EE4\u6267\u884C\u5931\u8D25"})}):r({success:!0,result:a})}catch(o){clearTimeout(n),r({success:!1,error:o.message||"\u547D\u4EE4\u6267\u884C\u5F02\u5E38"})}})}isDangerousCommand(e){let t=["delete","remove","clear","reset","destroy","wipe","format","erase"],r=e.toLowerCase();return t.some(n=>r.includes(n))}recordExecution(e){this.executionHistory.push(e),this.executionHistory.length>this.maxHistorySize&&(this.executionHistory=this.executionHistory.slice(-this.maxHistorySize))}getExecutionHistory(e){let t=[...this.executionHistory].reverse();return e?t.slice(0,e):t}getExecutionStats(){let e=this.executionHistory.length,t=this.executionHistory.filter(a=>a.success).length,r=e-t,n=e>0?this.executionHistory.reduce((a,c)=>a+c.executionTime,0)/e:0,o={};this.executionHistory.forEach(a=>{o[a.commandId]=(o[a.commandId]||0)+1});let i=Object.entries(o).sort(([,a],[,c])=>c-a).slice(0,10).map(([a,c])=>({commandId:a,count:c}));return{totalExecutions:e,successfulExecutions:t,failedExecutions:r,averageExecutionTime:Math.round(n),mostExecutedCommands:i}}clearExecutionHistory(){this.executionHistory=[]}async executeCommandsBatch(e,t){let r=[],{stopOnFailure:n=!1,timeout:o=5e3,confirmEach:i=!1}=t||{};for(let a of e){let c=await this.executeInternal({commandId:a,timeout:o,confirmExecution:i});if(r.push({commandId:a,result:c}),!c.success&&n)break}return r}};var ae=class extends v{constructor(e){super({name:"plugin_manager",description:"\u7EFC\u5408\u7684\u63D2\u4EF6\u7BA1\u7406\u5DE5\u5177\uFF0C\u652F\u6301\u63D2\u4EF6\u53D1\u73B0\u3001\u547D\u4EE4\u67E5\u8BE2\u548C\u6267\u884C",category:"plugin",version:"1.0.0",parameters:{type:"object",properties:{action:{type:"string",description:"\u8981\u6267\u884C\u7684\u64CD\u4F5C",enum:["discover","query_commands","execute_command","get_stats"]},pluginId:{type:"string",description:"\u63D2\u4EF6ID\uFF08\u7528\u4E8E\u7279\u5B9A\u63D2\u4EF6\u64CD\u4F5C\uFF09"},commandId:{type:"string",description:"\u547D\u4EE4ID\uFF08\u7528\u4E8E\u547D\u4EE4\u6267\u884C\uFF09"},filter:{type:"string",description:"\u8FC7\u6EE4\u6761\u4EF6\uFF08\u7528\u4E8E\u641C\u7D22\uFF09"},includeDisabled:{type:"boolean",description:"\u662F\u5426\u5305\u542B\u7981\u7528\u7684\u63D2\u4EF6",default:!1},confirmExecution:{type:"boolean",description:"\u6267\u884C\u547D\u4EE4\u524D\u662F\u5426\u9700\u8981\u786E\u8BA4",default:!0}},required:["action"]},permissions:{required:["plugin_read"],optional:["plugin_execute","plugin_dangerous"],dangerous:!1,requiresConfirmation:!1},metadata:{author:"AI Coach Team",tags:["plugin","management","comprehensive"],documentation:"\u63D0\u4F9B\u5B8C\u6574\u7684\u63D2\u4EF6\u7BA1\u7406\u529F\u80FD\uFF0C\u5305\u62EC\u53D1\u73B0\u3001\u67E5\u8BE2\u548C\u6267\u884C",examples:[{name:"\u53D1\u73B0\u6240\u6709\u63D2\u4EF6",description:"\u83B7\u53D6\u6240\u6709\u5DF2\u5B89\u88C5\u63D2\u4EF6\u7684\u5217\u8868",input:{action:"discover"},expectedOutput:{plugins:[]}},{name:"\u67E5\u8BE2\u63D2\u4EF6\u547D\u4EE4",description:"\u67E5\u8BE2\u7279\u5B9A\u63D2\u4EF6\u7684\u53EF\u7528\u547D\u4EE4",input:{action:"query_commands",pluginId:"calendar"},expectedOutput:{commands:[]}},{name:"\u6267\u884C\u63D2\u4EF6\u547D\u4EE4",description:"\u6267\u884C\u6307\u5B9A\u7684\u63D2\u4EF6\u547D\u4EE4",input:{action:"execute_command",commandId:"daily-notes:open-today"},expectedOutput:{success:!0}}]}});l(this,"app");l(this,"discoveryTool");l(this,"commandQueryTool");l(this,"commandExecutorTool");this.app=e,this.discoveryTool=new ne(e),this.commandQueryTool=new oe(e),this.commandExecutorTool=new ie(e)}async executeInternal(e,t){let{action:r}=e;try{switch(r){case"discover":return await this.handleDiscoverAction(e,t);case"query_commands":return await this.handleQueryCommandsAction(e,t);case"execute_command":return await this.handleExecuteCommandAction(e,t);case"get_stats":return await this.handleGetStatsAction(e,t);default:return{success:!1,error:`\u672A\u77E5\u7684\u64CD\u4F5C: ${r}`,metadata:{action:r}}}}catch(n){return{success:!1,error:`\u63D2\u4EF6\u7BA1\u7406\u64CD\u4F5C\u5931\u8D25: ${n.message}`,metadata:{action:r,errorType:"plugin_manager_error"}}}}async validateInternal(e){let t=[],r=[],{action:n,commandId:o,pluginId:i}=e,a=["discover","query_commands","execute_command","get_stats"];switch(a.includes(n)||t.push(`\u65E0\u6548\u7684\u64CD\u4F5C: ${n}\u3002\u6709\u6548\u64CD\u4F5C: ${a.join(", ")}`),n){case"execute_command":o||t.push("\u6267\u884C\u547D\u4EE4\u64CD\u4F5C\u9700\u8981\u63D0\u4F9B commandId \u53C2\u6570");break;case"query_commands":i&&!this.discoveryTool.pluginExists(i)&&r.push(`\u63D2\u4EF6\u53EF\u80FD\u4E0D\u5B58\u5728: ${i}`);break}return{valid:t.length===0,errors:t,warnings:r}}async handleDiscoverAction(e,t){let{includeDisabled:r,filter:n,pluginId:o}=e;if(o){let i=await this.discoveryTool.getPluginDetails(o);return i?{success:!0,data:{plugin:i},metadata:{action:"discover_specific",pluginId:o}}:{success:!1,error:`\u63D2\u4EF6\u4E0D\u5B58\u5728: ${o}`,metadata:{pluginId:o}}}else return await this.discoveryTool.execute({includeDisabled:r,filterByName:n},t)}async handleQueryCommandsAction(e,t){let{pluginId:r,filter:n}=e;return await this.commandQueryTool.execute({pluginId:r,commandFilter:n,includeHotkeys:!0},t)}async handleExecuteCommandAction(e,t){let{commandId:r,confirmExecution:n}=e;return await this.commandExecutorTool.execute({commandId:r,confirmExecution:n},t)}async handleGetStatsAction(e,t){try{let r=this.discoveryTool.getPluginStats(),n=this.commandQueryTool.getAllPluginCommandStats(),o=this.commandExecutorTool.getExecutionStats();return{success:!0,data:{plugins:r,commands:n,executions:o,summary:{totalPlugins:r.totalPlugins,totalCommands:Object.values(n).reduce((i,a)=>i+a,0),totalExecutions:o.totalExecutions}},metadata:{action:"get_stats",timestamp:new Date().toISOString()}}}catch(r){return{success:!1,error:`\u83B7\u53D6\u7EDF\u8BA1\u4FE1\u606F\u5931\u8D25: ${r.message}`,metadata:{action:"get_stats"}}}}async searchPluginsAndCommands(e){let t=await this.discoveryTool.execute({filterByName:e,includeDisabled:!0}),r=await this.commandQueryTool.execute({commandFilter:e,includeHotkeys:!0});return{plugins:t.success?t.data.plugins:[],commands:r.success?r.data.commands:[]}}async getPluginFullInfo(e){let t=await this.discoveryTool.getPluginDetails(e);if(!t)return null;let r=this.commandQueryTool.getPluginCommandStats(e),n=await this.commandQueryTool.execute({pluginId:e,includeHotkeys:!0});return{...t,commandStats:r,commands:n.success?n.data.commands:[]}}async executeBatchCommands(e,t){return await this.commandExecutorTool.executeCommandsBatch(e,t)}};var Ee=require("obsidian");var fe=require("obsidian"),ce=class{constructor(s,e=1e3){this.indices=new Map;this.chunkSize=1e3;this.overlapSize=200;this.app=s,this.vault=s.vault,this.chunkSize=e,this.overlapSize=Math.min(200,Math.floor(e*.2))}async indexFile(s){try{let e=s.content||await this.readFileContent(s.path);if(!e)return[];let t=this.extractMetadata(e,s),r=this.chunkText(e),n=[];for(let o=0;o<r.length;o++){let i=r[o],a={id:this.generateIndexId(s.path,o),filePath:s.path,content:i,embedding:void 0,metadata:{...t,chunkIndex:o,totalChunks:r.length,wordCount:this.countWords(i)},createdAt:new Date,updatedAt:new Date};n.push(a)}return this.indices.set(s.path,n),n}catch(e){return console.error(`Failed to index file ${s.path}:`,e),[]}}async updateIndex(s){try{let e=this.vault.getAbstractFileByPath(s);if(!(e instanceof fe.TFile))throw new Error(`File not found: ${s}`);let t={path:s,name:e.name,extension:e.extension,size:e.stat.size,mtime:e.stat.mtime};await this.indexFile(t)}catch(e){throw console.error(`Failed to update index for ${s}:`,e),e}}async deleteIndex(s){this.indices.delete(s)}async search(s){let{query:e,maxResults:t=10,threshold:r=.5}=s,n=[];for(let[o,i]of this.indices.entries())for(let a of i){let c=this.calculateTextSimilarity(e,a.content);c>=r&&n.push({file:{path:o},content:a.content,score:c,highlights:this.extractHighlights(e,a.content),metadata:a.metadata})}return n.sort((o,i)=>i.score-o.score),n.slice(0,t)}async getStats(){let s=this.indices.size,e=0,t=0;for(let r of this.indices.values())e+=r.length,t+=r.reduce((n,o)=>n+o.content.length,0);return{totalFiles:s,indexedFiles:s,totalChunks:e,lastUpdated:new Date,indexSize:t}}async readFileContent(s){try{let e=this.vault.getAbstractFileByPath(s);if(!(e instanceof fe.TFile))throw new Error(`File not found: ${s}`);return await this.vault.read(e)}catch(e){return console.error(`Failed to read file ${s}:`,e),""}}extractMetadata(s,e){let t={title:e.name.replace(/\.[^/.]+$/,""),tags:[],links:[],headings:[]},r=s.match(/^#\s+(.+)$/m);r&&(t.title=r[1].trim());let n=s.match(/#[\w\-_]+/g);n&&(t.tags=[...new Set(n.map(a=>a.slice(1)))]);let o=s.match(/\[\[([^\]]+)\]\]/g);o&&(t.links=[...new Set(o.map(a=>a.slice(2,-2)))]);let i=s.match(/^#{1,6}\s+(.+)$/gm);return i&&(t.headings=i.map(a=>{var m;let c=((m=a.match(/^#+/))==null?void 0:m[0].length)||1,u=a.replace(/^#+\s+/,"");return`${"#".repeat(c)} ${u}`})),t}chunkText(s){let e=[],t=this.splitIntoSentences(s),r="",n=0;for(let o of t){let i=o.length;n+i>this.chunkSize&&r?(e.push(r.trim()),r=this.getOverlapText(r)+o,n=r.length):(r+=o,n+=i)}return r.trim()&&e.push(r.trim()),e.filter(o=>o.length>50)}splitIntoSentences(s){return s.split(/[.!?]+/).map(e=>e.trim()).filter(e=>e.length>0).map(e=>e+". ")}getOverlapText(s){if(s.length<=this.overlapSize)return s;let e=s.slice(-this.overlapSize),t=e.lastIndexOf(" ");return t>this.overlapSize*.5?e.slice(t+1):e}calculateTextSimilarity(s,e){let t=s.toLowerCase().split(/\s+/).filter(o=>o.length>2),r=e.toLowerCase().split(/\s+/);if(t.length===0)return 0;let n=0;for(let o of t)r.some(i=>i.includes(o))&&n++;return n/t.length}extractHighlights(s,e,t=3){let r=s.toLowerCase().split(/\s+/).filter(i=>i.length>2),n=e.split(/[.!?]+/).filter(i=>i.trim().length>0),o=[];for(let i of n){let a=i.toLowerCase();r.some(u=>a.includes(u))&&o.length<t&&o.push(i.trim()+"...")}return o}countWords(s){return s.split(/\s+/).filter(e=>e.length>0).length}generateIndexId(s,e){return`${s}#${e}`}getAllIndices(){return new Map(this.indices)}clearAllIndices(){this.indices.clear()}async indexFiles(s){for(let t=0;t<s.length;t+=10){let r=s.slice(t,t+10);await Promise.all(r.map(n=>this.indexFile(n)))}}};var le=class extends v{constructor(e){super({name:"vault_query",description:"\u641C\u7D22\u548C\u67E5\u8BE2Vault\u4E2D\u7684\u7B14\u8BB0\u5185\u5BB9",category:"vault",version:"1.0.0",parameters:{type:"object",properties:{query:{type:"string",description:"\u641C\u7D22\u67E5\u8BE2\u5B57\u7B26\u4E32"},maxResults:{type:"number",description:"\u6700\u5927\u8FD4\u56DE\u7ED3\u679C\u6570",default:10,minimum:1,maximum:50},fileTypes:{type:"array",description:"\u6587\u4EF6\u7C7B\u578B\u8FC7\u6EE4",items:{type:"string"},default:["md"]},tags:{type:"array",description:"\u6807\u7B7E\u8FC7\u6EE4",items:{type:"string"}},includeContent:{type:"boolean",description:"\u662F\u5426\u5305\u542B\u6587\u4EF6\u5185\u5BB9",default:!0},threshold:{type:"number",description:"\u76F8\u4F3C\u5EA6\u9608\u503C",default:.3,minimum:0,maximum:1}},required:["query"]},permissions:{required:["vault_read"],optional:[],dangerous:!1,requiresConfirmation:!1},metadata:{author:"AI Coach Team",tags:["vault","search","query"],documentation:"\u5728Obsidian Vault\u4E2D\u641C\u7D22\u548C\u67E5\u8BE2\u7B14\u8BB0\u5185\u5BB9",examples:[{name:"\u57FA\u7840\u641C\u7D22",description:"\u641C\u7D22\u5305\u542B\u7279\u5B9A\u5173\u952E\u8BCD\u7684\u7B14\u8BB0",input:{query:"machine learning"},expectedOutput:{results:[]}},{name:"\u6807\u7B7E\u8FC7\u6EE4\u641C\u7D22",description:"\u641C\u7D22\u7279\u5B9A\u6807\u7B7E\u7684\u7B14\u8BB0",input:{query:"neural networks",tags:["ai","research"]},expectedOutput:{results:[]}}]}});l(this,"app");l(this,"indexer");l(this,"initialized",!1);this.app=e,this.indexer=new ce(e)}async initialize(){if(!this.initialized)try{await this.buildInitialIndex(),this.initialized=!0,console.log("VaultQueryTool initialized successfully")}catch(e){throw console.error("Failed to initialize VaultQueryTool:",e),e}}async executeInternal(e,t){try{this.initialized||await this.initialize();let{query:r,maxResults:n=10,fileTypes:o=["md"],tags:i,includeContent:a=!0,threshold:c=.3}=e,u={query:r,maxResults:n,threshold:c,includeContent:a,fileTypes:o,tags:i},m=await this.searchVault(u);return{success:!0,data:{query:r,results:m,totalResults:m.length,searchOptions:u},metadata:{source:"vault_indexer",timestamp:new Date().toISOString(),indexStats:await this.indexer.getStats()}}}catch(r){return{success:!1,error:`Vault\u67E5\u8BE2\u5931\u8D25: ${r.message}`,metadata:{errorType:"vault_query_error"}}}}async searchVault(e){let t=await this.indexer.search(e),r=[];if(e.useSemanticSearch!==!1)try{r=await this.performSemanticSearch(e)}catch(o){console.warn("Semantic search failed, falling back to basic search:",o)}return this.mergeSearchResults(t,r).map(o=>{var i;return{content:o.content,file:((i=o.file)==null?void 0:i.path)||o.filePath,score:o.score,metadata:{highlights:o.highlights||this.generateHighlights(o.content,e.query),searchType:o.searchType||"basic",...o.metadata}}})}async performSemanticSearch(e){if(!this.embeddingService)return[];try{let t=await this.embeddingService.generateEmbedding(e.query);return(await this.vectorStore.search(t,e.maxResults*2,e.threshold)).map(n=>{var o;return{...n,searchType:"semantic",filePath:((o=n.metadata)==null?void 0:o.file)||n.file}})}catch(t){return console.error("Semantic search error:",t),[]}}mergeSearchResults(e,t){var n,o;let r=new Map;for(let i of e){let a=`${((n=i.file)==null?void 0:n.path)||i.filePath}_${i.chunkIndex||0}`;r.set(a,{...i,searchType:"basic"})}for(let i of t){let a=`${i.filePath}_${((o=i.metadata)==null?void 0:o.chunkIndex)||0}`,c=r.get(a);c?(c.score=Math.max(c.score,i.score*1.1),c.searchType="hybrid"):r.set(a,i)}return Array.from(r.values()).sort((i,a)=>a.score-i.score).slice(0,20)}generateHighlights(e,t){if(!e||!t)return[];let r=[],n=t.toLowerCase().split(/\s+/),o=e.split(/[.!?]+/).filter(i=>i.trim().length>10);for(let i of o){let a=i.toLowerCase(),c=0;for(let u of n)a.includes(u)&&(c+=u.length);if(c>0&&r.push(i.trim()),r.length>=3)break}return r}async buildInitialIndex(){let e=this.app.vault.getMarkdownFiles(),t=[];for(let r of e)try{let n=await this.app.vault.read(r),o={path:r.path,name:r.name,extension:r.extension,size:r.stat.size,mtime:r.stat.mtime,content:n};t.push(o)}catch(n){console.warn(`Failed to read file ${r.path}:`,n)}await this.indexer.indexFiles(t)}async rebuildIndex(){this.indexer.clearAllIndices(),await this.buildInitialIndex()}async updateFileIndex(e){await this.indexer.updateIndex(e)}async deleteFileIndex(e){await this.indexer.deleteIndex(e)}async getRelatedFiles(e,t=5){try{let r=this.app.vault.getAbstractFileByPath(e);if(!(r instanceof Ee.TFile))throw new Error(`File not found: ${e}`);let n=await this.app.vault.read(r),a={query:this.extractKeywords(n).slice(0,5).join(" "),maxResults:t+1,threshold:.2,includeContent:!0,fileTypes:["md"]};return(await this.searchVault(a)).filter(u=>u.file!==e).slice(0,t)}catch(r){return console.error(`Failed to get related files for ${e}:`,r),[]}}extractKeywords(e){let t=e.toLowerCase().replace(/[^\w\s]/g," ").split(/\s+/).filter(n=>n.length>3),r={};return t.forEach(n=>{r[n]=(r[n]||0)+1}),Object.entries(r).sort(([,n],[,o])=>o-n).slice(0,10).map(([n])=>n)}async searchByTags(e,t=10){let r={query:e.join(" "),maxResults:t,threshold:.1,includeContent:!0,tags:e,fileTypes:["md"]};return await this.searchVault(r)}async searchByLinks(e,t=10){let r={query:`[[${e}]]`,maxResults:t,threshold:.5,includeContent:!0,fileTypes:["md"]};return await this.searchVault(r)}async getIndexStats(){return await this.indexer.getStats()}async getSearchSuggestions(e){let t=this.indexer.getAllIndices(),r=new Set;for(let n of t.values())for(let o of n){let i=o.content.toLowerCase(),a=e.toLowerCase();if(i.includes(a)){let c=i.split(/\s+/);for(let u=0;u<c.length-1;u++){let m=c.slice(u,u+3).join(" ");m.includes(a)&&m.length>e.length&&r.add(m)}}}return Array.from(r).slice(0,10)}};var ue=class extends v{constructor(){super({name:"web_search",description:"\u5728\u7F51\u7EDC\u4E0A\u641C\u7D22\u4FE1\u606F\uFF0C\u652F\u6301\u591A\u79CD\u641C\u7D22\u5F15\u64CE",category:"web",version:"1.0.0",parameters:{type:"object",properties:{query:{type:"string",description:"\u641C\u7D22\u67E5\u8BE2\u5B57\u7B26\u4E32"},engine:{type:"string",description:"\u641C\u7D22\u5F15\u64CE",enum:["duckduckgo","google","bing"],default:"duckduckgo"},maxResults:{type:"number",description:"\u6700\u5927\u8FD4\u56DE\u7ED3\u679C\u6570",default:5,minimum:1,maximum:20},language:{type:"string",description:"\u641C\u7D22\u8BED\u8A00",default:"zh-CN"},safeSearch:{type:"boolean",description:"\u542F\u7528\u5B89\u5168\u641C\u7D22",default:!0},region:{type:"string",description:"\u641C\u7D22\u5730\u533A",default:"CN"}},required:["query"]},permissions:{required:["web_access"],optional:["web_api_key"],dangerous:!1,requiresConfirmation:!1},metadata:{author:"AI Coach Team",tags:["web","search","information"],documentation:"\u5728\u7F51\u7EDC\u4E0A\u641C\u7D22\u4FE1\u606F\uFF0C\u83B7\u53D6\u6700\u65B0\u7684\u5916\u90E8\u77E5\u8BC6",examples:[{name:"\u57FA\u7840\u641C\u7D22",description:"\u641C\u7D22\u7279\u5B9A\u4E3B\u9898\u7684\u4FE1\u606F",input:{query:"artificial intelligence latest news"},expectedOutput:{results:[]}},{name:"\u6307\u5B9A\u641C\u7D22\u5F15\u64CE",description:"\u4F7F\u7528\u7279\u5B9A\u641C\u7D22\u5F15\u64CE\u8FDB\u884C\u641C\u7D22",input:{query:"machine learning",engine:"google",maxResults:10},expectedOutput:{results:[]}}]}});l(this,"searchEngines",new Map);this.initializeSearchEngines()}async executeInternal(e,t){try{let{query:r,engine:n="duckduckgo",maxResults:o=5,language:i="zh-CN",safeSearch:a=!0,region:c="CN"}=e,u=this.searchEngines.get(n);if(!u)return{success:!1,error:`\u4E0D\u652F\u6301\u7684\u641C\u7D22\u5F15\u64CE: ${n}`};let m={query:r,maxResults:o,language:i,safeSearch:a,region:c},p=await u.search(m);return{success:!0,data:{query:r,engine:n,results:p,totalResults:p.length,searchOptions:m},metadata:{source:n,timestamp:new Date().toISOString(),searchTime:Date.now()}}}catch(r){return{success:!1,error:`\u7F51\u7EDC\u641C\u7D22\u5931\u8D25: ${r.message}`,metadata:{errorType:"web_search_error"}}}}initializeSearchEngines(){this.searchEngines.set("duckduckgo",new ye),this.searchEngines.set("google",new ve),this.searchEngines.set("bing",new xe)}addSearchEngine(e,t){this.searchEngines.set(e,t)}getSupportedEngines(){return Array.from(this.searchEngines.keys())}},ye=class{constructor(){l(this,"baseUrl","https://api.duckduckgo.com/")}async search(s){try{let{query:e,maxResults:t}=s,r=`${this.baseUrl}?q=${encodeURIComponent(e)}&format=json&no_html=1&skip_disambig=1`,n=await fetch(r);if(!n.ok)throw new Error(`DuckDuckGo API error: ${n.status}`);let o=await n.json(),i=[];if(o.Abstract&&i.push({title:o.Heading||"DuckDuckGo Instant Answer",url:o.AbstractURL||"",snippet:o.Abstract,score:1}),o.RelatedTopics&&Array.isArray(o.RelatedTopics))for(let a of o.RelatedTopics.slice(0,t-i.length))a.Text&&a.FirstURL&&i.push({title:a.Text.split(" - ")[0]||"Related Topic",url:a.FirstURL,snippet:a.Text,score:.8});return i.slice(0,t)}catch(e){throw console.error("DuckDuckGo search error:",e),e}}isConfigured(){return!0}configure(s){}},ve=class{constructor(){l(this,"apiKey","");l(this,"searchEngineId","");l(this,"baseUrl","https://www.googleapis.com/customsearch/v1")}async search(s){if(!this.isConfigured())throw new Error("Google Search API not configured");try{let{query:e,maxResults:t,language:r,safeSearch:n}=s,o=new URLSearchParams({key:this.apiKey,cx:this.searchEngineId,q:e,num:Math.min(t,10).toString(),hl:r,safe:n?"active":"off"}),i=`${this.baseUrl}?${o}`,a=await fetch(i);if(!a.ok)throw new Error(`Google Search API error: ${a.status}`);let c=await a.json(),u=[];if(c.items&&Array.isArray(c.items))for(let m of c.items)u.push({title:m.title||"",url:m.link||"",snippet:m.snippet||"",score:.9});return u}catch(e){throw console.error("Google search error:",e),e}}isConfigured(){return!!(this.apiKey&&this.searchEngineId)}configure(s){this.apiKey=s.apiKey||"",this.searchEngineId=s.searchEngineId||""}},xe=class{constructor(){l(this,"apiKey","");l(this,"baseUrl","https://api.bing.microsoft.com/v7.0/search")}async search(s){if(!this.isConfigured())throw new Error("Bing Search API not configured");try{let{query:e,maxResults:t,language:r,safeSearch:n}=s,o=new URLSearchParams({q:e,count:Math.min(t,50).toString(),mkt:r,safeSearch:n?"Strict":"Off"}),i=`${this.baseUrl}?${o}`,a=await fetch(i,{headers:{"Ocp-Apim-Subscription-Key":this.apiKey}});if(!a.ok)throw new Error(`Bing Search API error: ${a.status}`);let c=await a.json(),u=[];if(c.webPages&&c.webPages.value)for(let m of c.webPages.value)u.push({title:m.name||"",url:m.url||"",snippet:m.snippet||"",score:.85});return u}catch(e){throw console.error("Bing search error:",e),e}}isConfigured(){return!!this.apiKey}configure(s){this.apiKey=s.apiKey||""}};var V=class{constructor(s={}){this.timeout=s.timeout||5e3,this.memoryLimit=s.memoryLimit||10*1024*1024,this.allowedGlobals=new Set(s.allowedGlobals||this.getDefaultAllowedGlobals()),this.blockedPatterns=s.blockedPatterns||this.getDefaultBlockedPatterns()}async execute(s,e={}){let t=Date.now();try{let r=this.preprocessCode(s),n=this.validateCodeSecurity(r);if(!n.safe)return{success:!1,error:`\u4EE3\u7801\u5B89\u5168\u68C0\u67E5\u5931\u8D25: ${n.reason}`,executionTime:Date.now()-t};let o=this.createSandboxEnvironment(e),i=await this.executeInSandbox(r,o);return{success:!0,result:i.value,output:i.output,executionTime:Date.now()-t,memoryUsed:i.memoryUsed}}catch(r){return{success:!1,error:r.message,executionTime:Date.now()-t}}}preprocessCode(s){let e=s.replace(/\/\*[\s\S]*?\*\//g,"");return e=e.replace(/\/\/.*$/gm,""),e.includes("use strict")||(e=`"use strict";
`+e),e}validateCodeSecurity(s){for(let t of this.blockedPatterns)if(t.test(s))return{safe:!1,reason:`\u4EE3\u7801\u5305\u542B\u88AB\u7981\u6B62\u7684\u6A21\u5F0F: ${t.source}`};return s.length>5e4?{safe:!1,reason:"\u4EE3\u7801\u957F\u5EA6\u8D85\u8FC7\u9650\u5236"}:this.getMaxNestingDepth(s)>20?{safe:!1,reason:"\u4EE3\u7801\u5D4C\u5957\u6DF1\u5EA6\u8FC7\u6DF1"}:{safe:!0}}createSandboxEnvironment(s){let e=[];return{Object,Array,String,Number,Boolean,Date,Math,JSON,RegExp,Error,console:{log:(...r)=>e.push(r.map(n=>String(n)).join(" ")),warn:(...r)=>e.push("WARN: "+r.map(n=>String(n)).join(" ")),error:(...r)=>e.push("ERROR: "+r.map(n=>String(n)).join(" ")),info:(...r)=>e.push("INFO: "+r.map(n=>String(n)).join(" "))},...s,__output:e,__memoryUsage:0}}async executeInSandbox(s,e){return new Promise((t,r)=>{let n=setTimeout(()=>{r(new Error("\u4EE3\u7801\u6267\u884C\u8D85\u65F6"))},this.timeout);try{let i=new Function(...Object.keys(e),`
          try {
            ${s}
          } catch (error) {
            throw error;
          }
        `)(...Object.values(e));clearTimeout(n),t({value:i,output:e.__output.join(`
`),memoryUsed:this.estimateMemoryUsage(e)})}catch(o){clearTimeout(n),r(o)}})}estimateMemoryUsage(s){try{return JSON.stringify(s).length*2}catch(e){return 0}}getMaxNestingDepth(s){let e=0,t=0;for(let r of s)r==="{"||r==="("||r==="["?(t++,e=Math.max(e,t)):(r==="}"||r===")"||r==="]")&&t--;return e}getDefaultAllowedGlobals(){return["Object","Array","String","Number","Boolean","Date","Math","JSON","RegExp","Error","parseInt","parseFloat","isNaN","isFinite","encodeURIComponent","decodeURIComponent"]}getDefaultBlockedPatterns(){return[/eval\s*\(/,/Function\s*\(/,/setTimeout\s*\(/,/setInterval\s*\(/,/require\s*\(/,/import\s+/,/export\s+/,/process\./,/global\./,/window\./,/document\./,/fetch\s*\(/,/XMLHttpRequest/,/WebSocket/,/localStorage/,/sessionStorage/,/indexedDB/,/navigator\./,/location\./,/history\./,/alert\s*\(/,/confirm\s*\(/,/prompt\s*\(/,/while\s*\(\s*true\s*\)/,/for\s*\(\s*;\s*;\s*\)/,/\.\s*constructor/,/__proto__/,/prototype\s*\[/]}};var me=class{constructor(){this.syntaxChecker=new we,this.securityAnalyzer=new Te,this.codeFormatter=new Ce}async validateCode(s,e={}){let t={valid:!0,errors:[],warnings:[],suggestions:[],processedCode:s};try{let r=this.extractCodeFromMarkdown(s),n=await this.syntaxChecker.check(r);n.valid||(t.valid=!1,t.errors.push(...n.errors)),t.warnings.push(...n.warnings);let o=await this.securityAnalyzer.analyze(r);o.safe||(t.valid=!1,t.errors.push(...o.risks.map(i=>`\u5B89\u5168\u98CE\u9669: ${i}`))),t.warnings.push(...o.warnings),t.valid&&e.format?t.processedCode=await this.codeFormatter.format(r):t.processedCode=r,t.suggestions=this.generateSuggestions(r,n,o)}catch(r){t.valid=!1,t.errors.push(`\u9A8C\u8BC1\u8FC7\u7A0B\u51FA\u9519: ${r.message}`)}return t}extractCodeFromMarkdown(s){let e=/```(?:javascript|js)?\n?([\s\S]*?)```/gi,t=s.match(e);if(t&&t.length>0)return t[0].replace(/```(?:javascript|js)?\n?/gi,"").replace(/```$/g,"").trim();let r=/`([^`]+)`/g,n=s.match(r);return n&&n.length>0?n.map(o=>o.replace(/`/g,"")).join(`
`):s.trim()}generateSuggestions(s,e,t){let r=[];return e.suggestions&&r.push(...e.suggestions),t.suggestions&&r.push(...t.suggestions),s.includes("use strict")||r.push('\u5EFA\u8BAE\u6DFB\u52A0 "use strict" \u4EE5\u542F\u7528\u4E25\u683C\u6A21\u5F0F'),s.includes("var ")&&r.push("\u5EFA\u8BAE\u4F7F\u7528 let \u6216 const \u66FF\u4EE3 var"),s.includes("==")&&!s.includes("===")&&r.push("\u5EFA\u8BAE\u4F7F\u7528 === \u66FF\u4EE3 == \u8FDB\u884C\u4E25\u683C\u6BD4\u8F83"),r}},we=class{async check(s){let e={valid:!0,errors:[],warnings:[],suggestions:[]};try{new Function(s)}catch(t){e.valid=!1,e.errors.push(`\u8BED\u6CD5\u9519\u8BEF: ${t.message}`)}return this.checkCommonIssues(s,e),e}checkCommonIssues(s,e){let t={"(":")","[":"]","{":"}"},r=[];for(let m of s)if(m in t)r.push(m);else if(Object.values(t).includes(m)){let p=r.pop();if(!p||t[p]!==m){e.warnings.push("\u53EF\u80FD\u5B58\u5728\u672A\u5339\u914D\u7684\u62EC\u53F7");break}}r.length>0&&e.warnings.push("\u53EF\u80FD\u5B58\u5728\u672A\u95ED\u5408\u7684\u62EC\u53F7");let n=/\b([a-zA-Z_$][a-zA-Z0-9_$]*)\s*(?=\s*[^=]|$)/g,o=new Set,i=new Set,a=/(?:var|let|const)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,c;for(;(c=a.exec(s))!==null;)o.add(c[1]);let u=/function\s*\w*\s*\(([^)]*)\)/g;for(;(c=u.exec(s))!==null;)c[1].split(",").map(p=>p.trim().split(/\s+/)[0]).forEach(p=>{p&&o.add(p)});for(;(c=n.exec(s))!==null;){let m=c[1];this.isBuiltinOrGlobal(m)||i.add(m)}for(let m of i)o.has(m)||e.warnings.push(`\u53EF\u80FD\u4F7F\u7528\u4E86\u672A\u5B9A\u4E49\u7684\u53D8\u91CF: ${m}`)}isBuiltinOrGlobal(s){return["console","Math","Date","Array","Object","String","Number","Boolean","JSON","RegExp","Error","parseInt","parseFloat","isNaN","isFinite","undefined","null","true","false","Infinity","NaN"].includes(s)}},Te=class{constructor(){this.dangerousPatterns=[{pattern:/eval\s*\(/,risk:"\u4F7F\u7528eval\u51FD\u6570\u53EF\u80FD\u5BFC\u81F4\u4EE3\u7801\u6CE8\u5165",severity:"high"},{pattern:/Function\s*\(/,risk:"\u4F7F\u7528Function\u6784\u9020\u51FD\u6570\u53EF\u80FD\u4E0D\u5B89\u5168",severity:"high"},{pattern:/setTimeout\s*\(.*string/,risk:"\u4F7F\u7528\u5B57\u7B26\u4E32\u4F5C\u4E3AsetTimeout\u53C2\u6570\u4E0D\u5B89\u5168",severity:"medium"},{pattern:/setInterval\s*\(.*string/,risk:"\u4F7F\u7528\u5B57\u7B26\u4E32\u4F5C\u4E3AsetInterval\u53C2\u6570\u4E0D\u5B89\u5168",severity:"medium"},{pattern:/document\.write/,risk:"document.write\u53EF\u80FD\u5BFC\u81F4XSS",severity:"medium"},{pattern:/innerHTML\s*=/,risk:"\u76F4\u63A5\u8BBE\u7F6EinnerHTML\u53EF\u80FD\u5BFC\u81F4XSS",severity:"medium"},{pattern:/while\s*\(\s*true\s*\)/,risk:"\u65E0\u9650\u5FAA\u73AF\u53EF\u80FD\u5BFC\u81F4\u7A0B\u5E8F\u5361\u6B7B",severity:"high"},{pattern:/for\s*\(\s*;\s*;\s*\)/,risk:"\u65E0\u9650\u5FAA\u73AF\u53EF\u80FD\u5BFC\u81F4\u7A0B\u5E8F\u5361\u6B7B",severity:"high"}]}async analyze(s){let e={safe:!0,risks:[],warnings:[],suggestions:[]};for(let{pattern:r,risk:n,severity:o}of this.dangerousPatterns)r.test(s)&&(o==="high"?(e.safe=!1,e.risks.push(n)):e.warnings.push(n));return this.calculateComplexity(s)>20&&e.warnings.push("\u4EE3\u7801\u590D\u6742\u5EA6\u8F83\u9AD8\uFF0C\u5EFA\u8BAE\u7B80\u5316"),s.includes("innerHTML")&&e.suggestions.push("\u5EFA\u8BAE\u4F7F\u7528textContent\u6216\u5B89\u5168\u7684DOM\u64CD\u4F5C\u65B9\u6CD5"),s.includes("eval")&&e.suggestions.push("\u5EFA\u8BAE\u907F\u514D\u4F7F\u7528eval\uFF0C\u8003\u8651\u4F7F\u7528JSON.parse\u6216\u5176\u4ED6\u5B89\u5168\u65B9\u6CD5"),e}calculateComplexity(s){let e=1;return e+=(s.match(/if\s*\(/g)||[]).length,e+=(s.match(/else\s+if/g)||[]).length,e+=(s.match(/switch\s*\(/g)||[]).length,e+=(s.match(/case\s+/g)||[]).length,e+=(s.match(/for\s*\(/g)||[]).length,e+=(s.match(/while\s*\(/g)||[]).length,e+=(s.match(/do\s*{/g)||[]).length,e+=(s.match(/&&/g)||[]).length,e+=(s.match(/\|\|/g)||[]).length,e+=(s.match(/try\s*{/g)||[]).length,e+=(s.match(/catch\s*\(/g)||[]).length,e}},Ce=class{async format(s){let e=s;return e=this.addIndentation(e),e=this.addSemicolons(e),e}addIndentation(s){let e=s.split(`
`),t=0,r=2;return e.map(n=>{let o=n.trim();if(!o)return"";(o.startsWith("}")||o.startsWith("]")||o.startsWith(")"))&&(t=Math.max(0,t-1));let i=" ".repeat(t*r)+o;return(o.endsWith("{")||o.endsWith("[")||o.endsWith("("))&&t++,i}).join(`
`)}addSemicolons(s){return s.replace(/([^;{}\s])\s*\n/g,`$1;
`)}};var de=class extends v{constructor(e){super({name:"javascript_executor",description:"\u5728\u5B89\u5168\u6C99\u7BB1\u4E2D\u6267\u884CJavaScript\u4EE3\u7801",category:"javascript",version:"1.0.0",parameters:{type:"object",properties:{code:{type:"string",description:"\u8981\u6267\u884C\u7684JavaScript\u4EE3\u7801"},context:{type:"object",description:"\u6267\u884C\u4E0A\u4E0B\u6587\u53D8\u91CF"},timeout:{type:"number",description:"\u6267\u884C\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09",default:5e3,minimum:1e3,maximum:3e4},validateOnly:{type:"boolean",description:"\u4EC5\u9A8C\u8BC1\u4EE3\u7801\uFF0C\u4E0D\u6267\u884C",default:!1},formatCode:{type:"boolean",description:"\u662F\u5426\u683C\u5F0F\u5316\u4EE3\u7801",default:!0},allowDangerous:{type:"boolean",description:"\u662F\u5426\u5141\u8BB8\u6F5C\u5728\u5371\u9669\u7684\u64CD\u4F5C",default:!1}},required:["code"]},permissions:{required:["javascript_execute"],optional:["javascript_dangerous"],dangerous:!0,requiresConfirmation:!0},metadata:{author:"AI Coach Team",tags:["javascript","execution","sandbox"],documentation:"\u5728\u5B89\u5168\u7684\u6C99\u7BB1\u73AF\u5883\u4E2D\u6267\u884CJavaScript\u4EE3\u7801",examples:[{name:"\u7B80\u5355\u8BA1\u7B97",description:"\u6267\u884C\u7B80\u5355\u7684\u6570\u5B66\u8BA1\u7B97",input:{code:"const result = 2 + 3; console.log(result); result;"},expectedOutput:{result:5,output:"5"}},{name:"\u6570\u7EC4\u64CD\u4F5C",description:"\u5BF9\u6570\u7EC4\u8FDB\u884C\u64CD\u4F5C",input:{code:"const arr = [1, 2, 3]; const doubled = arr.map(x => x * 2); doubled;",context:{}},expectedOutput:{result:[2,4,6]}}]}});l(this,"sandbox");l(this,"validator");l(this,"executionHistory",[]);l(this,"maxHistorySize",50);this.sandbox=new V(e),this.validator=new me}async executeInternal(e,t){let r=Date.now();try{let{code:n,context:o={},timeout:i=5e3,validateOnly:a=!1,formatCode:c=!0,allowDangerous:u=!1}=e,m={format:c,allowDangerousPatterns:u},p=await this.validator.validateCode(n,m);if(!p.valid){let S=this.createExecutionRecord(n,{success:!1,error:p.errors.join("; "),executionTime:Date.now()-r},t);return this.addToHistory(S),{success:!1,error:`\u4EE3\u7801\u9A8C\u8BC1\u5931\u8D25: ${p.errors.join("; ")}`,data:{validation:p},metadata:{executionTime:Date.now()-r,validationOnly:!0}}}if(a)return{success:!0,data:{validation:p,processedCode:p.processedCode},metadata:{executionTime:Date.now()-r,validationOnly:!0}};if(p.warnings.length>0&&!u&&t&&!t.permissions.includes("javascript_dangerous"))return{success:!1,error:"\u4EE3\u7801\u5305\u542B\u6F5C\u5728\u98CE\u9669\uFF0C\u9700\u8981\u5371\u9669\u64CD\u4F5C\u6743\u9650",data:{validation:p,warnings:p.warnings}};this.sandbox=new V({timeout:i});let f=await this.sandbox.execute(p.processedCode,o),w=this.createExecutionRecord(n,f,t);return this.addToHistory(w),{success:f.success,data:{result:f.result,output:f.output,validation:p,processedCode:p.processedCode},error:f.error,metadata:{executionTime:f.executionTime,memoryUsed:f.memoryUsed,codeLength:n.length,processedCodeLength:p.processedCode.length}}}catch(n){let o=this.createExecutionRecord(e.code,{success:!1,error:n.message,executionTime:Date.now()-r},t);return this.addToHistory(o),{success:!1,error:`JavaScript\u6267\u884C\u5931\u8D25: ${n.message}`,metadata:{executionTime:Date.now()-r,errorType:"execution_error"}}}}async validateInternal(e){let t=[],r=[],{code:n,timeout:o}=e;return!n||typeof n!="string"?t.push("\u4EE3\u7801\u53C2\u6570\u5FC5\u987B\u662F\u975E\u7A7A\u5B57\u7B26\u4E32"):(n.length>1e5&&t.push("\u4EE3\u7801\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7100KB"),n.length<5&&r.push("\u4EE3\u7801\u8FC7\u77ED\uFF0C\u53EF\u80FD\u65E0\u6CD5\u6267\u884C\u6709\u610F\u4E49\u7684\u64CD\u4F5C")),o&&(o<1e3||o>3e4)&&t.push("\u8D85\u65F6\u65F6\u95F4\u5FC5\u987B\u57281000-30000\u6BEB\u79D2\u4E4B\u95F4"),{valid:t.length===0,errors:t,warnings:r}}createExecutionRecord(e,t,r){return{id:this.generateExecutionId(),code:e.substring(0,1e3),result:t,timestamp:new Date,userId:(r==null?void 0:r.userId)||"anonymous",sessionId:r==null?void 0:r.sessionId}}addToHistory(e){this.executionHistory.push(e),this.executionHistory.length>this.maxHistorySize&&(this.executionHistory=this.executionHistory.slice(-this.maxHistorySize))}generateExecutionId(){return`js_exec_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}getExecutionHistory(e){let t=[...this.executionHistory].reverse();return e?t.slice(0,e):t}getExecutionStats(){let e=this.executionHistory.length,t=this.executionHistory.filter(i=>i.result.success).length,r=e-t,n=e>0?this.executionHistory.reduce((i,a)=>i+a.result.executionTime,0)/e:0,o=this.executionHistory.reduce((i,a)=>i+a.code.length,0);return{totalExecutions:e,successfulExecutions:t,failedExecutions:r,averageExecutionTime:Math.round(n),totalCodeLength:o}}clearExecutionHistory(){this.executionHistory=[]}async getCodeSuggestions(e){return(await this.validator.validateCode(e,{format:!1})).suggestions}async formatCode(e){return(await this.validator.validateCode(e,{format:!0})).processedCode}};var pe=class extends x.Plugin{constructor(){super(...arguments);this.config=M;this.chatModal=null;this.statusBarManager=null;this.initialized=!1}async onload(){console.log("Loading AI Coach Advanced Plugin...");try{await this.initializeCore(),this.registerCommands(),this.addSettingTab(new se(this.app,this)),this.statusBarManager=new re(this.addStatusBarItem(),this.orchestrationEngine),this.statusBarManager.initialize(),this.registerVaultEvents(),console.log("AI Coach Advanced Plugin loaded successfully"),new x.Notice("AI Coach Advanced \u63D2\u4EF6\u5DF2\u52A0\u8F7D")}catch(e){console.error("Failed to load AI Coach Advanced Plugin:",e),new x.Notice("AI Coach Advanced \u63D2\u4EF6\u52A0\u8F7D\u5931\u8D25: "+e.message)}}async onunload(){var e;console.log("Unloading AI Coach Advanced Plugin...");try{this.chatModal&&this.chatModal.close(),this.orchestrationEngine&&await this.orchestrationEngine.cleanup(),this.statusBarManager&&this.statusBarManager.cleanup(),await((e=this.configManager)==null?void 0:e.saveConfig()),this.initialized=!1,console.log("AI Coach Advanced Plugin unloaded successfully")}catch(t){console.error("Error during plugin unload:",t)}}async initializeCore(){this.configManager=new Q(this),this.config=await this.configManager.loadConfig(),this.orchestrationEngine=new ee(this.app,this.configManager),await this.registerTools(),await this.orchestrationEngine.initialize(),this.initialized=!0}async registerTools(){try{let e=new ae(this.app);await this.orchestrationEngine.registerTool(e);let t=new le(this.app);await this.orchestrationEngine.registerTool(t);let r=new ue;await this.orchestrationEngine.registerTool(r);let n=new de;await this.orchestrationEngine.registerTool(n);let o=new _(this.app),i=new O(this.app);await this.orchestrationEngine.registerTool(o),await this.orchestrationEngine.registerTool(i),console.log("All tools registered successfully")}catch(e){throw console.error("Failed to register tools:",e),e}}registerCommands(){this.addCommand({id:"open-ai-chat",name:"\u6253\u5F00AI\u52A9\u624B\u5BF9\u8BDD",callback:()=>this.openChatModal()}),this.addCommand({id:"quick-query",name:"\u5FEB\u901F\u67E5\u8BE2",callback:()=>this.quickQuery()}),this.addCommand({id:"rebuild-vault-index",name:"\u91CD\u5EFAVault\u7D22\u5F15",callback:()=>this.rebuildVaultIndex()}),this.addCommand({id:"clear-conversation-history",name:"\u6E05\u7406\u5BF9\u8BDD\u5386\u53F2",callback:()=>this.clearConversationHistory()})}registerVaultEvents(){this.registerEvent(this.app.vault.on("create",e=>{e instanceof x.TFile&&e.extension==="md"&&this.onFileChanged(e)})),this.registerEvent(this.app.vault.on("modify",e=>{e instanceof x.TFile&&e.extension==="md"&&this.onFileChanged(e)})),this.registerEvent(this.app.vault.on("delete",e=>{e instanceof x.TFile&&e.extension==="md"&&this.onFileDeleted(e)}))}async onFileChanged(e){var t,r;if((r=(t=this.config.tools)==null?void 0:t.vault)!=null&&r.indexingEnabled&&this.initialized)try{let n=this.getVaultQueryTool();n&&(await n.updateFileIndex(e.path),console.log(`Updated index for file: ${e.path}`))}catch(n){console.error(`Failed to update index for ${e.path}:`,n)}}async onFileDeleted(e){var t,r;if((r=(t=this.config.tools)==null?void 0:t.vault)!=null&&r.indexingEnabled&&this.initialized)try{let n=this.getVaultQueryTool();n&&(await n.deleteFileIndex(e.path),console.log(`Deleted index for file: ${e.path}`))}catch(n){console.error(`Failed to delete index for ${e.path}:`,n)}}getVaultQueryTool(){return this.orchestrationEngine.getAvailableTools().find(t=>t.name==="vault_query")}async openChatModal(){if(!this.initialized){new x.Notice("AI Coach \u5C1A\u672A\u521D\u59CB\u5316\u5B8C\u6210");return}this.chatModal||(this.chatModal=new te(this.app,this.orchestrationEngine)),this.chatModal.open()}async quickQuery(){new x.Notice("\u5FEB\u901F\u67E5\u8BE2\u529F\u80FD\u5F00\u53D1\u4E2D...")}async rebuildVaultIndex(){try{new x.Notice("\u5F00\u59CB\u91CD\u5EFAVault\u7D22\u5F15...");let e=this.getVaultQueryTool();if(e){await e.rebuildIndex(),new x.Notice("Vault\u7D22\u5F15\u91CD\u5EFA\u5B8C\u6210");let t=await e.getIndexStats();console.log("Index stats:",t)}else throw new Error("Vault\u67E5\u8BE2\u5DE5\u5177\u672A\u627E\u5230")}catch(e){console.error("Failed to rebuild vault index:",e),new x.Notice("\u7D22\u5F15\u91CD\u5EFA\u5931\u8D25: "+e.message)}}async clearConversationHistory(){try{await this.orchestrationEngine.endCurrentConversation(),new x.Notice("\u5BF9\u8BDD\u5386\u53F2\u5DF2\u6E05\u7406")}catch(e){console.error("Failed to clear conversation history:",e),new x.Notice("\u6E05\u7406\u5BF9\u8BDD\u5386\u53F2\u5931\u8D25: "+e.message)}}getConfig(){return this.config}async updateConfig(e){this.configManager.updateConfig(e),await this.configManager.saveConfig(),this.config=this.configManager.getConfig(),e.llm&&await this.orchestrationEngine.updateConfig(e.llm)}getOrchestrationEngine(){return this.orchestrationEngine}getConfigManager(){return this.configManager}isInitialized(){return this.initialized}};
