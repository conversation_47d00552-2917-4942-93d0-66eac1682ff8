const fs = require('fs');
const path = require('path');
const yauzl = require('yauzl');

async function detailedVerify() {
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const version = packageJson.version;
    const packagePath = path.join('releases', `ai-coach-advanced-v${version}.zip`);
    
    console.log(`🔍 详细验证包文件: ${packagePath}`);
    
    // 检查本地dist目录
    console.log('\n📁 本地dist目录内容:');
    const distFiles = fs.readdirSync('dist');
    distFiles.forEach(file => {
      const filePath = path.join('dist', file);
      const stats = fs.statSync(filePath);
      console.log(`  📄 ${file} (${(stats.size / 1024).toFixed(1)} KB)`);
    });
    
    // 检查zip包内容
    yauzl.open(packagePath, { lazyEntries: true }, (err, zipfile) => {
      if (err) {
        console.error('❌ 无法打开zip文件:', err);
        return;
      }
      
      const files = [];
      
      zipfile.readEntry();
      zipfile.on('entry', (entry) => {
        files.push({
          name: entry.fileName,
          size: entry.uncompressedSize,
          compressedSize: entry.compressedSize
        });
        zipfile.readEntry();
      });
      
      zipfile.on('end', () => {
        console.log('\n📦 ZIP包内容详情:');
        files.sort((a, b) => a.name.localeCompare(b.name)).forEach(file => {
          const sizeKB = (file.size / 1024).toFixed(1);
          const compressedKB = (file.compressedSize / 1024).toFixed(1);
          console.log(`  📄 ${file.name} (${sizeKB} KB → ${compressedKB} KB)`);
        });
        
        // 检查main.js
        const mainJs = files.find(f => f.name === 'main.js');
        if (mainJs) {
          console.log('\n✅ main.js文件详情:');
          console.log(`  📏 原始大小: ${(mainJs.size / 1024).toFixed(1)} KB`);
          console.log(`  📦 压缩大小: ${(mainJs.compressedSize / 1024).toFixed(1)} KB`);
          console.log(`  📊 压缩率: ${((1 - mainJs.compressedSize / mainJs.size) * 100).toFixed(1)}%`);
          
          // 与本地文件比较
          const localMainJs = path.join('dist', 'main.js');
          if (fs.existsSync(localMainJs)) {
            const localStats = fs.statSync(localMainJs);
            console.log(`  🔄 本地文件大小: ${(localStats.size / 1024).toFixed(1)} KB`);
            
            if (localStats.size === mainJs.size) {
              console.log('  ✅ 文件大小匹配！');
            } else {
              console.log('  ⚠️ 文件大小不匹配！');
            }
          }
        } else {
          console.log('\n❌ main.js文件未找到！');
        }
        
        // 统计信息
        const totalSize = files.reduce((sum, f) => sum + f.size, 0);
        const totalCompressed = files.reduce((sum, f) => sum + f.compressedSize, 0);
        
        console.log('\n📊 包统计信息:');
        console.log(`  📄 文件总数: ${files.length}`);
        console.log(`  📏 总大小: ${(totalSize / 1024).toFixed(1)} KB`);
        console.log(`  📦 压缩后: ${(totalCompressed / 1024).toFixed(1)} KB`);
        console.log(`  📊 总压缩率: ${((1 - totalCompressed / totalSize) * 100).toFixed(1)}%`);
        
        // 检查关键文件
        const criticalFiles = ['main.js', 'manifest.json', 'styles.css'];
        console.log('\n🔑 关键文件检查:');
        criticalFiles.forEach(fileName => {
          const file = files.find(f => f.name === fileName);
          if (file) {
            console.log(`  ✅ ${fileName} - ${(file.size / 1024).toFixed(1)} KB`);
          } else {
            console.log(`  ❌ ${fileName} - 缺失！`);
          }
        });
      });
    });
    
  } catch (error) {
    console.error('❌ 验证失败:', error);
  }
}

detailedVerify();
