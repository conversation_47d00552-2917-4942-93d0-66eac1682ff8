import { App } from 'obsidian';
import { BaseTool, ToolConfig } from '@/core/tools/BaseTool';
import { ToolResult, ToolExecutionContext } from '@/types/tools';
import { ConversationContext, ConversationMessage, ConversationMetadata } from '@/types/memory';

/**
 * 短期记忆管理工具
 * 管理对话上下文和会话状态
 */
export class ShortTermMemoryTool extends BaseTool {
  private app: App;
  private conversations: Map<string, ConversationContext> = new Map();
  private maxConversations: number = 100;
  private maxMessagesPerConversation: number = 50;

  constructor(app: App) {
    const config: ToolConfig = {
      name: 'short_term_memory',
      description: '管理短期对话记忆和会话上下文',
      category: 'memory',
      version: '1.0.0',
      parameters: {
        type: 'object',
        properties: {
          action: {
            type: 'string',
            description: '操作类型',
            enum: ['create', 'get', 'update', 'delete', 'list', 'add_message', 'get_context']
          },
          conversationId: {
            type: 'string',
            description: '对话ID'
          },
          message: {
            type: 'object',
            description: '消息对象',
            properties: {
              role: { type: 'string', enum: ['user', 'assistant', 'system'] },
              content: { type: 'string' },
              metadata: { type: 'object' }
            }
          },
          metadata: {
            type: 'object',
            description: '对话元数据'
          },
          limit: {
            type: 'number',
            description: '限制返回数量',
            default: 10,
            minimum: 1,
            maximum: 100
          }
        },
        required: ['action']
      },
      permissions: {
        required: ['memory_read', 'memory_write'],
        optional: [],
        dangerous: false,
        requiresConfirmation: false
      },
      metadata: {
        author: 'AI Coach Team',
        tags: ['memory', 'conversation', 'context'],
        documentation: '管理短期对话记忆，维护会话上下文',
        examples: [
          {
            name: '创建新对话',
            description: '创建一个新的对话会话',
            input: { action: 'create', metadata: { title: 'New Chat' } },
            expectedOutput: { conversationId: 'uuid' }
          },
          {
            name: '添加消息',
            description: '向对话中添加新消息',
            input: { 
              action: 'add_message', 
              conversationId: 'uuid',
              message: { role: 'user', content: 'Hello' }
            },
            expectedOutput: { success: true }
          }
        ]
      }
    };

    super(config);
    this.app = app;
  }

  protected async executeInternal(
    args: Record<string, any>,
    context?: ToolExecutionContext
  ): Promise<ToolResult> {
    const { action } = args;

    try {
      switch (action) {
        case 'create':
          return await this.createConversation(args);
        case 'get':
          return await this.getConversation(args);
        case 'update':
          return await this.updateConversation(args);
        case 'delete':
          return await this.deleteConversation(args);
        case 'list':
          return await this.listConversations(args);
        case 'add_message':
          return await this.addMessage(args);
        case 'get_context':
          return await this.getContext(args);
        default:
          return {
            success: false,
            error: `未知操作: ${action}`
          };
      }
    } catch (error) {
      return {
        success: false,
        error: `短期记忆操作失败: ${error.message}`
      };
    }
  }

  /**
   * 创建新对话
   */
  private async createConversation(args: Record<string, any>): Promise<ToolResult> {
    const { metadata = {} } = args;
    
    const conversationId = this.generateConversationId();
    const now = new Date();
    
    const conversation: ConversationContext = {
      id: conversationId,
      messages: [],
      metadata: {
        title: metadata.title || `对话 ${now.toLocaleString()}`,
        tags: metadata.tags || [],
        summary: metadata.summary,
        toolsUsed: [],
        filesReferenced: []
      },
      createdAt: now,
      updatedAt: now
    };

    this.conversations.set(conversationId, conversation);
    this.cleanupOldConversations();

    return {
      success: true,
      data: {
        conversationId,
        conversation
      }
    };
  }

  /**
   * 获取对话
   */
  private async getConversation(args: Record<string, any>): Promise<ToolResult> {
    const { conversationId } = args;
    
    if (!conversationId) {
      return {
        success: false,
        error: '缺少对话ID'
      };
    }

    const conversation = this.conversations.get(conversationId);
    if (!conversation) {
      return {
        success: false,
        error: `对话不存在: ${conversationId}`
      };
    }

    return {
      success: true,
      data: { conversation }
    };
  }

  /**
   * 更新对话元数据
   */
  private async updateConversation(args: Record<string, any>): Promise<ToolResult> {
    const { conversationId, metadata } = args;
    
    if (!conversationId) {
      return {
        success: false,
        error: '缺少对话ID'
      };
    }

    const conversation = this.conversations.get(conversationId);
    if (!conversation) {
      return {
        success: false,
        error: `对话不存在: ${conversationId}`
      };
    }

    // 更新元数据
    if (metadata) {
      conversation.metadata = { ...conversation.metadata, ...metadata };
      conversation.updatedAt = new Date();
    }

    return {
      success: true,
      data: { conversation }
    };
  }

  /**
   * 删除对话
   */
  private async deleteConversation(args: Record<string, any>): Promise<ToolResult> {
    const { conversationId } = args;
    
    if (!conversationId) {
      return {
        success: false,
        error: '缺少对话ID'
      };
    }

    const deleted = this.conversations.delete(conversationId);
    
    return {
      success: deleted,
      data: { deleted }
    };
  }

  /**
   * 列出对话
   */
  private async listConversations(args: Record<string, any>): Promise<ToolResult> {
    const { limit = 10 } = args;
    
    const conversations = Array.from(this.conversations.values())
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
      .slice(0, limit);

    return {
      success: true,
      data: {
        conversations,
        total: this.conversations.size
      }
    };
  }

  /**
   * 添加消息
   */
  private async addMessage(args: Record<string, any>): Promise<ToolResult> {
    const { conversationId, message } = args;
    
    if (!conversationId || !message) {
      return {
        success: false,
        error: '缺少对话ID或消息内容'
      };
    }

    const conversation = this.conversations.get(conversationId);
    if (!conversation) {
      return {
        success: false,
        error: `对话不存在: ${conversationId}`
      };
    }

    const newMessage: ConversationMessage = {
      id: this.generateMessageId(),
      role: message.role,
      content: message.content,
      timestamp: new Date(),
      metadata: message.metadata || {}
    };

    conversation.messages.push(newMessage);
    conversation.updatedAt = new Date();

    // 限制消息数量
    if (conversation.messages.length > this.maxMessagesPerConversation) {
      conversation.messages = conversation.messages.slice(-this.maxMessagesPerConversation);
    }

    return {
      success: true,
      data: {
        messageId: newMessage.id,
        conversation
      }
    };
  }

  /**
   * 获取上下文
   */
  private async getContext(args: Record<string, any>): Promise<ToolResult> {
    const { conversationId, limit = 10 } = args;
    
    if (!conversationId) {
      return {
        success: false,
        error: '缺少对话ID'
      };
    }

    const conversation = this.conversations.get(conversationId);
    if (!conversation) {
      return {
        success: false,
        error: `对话不存在: ${conversationId}`
      };
    }

    const recentMessages = conversation.messages.slice(-limit);
    
    return {
      success: true,
      data: {
        conversationId,
        messages: recentMessages,
        metadata: conversation.metadata,
        messageCount: conversation.messages.length
      }
    };
  }

  /**
   * 生成对话ID
   */
  private generateConversationId(): string {
    return `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 清理旧对话
   */
  private cleanupOldConversations(): void {
    if (this.conversations.size <= this.maxConversations) {
      return;
    }

    const conversations = Array.from(this.conversations.entries())
      .sort(([, a], [, b]) => a.updatedAt.getTime() - b.updatedAt.getTime());

    const toDelete = conversations.slice(0, conversations.length - this.maxConversations);
    toDelete.forEach(([id]) => {
      this.conversations.delete(id);
    });
  }

  /**
   * 获取统计信息
   */
  getStats(): {
    totalConversations: number;
    totalMessages: number;
    averageMessagesPerConversation: number;
    oldestConversation: Date | null;
    newestConversation: Date | null;
  } {
    const conversations = Array.from(this.conversations.values());
    const totalMessages = conversations.reduce((sum, conv) => sum + conv.messages.length, 0);
    
    const dates = conversations.map(conv => conv.updatedAt);
    const oldestDate = dates.length > 0 ? new Date(Math.min(...dates.map(d => d.getTime()))) : null;
    const newestDate = dates.length > 0 ? new Date(Math.max(...dates.map(d => d.getTime()))) : null;

    return {
      totalConversations: conversations.length,
      totalMessages,
      averageMessagesPerConversation: conversations.length > 0 ? totalMessages / conversations.length : 0,
      oldestConversation: oldestDate,
      newestConversation: newestDate
    };
  }

  /**
   * 清空所有对话
   */
  clearAll(): void {
    this.conversations.clear();
  }

  /**
   * 搜索对话
   */
  searchConversations(query: string, limit: number = 10): ConversationContext[] {
    const queryLower = query.toLowerCase();
    const results: Array<{ conversation: ConversationContext; score: number }> = [];

    for (const conversation of this.conversations.values()) {
      let score = 0;

      // 搜索标题
      if (conversation.metadata.title?.toLowerCase().includes(queryLower)) {
        score += 10;
      }

      // 搜索消息内容
      for (const message of conversation.messages) {
        if (message.content.toLowerCase().includes(queryLower)) {
          score += 1;
        }
      }

      // 搜索标签
      if (conversation.metadata.tags.some(tag => tag.toLowerCase().includes(queryLower))) {
        score += 5;
      }

      if (score > 0) {
        results.push({ conversation, score });
      }
    }

    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(result => result.conversation);
  }
}
