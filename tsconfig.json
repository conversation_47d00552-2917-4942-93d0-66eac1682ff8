{"compilerOptions": {"baseUrl": ".", "inlineSourceMap": true, "inlineSources": true, "module": "ESNext", "target": "ES6", "allowJs": true, "noImplicitAny": true, "moduleResolution": "node", "importHelpers": true, "declaration": true, "outDir": "lib", "typeRoots": ["node_modules/@types"], "lib": ["DOM", "ES6"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "paths": {"@/*": ["src/*"], "@/types/*": ["src/types/*"], "@/core/*": ["src/core/*"], "@/tools/*": ["src/tools/*"], "@/ui/*": ["src/ui/*"], "@/utils/*": ["src/utils/*"]}}, "include": ["**/*.ts"], "exclude": ["node_modules", "lib", "**/*.test.ts"]}