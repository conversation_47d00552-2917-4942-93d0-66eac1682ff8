# AI Coach Advanced - 文件操作指南

## 🗂️ 概述

AI Coach Advanced 现在包含强大的文件操作功能，让你可以通过自然语言指令来管理Obsidian中的文件和文件夹。

## 🛠️ 可用工具

### 1. 文件操作工具 (file_operation)
**功能**: 创建、读取、编辑、删除、移动文件和文件夹

**支持的操作**:
- `create` - 创建新文件
- `read` - 读取文件内容
- `update` - 更新文件内容
- `delete` - 删除文件或文件夹
- `move` - 移动/重命名文件
- `list` - 列出文件和文件夹
- `mkdir` - 创建文件夹

### 2. 笔记模板工具 (note_template)
**功能**: 使用预定义模板快速创建结构化笔记

**支持的模板**:
- `daily` - 日记模板
- `meeting` - 会议记录模板
- `project` - 项目管理模板
- `book` - 读书笔记模板
- `idea` - 想法记录模板
- `todo` - 任务清单模板
- `journal` - 日志模板
- `research` - 研究笔记模板

## 📝 使用示例

### 基础文件操作

#### 创建文件
```
用户: 帮我创建一个名为"学习计划"的文件
AI: [调用file_operation工具] → 创建学习计划.md文件
```

#### 编辑文件
```
用户: 在"学习计划.md"文件中添加今天的学习任务
AI: [调用file_operation工具] → 读取并更新文件内容
```

#### 删除文件
```
用户: 删除"临时笔记.md"文件
AI: [调用file_operation工具] → 删除指定文件
```

#### 创建文件夹
```
用户: 创建一个名为"项目文档"的文件夹
AI: [调用file_operation工具] → 创建新文件夹
```

#### 移动文件
```
用户: 将"会议记录.md"移动到"工作"文件夹中
AI: [调用file_operation工具] → 移动文件到指定位置
```

#### 列出文件
```
用户: 显示"项目"文件夹中的所有文件
AI: [调用file_operation工具] → 列出文件夹内容
```

### 模板化笔记创建

#### 创建日记
```
用户: 创建今天的日记
AI: [调用note_template工具] → 使用daily模板创建日记
```

#### 创建会议记录
```
用户: 创建一个关于"产品规划"的会议记录
AI: [调用note_template工具] → 使用meeting模板创建会议记录
```

#### 创建项目笔记
```
用户: 为"网站重构"项目创建项目管理笔记
AI: [调用note_template工具] → 使用project模板创建项目笔记
```

#### 创建读书笔记
```
用户: 为《深度工作》这本书创建读书笔记
AI: [调用note_template工具] → 使用book模板创建读书笔记
```

## 🎯 高级用法

### 批量操作
```
用户: 在"学习"文件夹中创建本周的日记文件
AI: [组合使用file_operation和note_template] → 创建多个日记文件
```

### 内容管理
```
用户: 读取"项目计划.md"的内容，然后基于它创建一个任务清单
AI: [先读取文件，再创建todo模板] → 智能内容转换
```

### 文件整理
```
用户: 将所有以"会议"开头的文件移动到"会议记录"文件夹
AI: [列出文件，筛选，批量移动] → 自动文件整理
```

## 📋 模板详情

### 日记模板 (daily)
包含：
- 日期和天气
- 今日计划
- 重要事件
- 学习收获
- 反思总结
- 明日计划

### 会议记录模板 (meeting)
包含：
- 会议基本信息
- 议程
- 讨论要点
- 决策事项
- 行动计划
- 后续跟进

### 项目管理模板 (project)
包含：
- 项目概述
- 目标和里程碑
- 任务分解
- 资源需求
- 风险评估
- 项目日志

### 读书笔记模板 (book)
包含：
- 书籍基本信息
- 内容概要
- 章节笔记
- 重要观点
- 金句摘录
- 个人思考
- 行动计划

## 🔧 技术细节

### 文件路径规则
- 相对于Vault根目录
- 自动添加.md扩展名（如果未指定）
- 支持子文件夹路径（如"项目/网站重构.md"）

### 安全特性
- 文件存在性检查
- 覆盖保护
- 递归删除确认
- 路径验证

### 错误处理
- 友好的错误消息
- 操作失败回滚
- 权限检查
- 文件锁定检测

## 🎨 自定义选项

### 模板自定义字段
创建模板时可以提供自定义字段：

```json
{
  "templateType": "daily",
  "title": "2024年6月17日日记",
  "customFields": {
    "weather": "晴天",
    "mood": "愉快",
    "location": "家中"
  }
}
```

### 标签支持
为创建的笔记添加标签：

```json
{
  "templateType": "project",
  "title": "网站重构项目",
  "tags": ["项目", "技术", "重构"]
}
```

## 🚀 最佳实践

### 1. 文件命名
- 使用有意义的文件名
- 避免特殊字符
- 考虑使用日期前缀

### 2. 文件夹结构
- 建立清晰的层级结构
- 按类型或项目分组
- 定期整理和清理

### 3. 模板使用
- 选择合适的模板类型
- 填写完整的基本信息
- 根据需要自定义字段

### 4. 内容管理
- 定期备份重要文件
- 使用版本控制
- 建立命名规范

## 🔍 故障排除

### 常见问题

#### 文件创建失败
- 检查文件名是否包含非法字符
- 确认目标文件夹存在
- 验证是否有写入权限

#### 模板应用失败
- 确认模板类型正确
- 检查必需字段是否提供
- 验证自定义字段格式

#### 文件操作权限错误
- 检查文件是否被其他程序占用
- 确认Obsidian有足够权限
- 尝试重启Obsidian

### 调试方法
1. 查看浏览器控制台日志
2. 检查Obsidian通知消息
3. 验证文件系统状态
4. 测试简单操作

## 📞 获取帮助

如果遇到问题：
1. 查看详细的调试日志
2. 尝试简化操作步骤
3. 检查文件权限设置
4. 通过GitHub Issues报告问题

---

通过这些强大的文件操作功能，AI Coach Advanced 让你能够更高效地管理Obsidian中的内容，无论是日常笔记还是复杂的项目文档！🎉
