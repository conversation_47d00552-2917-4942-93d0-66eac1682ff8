import { App, TFile, Notice } from 'obsidian';
import { BaseTool, ToolConfig } from '@/core/tools/BaseTool';
import { ToolResult, ToolExecutionContext } from '@/types/tools';
import { UserPreference } from '@/types/memory';

/**
 * 长期记忆管理工具
 * 管理用户偏好和持久化信息存储
 */
export class LongTermMemoryTool extends BaseTool {
  private app: App;
  private preferencesFile: string = '.ai-coach/preferences.json';
  private memoryFolder: string = '.ai-coach/memory';
  private preferences: Map<string, UserPreference> = new Map();
  private initialized: boolean = false;

  constructor(app: App) {
    const config: ToolConfig = {
      name: 'long_term_memory',
      description: '管理长期用户偏好和持久化记忆存储',
      category: 'memory',
      version: '1.0.0',
      parameters: {
        type: 'object',
        properties: {
          action: {
            type: 'string',
            description: '操作类型',
            enum: ['set', 'get', 'delete', 'list', 'save_memory', 'get_memory', 'search_memory']
          },
          key: {
            type: 'string',
            description: '偏好设置的键名'
          },
          value: {
            description: '偏好设置的值'
          },
          type: {
            type: 'string',
            description: '值的类型',
            enum: ['string', 'number', 'boolean', 'object', 'array']
          },
          description: {
            type: 'string',
            description: '偏好设置的描述'
          },
          memoryId: {
            type: 'string',
            description: '记忆条目的ID'
          },
          content: {
            type: 'string',
            description: '记忆内容'
          },
          tags: {
            type: 'array',
            description: '记忆标签',
            items: { type: 'string' }
          },
          query: {
            type: 'string',
            description: '搜索查询'
          }
        },
        required: ['action']
      },
      permissions: {
        required: ['vault_write', 'vault_read'],
        optional: [],
        dangerous: false,
        requiresConfirmation: false
      },
      metadata: {
        author: 'AI Coach Team',
        tags: ['memory', 'preferences', 'storage'],
        documentation: '管理长期用户偏好和记忆存储',
        examples: [
          {
            name: '设置用户偏好',
            description: '保存用户的偏好设置',
            input: { 
              action: 'set', 
              key: 'preferred_language', 
              value: 'zh', 
              type: 'string',
              description: '用户首选语言'
            },
            expectedOutput: { success: true }
          },
          {
            name: '保存记忆',
            description: '保存重要的记忆信息',
            input: { 
              action: 'save_memory', 
              content: '用户喜欢使用Markdown格式',
              tags: ['preference', 'format']
            },
            expectedOutput: { memoryId: 'uuid' }
          }
        ]
      }
    };

    super(config);
    this.app = app;
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // 确保目录存在
      await this.ensureDirectories();
      
      // 加载偏好设置
      await this.loadPreferences();
      
      this.initialized = true;
      console.log('LongTermMemoryTool initialized successfully');
    } catch (error) {
      console.error('Failed to initialize LongTermMemoryTool:', error);
      throw error;
    }
  }

  protected async executeInternal(
    args: Record<string, any>,
    context?: ToolExecutionContext
  ): Promise<ToolResult> {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      const { action } = args;

      switch (action) {
        case 'set':
          return await this.setPreference(args);
        case 'get':
          return await this.getPreference(args);
        case 'delete':
          return await this.deletePreference(args);
        case 'list':
          return await this.listPreferences(args);
        case 'save_memory':
          return await this.saveMemory(args);
        case 'get_memory':
          return await this.getMemory(args);
        case 'search_memory':
          return await this.searchMemory(args);
        default:
          return {
            success: false,
            error: `未知操作: ${action}`
          };
      }
    } catch (error) {
      return {
        success: false,
        error: `长期记忆操作失败: ${error.message}`
      };
    }
  }

  /**
   * 设置偏好
   */
  private async setPreference(args: Record<string, any>): Promise<ToolResult> {
    const { key, value, type, description } = args;
    
    if (!key) {
      return {
        success: false,
        error: '缺少偏好键名'
      };
    }

    const preference: UserPreference = {
      key,
      value,
      type: type || typeof value,
      description,
      updatedAt: new Date()
    };

    this.preferences.set(key, preference);
    await this.savePreferences();

    return {
      success: true,
      data: { preference }
    };
  }

  /**
   * 获取偏好
   */
  private async getPreference(args: Record<string, any>): Promise<ToolResult> {
    const { key } = args;
    
    if (!key) {
      return {
        success: false,
        error: '缺少偏好键名'
      };
    }

    const preference = this.preferences.get(key);
    
    return {
      success: true,
      data: { 
        preference,
        exists: !!preference
      }
    };
  }

  /**
   * 删除偏好
   */
  private async deletePreference(args: Record<string, any>): Promise<ToolResult> {
    const { key } = args;
    
    if (!key) {
      return {
        success: false,
        error: '缺少偏好键名'
      };
    }

    const deleted = this.preferences.delete(key);
    if (deleted) {
      await this.savePreferences();
    }

    return {
      success: true,
      data: { deleted }
    };
  }

  /**
   * 列出偏好
   */
  private async listPreferences(): Promise<ToolResult> {
    const preferences = Array.from(this.preferences.values());
    
    return {
      success: true,
      data: {
        preferences,
        count: preferences.length
      }
    };
  }

  /**
   * 保存记忆
   */
  private async saveMemory(args: Record<string, any>): Promise<ToolResult> {
    const { content, tags = [], memoryId } = args;
    
    if (!content) {
      return {
        success: false,
        error: '缺少记忆内容'
      };
    }

    const id = memoryId || this.generateMemoryId();
    const fileName = `${this.memoryFolder}/${id}.md`;
    
    const memoryContent = this.formatMemoryContent(content, tags);
    
    try {
      await this.app.vault.adapter.write(fileName, memoryContent);
      
      return {
        success: true,
        data: {
          memoryId: id,
          fileName
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `保存记忆失败: ${error.message}`
      };
    }
  }

  /**
   * 获取记忆
   */
  private async getMemory(args: Record<string, any>): Promise<ToolResult> {
    const { memoryId } = args;
    
    if (!memoryId) {
      return {
        success: false,
        error: '缺少记忆ID'
      };
    }

    const fileName = `${this.memoryFolder}/${memoryId}.md`;
    
    try {
      const content = await this.app.vault.adapter.read(fileName);
      const parsed = this.parseMemoryContent(content);
      
      return {
        success: true,
        data: {
          memoryId,
          ...parsed
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `获取记忆失败: ${error.message}`
      };
    }
  }

  /**
   * 搜索记忆
   */
  private async searchMemory(args: Record<string, any>): Promise<ToolResult> {
    const { query } = args;
    
    if (!query) {
      return {
        success: false,
        error: '缺少搜索查询'
      };
    }

    try {
      const memoryFiles = await this.getMemoryFiles();
      const results = [];

      for (const file of memoryFiles) {
        const content = await this.app.vault.adapter.read(file);
        if (content.toLowerCase().includes(query.toLowerCase())) {
          const parsed = this.parseMemoryContent(content);
          const memoryId = file.replace(`${this.memoryFolder}/`, '').replace('.md', '');
          results.push({
            memoryId,
            ...parsed,
            fileName: file
          });
        }
      }

      return {
        success: true,
        data: {
          query,
          results,
          count: results.length
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `搜索记忆失败: ${error.message}`
      };
    }
  }

  /**
   * 确保目录存在
   */
  private async ensureDirectories(): Promise<void> {
    const adapter = this.app.vault.adapter;
    
    // 创建主目录
    if (!(await adapter.exists('.ai-coach'))) {
      await adapter.mkdir('.ai-coach');
    }
    
    // 创建记忆目录
    if (!(await adapter.exists(this.memoryFolder))) {
      await adapter.mkdir(this.memoryFolder);
    }
  }

  /**
   * 加载偏好设置
   */
  private async loadPreferences(): Promise<void> {
    try {
      const adapter = this.app.vault.adapter;
      if (await adapter.exists(this.preferencesFile)) {
        const content = await adapter.read(this.preferencesFile);
        const data = JSON.parse(content);
        
        for (const [key, preference] of Object.entries(data)) {
          this.preferences.set(key, preference as UserPreference);
        }
      }
    } catch (error) {
      console.warn('Failed to load preferences:', error);
    }
  }

  /**
   * 保存偏好设置
   */
  private async savePreferences(): Promise<void> {
    try {
      const data: Record<string, UserPreference> = {};
      for (const [key, preference] of this.preferences.entries()) {
        data[key] = preference;
      }
      
      const content = JSON.stringify(data, null, 2);
      await this.app.vault.adapter.write(this.preferencesFile, content);
    } catch (error) {
      console.error('Failed to save preferences:', error);
      throw error;
    }
  }

  /**
   * 格式化记忆内容
   */
  private formatMemoryContent(content: string, tags: string[]): string {
    const timestamp = new Date().toISOString();
    const tagString = tags.map(tag => `#${tag}`).join(' ');
    
    return `---
created: ${timestamp}
tags: [${tags.map(tag => `"${tag}"`).join(', ')}]
type: memory
---

# AI Coach Memory

${tagString}

${content}

---
*Created by AI Coach Advanced on ${new Date().toLocaleString()}*`;
  }

  /**
   * 解析记忆内容
   */
  private parseMemoryContent(content: string): {
    content: string;
    tags: string[];
    created: string;
  } {
    const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---/);
    let tags: string[] = [];
    let created = '';
    let mainContent = content;

    if (frontmatterMatch) {
      const frontmatter = frontmatterMatch[1];
      const tagsMatch = frontmatter.match(/tags:\s*\[(.*?)\]/);
      const createdMatch = frontmatter.match(/created:\s*(.+)/);
      
      if (tagsMatch) {
        tags = tagsMatch[1].split(',').map(tag => tag.trim().replace(/"/g, ''));
      }
      
      if (createdMatch) {
        created = createdMatch[1].trim();
      }
      
      mainContent = content.replace(frontmatterMatch[0], '').trim();
    }

    return { content: mainContent, tags, created };
  }

  /**
   * 获取记忆文件列表
   */
  private async getMemoryFiles(): Promise<string[]> {
    try {
      const adapter = this.app.vault.adapter;
      const files = await adapter.list(this.memoryFolder);
      return files.files.filter(file => file.endsWith('.md'));
    } catch (error) {
      console.error('Failed to get memory files:', error);
      return [];
    }
  }

  /**
   * 生成记忆ID
   */
  private generateMemoryId(): string {
    return `memory_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取统计信息
   */
  async getStats(): Promise<{
    preferencesCount: number;
    memoriesCount: number;
    totalSize: number;
  }> {
    const memoryFiles = await this.getMemoryFiles();
    let totalSize = 0;

    for (const file of memoryFiles) {
      try {
        const stat = await this.app.vault.adapter.stat(file);
        totalSize += stat?.size || 0;
      } catch (error) {
        // 忽略错误
      }
    }

    return {
      preferencesCount: this.preferences.size,
      memoriesCount: memoryFiles.length,
      totalSize
    };
  }

  /**
   * 清理旧记忆
   */
  async cleanupOldMemories(daysOld: number = 90): Promise<number> {
    const memoryFiles = await this.getMemoryFiles();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);
    
    let deletedCount = 0;

    for (const file of memoryFiles) {
      try {
        const stat = await this.app.vault.adapter.stat(file);
        if (stat && stat.mtime < cutoffDate.getTime()) {
          await this.app.vault.adapter.remove(file);
          deletedCount++;
        }
      } catch (error) {
        console.warn(`Failed to process file ${file}:`, error);
      }
    }

    return deletedCount;
  }
}
