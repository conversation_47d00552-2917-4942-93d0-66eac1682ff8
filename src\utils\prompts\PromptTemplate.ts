/**
 * 提示模板系统
 * 支持动态变量替换和条件渲染
 */

export interface PromptVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  required: boolean;
  description: string;
  defaultValue?: any;
}

export interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  category: 'system' | 'task' | 'tool' | 'response' | 'error';
  template: string;
  variables: PromptVariable[];
  version: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface PromptContext {
  [key: string]: any;
}

export class PromptTemplateEngine {
  private templates: Map<string, PromptTemplate> = new Map();

  /**
   * 注册提示模板
   */
  registerTemplate(template: PromptTemplate): void {
    this.templates.set(template.id, template);
  }

  /**
   * 获取提示模板
   */
  getTemplate(id: string): PromptTemplate | undefined {
    return this.templates.get(id);
  }

  /**
   * 列出所有模板
   */
  listTemplates(category?: string): PromptTemplate[] {
    const templates = Array.from(this.templates.values());
    return category ? templates.filter(t => t.category === category) : templates;
  }

  /**
   * 渲染提示模板
   */
  render(templateId: string, context: PromptContext): string {
    const template = this.templates.get(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    // 验证必需变量
    this.validateContext(template, context);

    // 渲染模板
    return this.renderTemplate(template.template, context);
  }

  /**
   * 批量渲染多个模板
   */
  renderMultiple(templateIds: string[], context: PromptContext): string[] {
    return templateIds.map(id => this.render(id, context));
  }

  /**
   * 验证上下文变量
   */
  private validateContext(template: PromptTemplate, context: PromptContext): void {
    for (const variable of template.variables) {
      if (variable.required && !(variable.name in context)) {
        throw new Error(`Required variable missing: ${variable.name}`);
      }

      if (variable.name in context) {
        const value = context[variable.name];
        if (!this.validateVariableType(value, variable.type)) {
          throw new Error(`Invalid type for variable ${variable.name}: expected ${variable.type}`);
        }
      }
    }
  }

  /**
   * 验证变量类型
   */
  private validateVariableType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number';
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      default:
        return true;
    }
  }

  /**
   * 渲染模板字符串
   */
  private renderTemplate(template: string, context: PromptContext): string {
    let result = template;

    // 处理条件渲染 {{#if condition}}...{{/if}}
    result = this.processConditionals(result, context);

    // 处理循环渲染 {{#each array}}...{{/each}}
    result = this.processLoops(result, context);

    // 处理变量替换 {{variable}}
    result = this.processVariables(result, context);

    return result.trim();
  }

  /**
   * 处理条件渲染
   */
  private processConditionals(template: string, context: PromptContext): string {
    const conditionalRegex = /\{\{#if\s+(\w+)\}\}([\s\S]*?)\{\{\/if\}\}/g;
    
    return template.replace(conditionalRegex, (match, condition, content) => {
      const value = context[condition];
      return value ? content : '';
    });
  }

  /**
   * 处理循环渲染
   */
  private processLoops(template: string, context: PromptContext): string {
    const loopRegex = /\{\{#each\s+(\w+)\}\}([\s\S]*?)\{\{\/each\}\}/g;
    
    return template.replace(loopRegex, (match, arrayName, content) => {
      const array = context[arrayName];
      if (!Array.isArray(array)) {
        return '';
      }

      return array.map((item, index) => {
        let itemContent = content;
        // 替换 {{this}} 为当前项
        itemContent = itemContent.replace(/\{\{this\}\}/g, String(item));
        // 替换 {{@index}} 为当前索引
        itemContent = itemContent.replace(/\{\{@index\}\}/g, String(index));
        
        // 如果item是对象，支持 {{property}} 访问
        if (typeof item === 'object' && item !== null) {
          for (const [key, value] of Object.entries(item)) {
            const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
            itemContent = itemContent.replace(regex, String(value));
          }
        }
        
        return itemContent;
      }).join('');
    });
  }

  /**
   * 处理变量替换
   */
  private processVariables(template: string, context: PromptContext): string {
    const variableRegex = /\{\{(\w+)\}\}/g;
    
    return template.replace(variableRegex, (match, variableName) => {
      const value = context[variableName];
      return value !== undefined ? String(value) : match;
    });
  }

  /**
   * 创建模板构建器
   */
  static createBuilder(): PromptTemplateBuilder {
    return new PromptTemplateBuilder();
  }
}

/**
 * 提示模板构建器
 */
export class PromptTemplateBuilder {
  private template: Partial<PromptTemplate> = {
    variables: [],
    version: '1.0.0',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  id(id: string): this {
    this.template.id = id;
    return this;
  }

  name(name: string): this {
    this.template.name = name;
    return this;
  }

  description(description: string): this {
    this.template.description = description;
    return this;
  }

  category(category: PromptTemplate['category']): this {
    this.template.category = category;
    return this;
  }

  content(template: string): this {
    this.template.template = template;
    return this;
  }

  variable(variable: PromptVariable): this {
    this.template.variables!.push(variable);
    return this;
  }

  version(version: string): this {
    this.template.version = version;
    return this;
  }

  build(): PromptTemplate {
    if (!this.template.id || !this.template.name || !this.template.template) {
      throw new Error('Template id, name, and content are required');
    }

    return this.template as PromptTemplate;
  }
}

/**
 * 常用变量类型定义
 */
export const CommonVariables = {
  USER_INPUT: {
    name: 'userInput',
    type: 'string' as const,
    required: true,
    description: '用户输入的内容',
  },
  CONTEXT: {
    name: 'context',
    type: 'string' as const,
    required: false,
    description: '上下文信息',
  },
  TOOLS: {
    name: 'tools',
    type: 'array' as const,
    required: false,
    description: '可用工具列表',
  },
  RESULTS: {
    name: 'results',
    type: 'array' as const,
    required: false,
    description: '工具执行结果',
  },
  ERROR: {
    name: 'error',
    type: 'string' as const,
    required: true,
    description: '错误信息',
  },
};
