import { ToolRegistry } from '../../../src/core/tools/ToolRegistry';
import { BaseTool } from '../../../src/core/tools/BaseTool';
import { ToolResult, ToolConfig } from '../../../src/types/tools';

// Mock tool for testing
class MockTool extends BaseTool {
  constructor(name: string = 'mock_tool') {
    const config: ToolConfig = {
      name,
      description: 'A mock tool for testing',
      category: 'test',
      version: '1.0.0',
      parameters: {
        type: 'object',
        properties: {
          input: { type: 'string' }
        },
        required: ['input']
      },
      permissions: {
        required: [],
        optional: [],
        dangerous: false,
        requiresConfirmation: false
      },
      metadata: {
        author: 'Test',
        tags: ['test'],
        documentation: 'Test tool'
      }
    };
    super(config);
  }

  protected async executeInternal(args: Record<string, any>): Promise<ToolResult> {
    return {
      success: true,
      data: { output: `Processed: ${args.input}` }
    };
  }
}

describe('ToolRegistry', () => {
  let registry: ToolRegistry;
  let mockTool: MockTool;

  beforeEach(() => {
    registry = new ToolRegistry();
    mockTool = new MockTool();
  });

  describe('register', () => {
    it('should register a tool successfully', async () => {
      await registry.register(mockTool);

      const tools = registry.list();
      expect(tools).toHaveLength(1);
      expect(tools[0].name).toBe('mock_tool');
    });

    it('should prevent duplicate tool registration', async () => {
      await registry.register(mockTool);

      await expect(registry.register(mockTool)).rejects.toThrow(
        'Tool with name mock_tool is already registered'
      );
    });

    it('should validate tool before registration', async () => {
      const invalidTool = new MockTool('');

      await expect(registry.register(invalidTool)).rejects.toThrow();
    });
  });

  describe('unregister', () => {
    it('should unregister a tool successfully', async () => {
      await registry.register(mockTool);
      
      const result = registry.unregister('mock_tool');
      
      expect(result).toBe(true);
      expect(registry.list()).toHaveLength(0);
    });

    it('should return false for non-existent tool', () => {
      const result = registry.unregister('non_existent');
      
      expect(result).toBe(false);
    });
  });

  describe('get', () => {
    it('should retrieve registered tool', async () => {
      await registry.register(mockTool);

      const tool = registry.get('mock_tool');

      expect(tool).toBe(mockTool);
    });

    it('should return undefined for non-existent tool', () => {
      const tool = registry.get('non_existent');

      expect(tool).toBeUndefined();
    });
  });

  describe('execute', () => {
    it('should execute tool successfully', async () => {
      await registry.register(mockTool);

      const result = await registry.execute('mock_tool', { input: 'test' });

      expect(result.success).toBe(true);
      expect(result.data?.output).toBe('Processed: test');
    });

    it('should handle tool execution errors', async () => {
      const errorTool = new (class extends MockTool {
        protected async executeInternal(): Promise<ToolResult> {
          throw new Error('Execution failed');
        }
      })('error_tool');

      await registry.register(errorTool);

      const result = await registry.execute('error_tool', { input: 'test' });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Execution failed');
    });

    it('should return error for non-existent tool', async () => {
      const result = await registry.execute('non_existent', {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('Tool non_existent not found');
    });
  });

  describe('list', () => {
    it('should return empty list initially', () => {
      const tools = registry.list();

      expect(tools).toHaveLength(0);
    });

    it('should return all registered tools', async () => {
      const tool1 = new MockTool('tool1');
      const tool2 = new MockTool('tool2');

      await registry.register(tool1);
      await registry.register(tool2);

      const tools = registry.list();

      expect(tools).toHaveLength(2);
      expect(tools.map(t => t.name)).toContain('tool1');
      expect(tools.map(t => t.name)).toContain('tool2');
    });
  });

  describe('listByCategory', () => {
    it('should filter tools by category', async () => {
      const testTool = new MockTool('test_tool');
      const webTool = new (class extends MockTool {
        constructor() {
          super('web_tool');
          this.config.category = 'web';
        }
      })();

      await registry.register(testTool);
      await registry.register(webTool);

      const testTools = registry.listByCategory('test');
      const webTools = registry.listByCategory('web');

      expect(testTools).toHaveLength(1);
      expect(testTools[0].name).toBe('test_tool');
      expect(webTools).toHaveLength(1);
      expect(webTools[0].name).toBe('web_tool');
    });
  });

  describe('getStats', () => {
    it('should return correct statistics', async () => {
      await registry.register(mockTool);
      await registry.execute('mock_tool', { input: 'test' });

      const stats = registry.getStats();

      expect(stats.totalTools).toBe(1);
      expect(stats.totalExecutions).toBe(1);
      expect(stats.successfulExecutions).toBe(1);
      expect(stats.failedExecutions).toBe(0);
    });

    it('should track failed executions', async () => {
      const errorTool = new (class extends MockTool {
        protected async executeInternal(): Promise<ToolResult> {
          throw new Error('Test error');
        }
      })('error_tool');

      await registry.register(errorTool);
      await registry.execute('error_tool', {});

      const stats = registry.getStats();

      expect(stats.totalExecutions).toBe(1);
      expect(stats.successfulExecutions).toBe(0);
      expect(stats.failedExecutions).toBe(1);
    });
  });

  describe('clear', () => {
    it('should clear all registered tools', async () => {
      await registry.register(mockTool);
      
      registry.clear();
      
      expect(registry.list()).toHaveLength(0);
    });
  });

  describe('hasPermission', () => {
    it('should check tool permissions correctly', async () => {
      const restrictedTool = new (class extends MockTool {
        constructor() {
          super('restricted_tool');
          this.config.permissions.required = ['admin'];
        }
      })();

      await registry.register(restrictedTool);

      const hasPermission = registry.hasPermission('restricted_tool', ['admin']);
      const noPermission = registry.hasPermission('restricted_tool', ['user']);

      expect(hasPermission).toBe(true);
      expect(noPermission).toBe(false);
    });
  });
});
