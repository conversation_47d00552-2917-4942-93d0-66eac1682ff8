// UI相关类型定义

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  status: 'sending' | 'sent' | 'error';
  metadata?: {
    toolsUsed?: string[];
    executionTime?: number;
    tokens?: number;
  };
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
}

export interface UIState {
  currentSession: ChatSession | null;
  isLoading: boolean;
  error: string | null;
  progress: {
    visible: boolean;
    message: string;
    percentage?: number;
  };
}

export interface ModalOptions {
  title: string;
  placeholder?: string;
  defaultValue?: string;
  multiline?: boolean;
  width?: number;
  height?: number;
}

export interface NotificationOptions {
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  duration?: number;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}
