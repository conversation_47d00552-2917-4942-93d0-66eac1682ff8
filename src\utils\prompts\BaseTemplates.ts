import { PromptTemplate, PromptTemplateBuilder, CommonVariables } from './PromptTemplate';

/**
 * 基础提示模板集合
 * 包含系统提示、任务分析、工具选择等核心模板
 */
export class BaseTemplates {
  /**
   * 系统提示模板
   */
  static readonly SYSTEM_PROMPT = new PromptTemplateBuilder()
    .id('system_prompt')
    .name('系统提示')
    .description('定义AI助手的角色和基本行为规范')
    .category('system')
    .content(`你是一个智能的Obsidian助手，名为AI Coach Advanced。你的主要职责是：

1. **理解用户意图**：准确理解用户的自然语言指令，识别用户想要完成的任务。

2. **积极使用工具**：当用户的请求需要获取信息、执行操作或处理数据时，必须主动调用相应的工具。

3. **知识库查询**：当需要查找信息时，优先搜索用户的Obsidian Vault笔记内容。

4. **内容生成**：基于查询结果和用户需求，生成有用、准确的回答或内容。

**重要：工具调用指导**
- 当用户询问笔记内容、查找信息时 → 使用 vault_query 工具
- 当需要最新信息、网络搜索时 → 使用 web_search 工具
- 当需要计算、数据处理、代码执行时 → 使用 javascript_executor 工具
- 当需要管理插件、执行命令时 → 使用 plugin_manager 工具
- 不要仅凭记忆回答，要通过工具获取准确信息

**行为准则**：
- 始终以用户的需求为中心
- 主动使用工具获取准确信息
- 在不确定时主动询问澄清
- 保护用户隐私和数据安全
- 承认自己的局限性

**当前可用工具**：
{{#if tools}}
{{#each tools}}
- {{name}}: {{description}}
{{/each}}
{{/if}}

请根据用户的指令，积极选择合适的工具并提供帮助。`)
    .variable(CommonVariables.TOOLS)
    .build();

  /**
   * 任务分析模板
   */
  static readonly TASK_ANALYSIS = new PromptTemplateBuilder()
    .id('task_analysis')
    .name('任务分析')
    .description('分析用户输入，识别任务类型和所需工具')
    .category('task')
    .content(`请分析以下用户输入，确定需要执行的任务：

用户输入：{{userInput}}

{{#if context}}
上下文信息：{{context}}
{{/if}}

请按以下格式分析：

**任务类型**：[查询信息/创建内容/执行操作/其他]

**主要目标**：[简要描述用户想要达成的目标]

**所需信息**：[列出完成任务需要的信息]

**建议工具**：[根据任务需求推荐使用的工具]

**执行步骤**：
1. [第一步]
2. [第二步]
3. [后续步骤...]

请基于这个分析制定执行计划。`)
    .variable(CommonVariables.USER_INPUT)
    .variable(CommonVariables.CONTEXT)
    .build();

  /**
   * 工具选择模板
   */
  static readonly TOOL_SELECTION = new PromptTemplateBuilder()
    .id('tool_selection')
    .name('工具选择')
    .description('根据任务需求选择合适的工具')
    .category('tool')
    .content(`根据以下任务需求，选择最合适的工具：

**任务描述**：{{taskDescription}}

**可用工具**：
{{#each tools}}
- **{{name}}**：{{description}}
  参数：{{#each parameters}}{{name}} ({{type}}){{#unless @last}}, {{/unless}}{{/each}}
{{/each}}

**选择标准**：
1. 工具功能是否匹配任务需求
2. 工具的可靠性和安全性
3. 执行效率和用户体验

请选择最合适的工具并说明理由。如果需要多个工具配合，请说明调用顺序。

**选择结果**：
工具名称：[选择的工具]
调用参数：[具体参数值]
选择理由：[为什么选择这个工具]`)
    .variable({
      name: 'taskDescription',
      type: 'string',
      required: true,
      description: '任务描述',
    })
    .variable(CommonVariables.TOOLS)
    .build();

  /**
   * Vault查询模板
   */
  static readonly VAULT_QUERY = new PromptTemplateBuilder()
    .id('vault_query')
    .name('Vault查询')
    .description('构造Vault知识库查询')
    .category('tool')
    .content(`基于用户问题构造Vault查询：

**用户问题**：{{userInput}}

**查询策略**：
1. 提取关键词和概念
2. 考虑同义词和相关术语
3. 构造有效的搜索查询

**查询关键词**：[提取的关键词]

**搜索查询**：[优化后的查询语句]

**预期结果**：[期望找到什么类型的信息]`)
    .variable(CommonVariables.USER_INPUT)
    .build();

  /**
   * 内容总结模板
   */
  static readonly CONTENT_SUMMARY = new PromptTemplateBuilder()
    .id('content_summary')
    .name('内容总结')
    .description('总结查询结果或生成内容')
    .category('response')
    .content(`基于以下信息为用户提供回答：

**用户问题**：{{userInput}}

**查询结果**：
{{#each results}}
**来源**：{{source}}
**内容**：{{content}}
**相关性**：{{score}}

{{/each}}

请基于这些信息提供一个准确、有用的回答。要求：

1. **直接回答**：首先直接回答用户的问题
2. **支撑信息**：引用相关的笔记内容作为支撑
3. **来源标注**：标明信息来源的笔记文件
4. **补充建议**：如果有相关的补充信息或建议，请提供

**回答格式**：
[直接回答用户问题]

**详细信息**：
[基于查询结果的详细说明]

**相关笔记**：
{{#each results}}
- [[{{source}}]]：{{content}}
{{/each}}

{{#if suggestions}}
**建议**：
{{suggestions}}
{{/if}}`)
    .variable(CommonVariables.USER_INPUT)
    .variable(CommonVariables.RESULTS)
    .variable({
      name: 'suggestions',
      type: 'string',
      required: false,
      description: '补充建议',
    })
    .build();

  /**
   * 错误处理模板
   */
  static readonly ERROR_HANDLING = new PromptTemplateBuilder()
    .id('error_handling')
    .name('错误处理')
    .description('处理工具调用错误或异常情况')
    .category('error')
    .content(`处理执行过程中的错误：

**错误信息**：{{error}}

**用户请求**：{{userInput}}

**错误分析**：
[分析错误原因和可能的解决方案]

**用户反馈**：
抱歉，在处理您的请求时遇到了问题：{{error}}

**可能的解决方案**：
1. [建议的解决方案1]
2. [建议的解决方案2]
3. [其他替代方案]

您可以尝试：
- 重新表述您的问题
- 检查相关设置
- 或者我可以用其他方式帮助您

请告诉我您希望如何继续。`)
    .variable(CommonVariables.ERROR)
    .variable(CommonVariables.USER_INPUT)
    .build();

  /**
   * 工具调用结果处理模板
   */
  static readonly TOOL_RESULT_PROCESSING = new PromptTemplateBuilder()
    .id('tool_result_processing')
    .name('工具结果处理')
    .description('处理和解释工具调用结果')
    .category('response')
    .content(`处理工具调用结果：

**工具名称**：{{toolName}}
**调用参数**：{{parameters}}
**执行结果**：{{result}}
**用户原始请求**：{{userInput}}

**结果分析**：
{{#if result.success}}
工具执行成功。结果数据：{{result.data}}

**用户回复**：
[基于结果为用户提供有用的回答]
{{else}}
工具执行失败。错误信息：{{result.error}}

**用户回复**：
很抱歉，{{toolName}}工具执行时出现问题：{{result.error}}

让我尝试其他方法来帮助您。
{{/if}}`)
    .variable({
      name: 'toolName',
      type: 'string',
      required: true,
      description: '工具名称',
    })
    .variable({
      name: 'parameters',
      type: 'object',
      required: true,
      description: '调用参数',
    })
    .variable({
      name: 'result',
      type: 'object',
      required: true,
      description: '执行结果',
    })
    .variable(CommonVariables.USER_INPUT)
    .build();

  /**
   * 获取所有基础模板
   */
  static getAllTemplates(): PromptTemplate[] {
    return [
      this.SYSTEM_PROMPT,
      this.TASK_ANALYSIS,
      this.TOOL_SELECTION,
      this.VAULT_QUERY,
      this.CONTENT_SUMMARY,
      this.ERROR_HANDLING,
      this.TOOL_RESULT_PROCESSING,
    ];
  }
}
