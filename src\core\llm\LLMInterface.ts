import { LLMMessage, LLMResponse, LLMGenerateOptions, ToolDefinition } from '@/types/llm';

/**
 * 统一的LLM接口抽象
 * 所有LLM提供商的适配器都需要实现这个接口
 */
export abstract class LLMInterface {
  protected apiKey: string;
  protected baseUrl?: string;
  protected model: string;
  protected timeout: number;

  constructor(apiKey: string, model: string, baseUrl?: string, timeout: number = 30000) {
    this.apiKey = apiKey;
    this.model = model;
    this.baseUrl = baseUrl;
    this.timeout = timeout;
  }

  /**
   * 生成文本响应
   * @param messages 对话消息列表
   * @param options 生成选项
   * @returns LLM响应
   */
  abstract generateText(
    messages: LLMMessage[], 
    options?: LLMGenerateOptions
  ): Promise<LLMResponse>;

  /**
   * 使用工具生成响应
   * @param messages 对话消息列表
   * @param tools 可用工具定义
   * @param options 生成选项
   * @returns LLM响应
   */
  abstract generateWithTools(
    messages: LLMMessage[], 
    tools: ToolDefinition[], 
    options?: LLMGenerateOptions
  ): Promise<LLMResponse>;

  /**
   * 流式生成文本响应
   * @param messages 对话消息列表
   * @param onChunk 接收流式数据的回调函数
   * @param options 生成选项
   * @returns Promise<void>
   */
  abstract generateTextStream(
    messages: LLMMessage[],
    onChunk: (chunk: string) => void,
    options?: LLMGenerateOptions
  ): Promise<void>;

  /**
   * 验证API连接
   * @returns 是否连接成功
   */
  abstract validateConnection(): Promise<boolean>;

  /**
   * 获取模型信息
   * @returns 模型信息
   */
  abstract getModelInfo(): Promise<ModelInfo>;

  /**
   * 计算token数量（估算）
   * @param text 文本内容
   * @returns token数量
   */
  abstract estimateTokens(text: string): number;

  /**
   * 获取支持的模型列表
   * @returns 模型列表
   */
  abstract getSupportedModels(): Promise<string[]>;

  /**
   * 更新配置
   * @param config 新配置
   */
  updateConfig(config: {
    apiKey?: string;
    model?: string;
    baseUrl?: string;
    timeout?: number;
  }): void {
    if (config.apiKey !== undefined) this.apiKey = config.apiKey;
    if (config.model !== undefined) this.model = config.model;
    if (config.baseUrl !== undefined) this.baseUrl = config.baseUrl;
    if (config.timeout !== undefined) this.timeout = config.timeout;
  }

  /**
   * 获取当前配置
   * @returns 当前配置
   */
  getConfig(): LLMConfig {
    return {
      apiKey: this.apiKey,
      model: this.model,
      baseUrl: this.baseUrl,
      timeout: this.timeout,
    };
  }
}

/**
 * 模型信息接口
 */
export interface ModelInfo {
  name: string;
  provider: string;
  maxTokens: number;
  supportsFunctionCalling: boolean;
  supportsStreaming: boolean;
  costPer1kTokens?: {
    input: number;
    output: number;
  };
}

/**
 * LLM配置接口
 */
export interface LLMConfig {
  apiKey: string;
  model: string;
  baseUrl?: string;
  timeout: number;
}

/**
 * LLM错误类型
 */
export class LLMError extends Error {
  public readonly code: string;
  public readonly statusCode?: number;
  public readonly details?: any;

  constructor(message: string, code: string, statusCode?: number, details?: any) {
    super(message);
    this.name = 'LLMError';
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
  }
}

/**
 * 常见错误代码
 */
export const LLMErrorCodes = {
  INVALID_API_KEY: 'INVALID_API_KEY',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  MODEL_NOT_FOUND: 'MODEL_NOT_FOUND',
  INSUFFICIENT_QUOTA: 'INSUFFICIENT_QUOTA',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT: 'TIMEOUT',
  INVALID_REQUEST: 'INVALID_REQUEST',
  SERVER_ERROR: 'SERVER_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const;
