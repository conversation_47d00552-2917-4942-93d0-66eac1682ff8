const fs = require('fs');
const path = require('path');
const yauzl = require('yauzl');

async function verifyPackage() {
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const version = packageJson.version;
    const packagePath = path.join('releases', `ai-coach-advanced-v${version}.zip`);
    
    if (!fs.existsSync(packagePath)) {
      console.error(`❌ 包文件不存在: ${packagePath}`);
      return;
    }
    
    console.log(`🔍 验证包文件: ${packagePath}`);
    
    yauzl.open(packagePath, { lazyEntries: true }, (err, zipfile) => {
      if (err) {
        console.error('❌ 无法打开zip文件:', err);
        return;
      }
      
      const files = [];
      
      zipfile.readEntry();
      zipfile.on('entry', (entry) => {
        files.push(entry.fileName);
        zipfile.readEntry();
      });
      
      zipfile.on('end', () => {
        console.log('\n📋 包文件内容:');
        files.sort().forEach(file => {
          console.log(`  📄 ${file}`);
        });
        
        // 检查必需文件
        const requiredFiles = [
          'main.js',
          'manifest.json',
          'styles.css',
          'README.md'
        ];
        
        console.log('\n✅ 必需文件检查:');
        let allPresent = true;
        
        requiredFiles.forEach(file => {
          const present = files.includes(file);
          console.log(`  ${present ? '✅' : '❌'} ${file}`);
          if (!present) allPresent = false;
        });
        
        if (allPresent) {
          console.log('\n🎉 所有必需文件都存在！');
        } else {
          console.log('\n⚠️ 缺少必需文件！');
        }
        
        console.log(`\n📊 总文件数: ${files.length}`);
      });
    });
    
  } catch (error) {
    console.error('❌ 验证失败:', error);
  }
}

verifyPackage();
