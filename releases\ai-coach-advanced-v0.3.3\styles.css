/* AI Coach Advanced - 样式文件 */

/* ==========================================================================
   聊天界面样式
   ========================================================================== */

.ai-coach-chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 600px;
}

.ai-coach-chat-output {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 8px;
  background: var(--background-primary);
  margin-bottom: 16px;
  font-family: var(--font-interface);
  line-height: 1.6;
}

.ai-coach-chat-output::-webkit-scrollbar {
  width: 8px;
}

.ai-coach-chat-output::-webkit-scrollbar-track {
  background: var(--background-secondary);
  border-radius: 4px;
}

.ai-coach-chat-output::-webkit-scrollbar-thumb {
  background: var(--background-modifier-border);
  border-radius: 4px;
}

.ai-coach-chat-output::-webkit-scrollbar-thumb:hover {
  background: var(--background-modifier-border-hover);
}

/* 消息样式 */
.ai-coach-message {
  margin-bottom: 16px;
  padding: 12px 16px;
  border-radius: 12px;
  max-width: 85%;
  word-wrap: break-word;
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.ai-coach-message-user {
  background: var(--interactive-accent);
  color: var(--text-on-accent);
  margin-left: auto;
  margin-right: 0;
  text-align: right;
}

.ai-coach-message-assistant {
  background: var(--background-secondary);
  color: var(--text-normal);
  margin-left: 0;
  margin-right: auto;
}

.ai-coach-message-role {
  font-weight: 600;
  font-size: 0.85em;
  opacity: 0.8;
  margin-bottom: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.ai-coach-message-content {
  white-space: pre-wrap;
  line-height: 1.5;
  font-size: 0.95em;
}

.ai-coach-message-time {
  font-size: 0.75em;
  opacity: 0.6;
  margin-top: 6px;
  font-style: italic;
}

/* 执行详情样式 */
.ai-coach-execution-details {
  margin-bottom: 16px;
  padding: 12px;
  background: var(--background-modifier-form-field);
  border: 1px solid var(--background-modifier-border);
  border-radius: 8px;
  font-size: 0.85em;
  color: var(--text-muted);
}

.ai-coach-execution-details > div {
  margin-bottom: 4px;
}

.ai-coach-execution-details > div:last-child {
  margin-bottom: 0;
}

/* 输入区域样式 */
.ai-coach-input-container {
  margin-top: 8px;
}

.ai-coach-input-container .setting-item {
  border: none;
  padding: 0;
}

.ai-coach-input-container .setting-item-info {
  margin-bottom: 8px;
}

.ai-coach-input-container .setting-item-name {
  font-weight: 600;
  color: var(--text-normal);
}

.ai-coach-input-container .setting-item-description {
  color: var(--text-muted);
  font-size: 0.9em;
}

.ai-coach-input-container textarea {
  width: 100%;
  min-height: 80px;
  padding: 12px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 8px;
  background: var(--background-primary);
  color: var(--text-normal);
  font-family: var(--font-interface);
  font-size: 0.95em;
  line-height: 1.4;
  resize: vertical;
  transition: border-color 0.2s ease;
}

.ai-coach-input-container textarea:focus {
  outline: none;
  border-color: var(--interactive-accent);
  box-shadow: 0 0 0 2px var(--interactive-accent-hover);
}

.ai-coach-input-container textarea::placeholder {
  color: var(--text-faint);
  font-style: italic;
}

/* 按钮容器样式 */
.ai-coach-button-container {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 12px;
  flex-wrap: wrap;
}

.ai-coach-button-container .setting-item {
  border: none;
  padding: 0;
  margin: 0;
}

.ai-coach-button-container button {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9em;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  border: 1px solid var(--background-modifier-border);
}

.ai-coach-button-container button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ai-coach-button-container button:active {
  transform: translateY(0);
}

.ai-coach-button-container button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* ==========================================================================
   状态栏样式
   ========================================================================== */

.ai-coach-status-bar {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85em;
  transition: all 0.2s ease;
  cursor: pointer;
  user-select: none;
}

.ai-coach-status-bar:hover {
  background-color: var(--background-modifier-hover);
}

.ai-coach-status-icon {
  font-size: 1.1em;
  line-height: 1;
}

.ai-coach-status-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
  font-weight: 500;
}

.ai-coach-progress-bar {
  width: 60px;
  height: 3px;
  background-color: var(--background-modifier-border);
  border-radius: 2px;
  overflow: hidden;
  margin-left: 4px;
}

.ai-coach-progress-fill {
  height: 100%;
  background-color: var(--interactive-accent);
  transition: width 0.3s ease;
  border-radius: 2px;
}

/* 状态颜色 */
.ai-coach-status-idle {
  color: var(--text-normal);
}

.ai-coach-status-processing {
  color: var(--interactive-accent);
}

.ai-coach-status-processing .ai-coach-status-icon {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.ai-coach-status-success {
  color: var(--text-success);
}

.ai-coach-status-error {
  color: var(--text-error);
}

.ai-coach-status-warning {
  color: var(--text-warning);
}

/* ==========================================================================
   设置页面样式
   ========================================================================== */

.ai-coach-settings {
  max-width: 800px;
  margin: 0 auto;
}

.ai-coach-settings .setting-item {
  padding: 16px 0;
  border-bottom: 1px solid var(--background-modifier-border);
}

.ai-coach-settings .setting-item:last-child {
  border-bottom: none;
}

.ai-coach-settings .setting-item-name {
  font-weight: 600;
  color: var(--text-normal);
  margin-bottom: 4px;
}

.ai-coach-settings .setting-item-description {
  color: var(--text-muted);
  font-size: 0.9em;
  line-height: 1.4;
  margin-bottom: 8px;
}

.ai-coach-settings input[type="text"],
.ai-coach-settings input[type="password"],
.ai-coach-settings select,
.ai-coach-settings textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 6px;
  background: var(--background-primary);
  color: var(--text-normal);
  font-family: var(--font-interface);
  font-size: 0.9em;
  transition: border-color 0.2s ease;
}

.ai-coach-settings input[type="text"]:focus,
.ai-coach-settings input[type="password"]:focus,
.ai-coach-settings select:focus,
.ai-coach-settings textarea:focus {
  outline: none;
  border-color: var(--interactive-accent);
}

.ai-coach-settings .checkbox-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-coach-settings input[type="checkbox"] {
  width: auto;
  margin: 0;
}

/* 设置组样式 */
.ai-coach-setting-group {
  margin-bottom: 24px;
  padding: 16px;
  background: var(--background-secondary);
  border-radius: 8px;
  border: 1px solid var(--background-modifier-border);
}

.ai-coach-setting-group-title {
  font-size: 1.1em;
  font-weight: 600;
  color: var(--text-normal);
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--background-modifier-border);
}

/* ==========================================================================
   模态框样式
   ========================================================================== */

.modal.ai-coach-modal {
  max-width: 900px;
  width: 90vw;
  max-height: 80vh;
}

.modal.ai-coach-modal .modal-content {
  padding: 24px;
}

.modal.ai-coach-modal .modal-title {
  font-size: 1.3em;
  font-weight: 600;
  color: var(--text-normal);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal.ai-coach-modal .modal-close-button {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  font-size: 1.2em;
  color: var(--text-muted);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal.ai-coach-modal .modal-close-button:hover {
  color: var(--text-normal);
  background: var(--background-modifier-hover);
}

/* ==========================================================================
   响应式设计
   ========================================================================== */

@media (max-width: 768px) {
  .ai-coach-chat-container {
    max-height: 500px;
  }
  
  .ai-coach-message {
    max-width: 95%;
    padding: 10px 12px;
  }
  
  .ai-coach-button-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .ai-coach-button-container button {
    width: 100%;
    margin-bottom: 8px;
  }
  
  .ai-coach-status-text {
    max-width: 100px;
  }
  
  .modal.ai-coach-modal {
    width: 95vw;
    max-height: 90vh;
  }
  
  .modal.ai-coach-modal .modal-content {
    padding: 16px;
  }
}

/* ==========================================================================
   主题适配
   ========================================================================== */

/* 深色主题特定样式 */
.theme-dark .ai-coach-message-user {
  background: var(--interactive-accent);
  color: var(--text-on-accent);
}

.theme-dark .ai-coach-message-assistant {
  background: var(--background-secondary);
  border: 1px solid var(--background-modifier-border);
}

.theme-dark .ai-coach-execution-details {
  background: var(--background-modifier-form-field);
  border-color: var(--background-modifier-border);
}

/* 浅色主题特定样式 */
.theme-light .ai-coach-message-assistant {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.theme-light .ai-coach-execution-details {
  background: #f1f3f4;
  border-color: #dadce0;
}

/* ==========================================================================
   动画和过渡效果
   ========================================================================== */

.ai-coach-fade-in {
  animation: fadeIn 0.3s ease-in;
}

.ai-coach-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ai-coach-loading {
  position: relative;
}

.ai-coach-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--background-modifier-border);
  border-top: 2px solid var(--interactive-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ==========================================================================
   工具提示样式
   ========================================================================== */

.ai-coach-tooltip {
  position: relative;
  cursor: help;
}

.ai-coach-tooltip::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--background-tooltip);
  color: var(--text-on-accent);
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 0.8em;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 1000;
}

.ai-coach-tooltip:hover::before {
  opacity: 1;
}

/* ==========================================================================
   辅助类
   ========================================================================== */

.ai-coach-hidden {
  display: none !important;
}

.ai-coach-disabled {
  opacity: 0.6;
  pointer-events: none;
}

.ai-coach-text-center {
  text-align: center;
}

.ai-coach-text-muted {
  color: var(--text-muted);
}

.ai-coach-text-success {
  color: var(--text-success);
}

.ai-coach-text-error {
  color: var(--text-error);
}

.ai-coach-text-warning {
  color: var(--text-warning);
}

.ai-coach-margin-top {
  margin-top: 16px;
}

.ai-coach-margin-bottom {
  margin-bottom: 16px;
}

.ai-coach-padding {
  padding: 16px;
}

.ai-coach-border-radius {
  border-radius: 8px;
}

/* ==========================================================================
   打印样式
   ========================================================================== */

@media print {
  .ai-coach-status-bar,
  .ai-coach-button-container,
  .modal-close-button {
    display: none !important;
  }
  
  .ai-coach-chat-output {
    border: none;
    box-shadow: none;
    overflow: visible;
    height: auto;
    max-height: none;
  }
  
  .ai-coach-message {
    break-inside: avoid;
    margin-bottom: 12px;
  }
}

/* ==========================================================================
   侧边栏样式
   ========================================================================== */

.ai-coach-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: var(--font-interface);
}

/* 标签页头部 */
.ai-coach-tab-header {
  display: flex;
  border-bottom: 1px solid var(--background-modifier-border);
  background: var(--background-secondary);
  flex-shrink: 0;
}

.ai-coach-tab {
  flex: 1;
  padding: 12px 8px;
  text-align: center;
  cursor: pointer;
  border-right: 1px solid var(--background-modifier-border);
  transition: all 0.2s ease;
  font-size: 0.9em;
  font-weight: 500;
  user-select: none;
}

.ai-coach-tab:last-child {
  border-right: none;
}

.ai-coach-tab:hover {
  background: var(--background-modifier-hover);
}

.ai-coach-tab-active {
  background: var(--background-primary) !important;
  color: var(--text-accent);
  font-weight: 600;
  border-bottom: 2px solid var(--interactive-accent);
}

/* 记忆面板 */
.ai-coach-memory-panel {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
}

.ai-coach-memories-container {
  margin-top: 10px;
}

.ai-coach-memory-item {
  border: 1px solid var(--background-modifier-border);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  background: var(--background-secondary);
  transition: all 0.2s ease;
}

.ai-coach-memory-item:hover {
  border-color: var(--background-modifier-border-hover);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ai-coach-memory-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  gap: 8px;
}

.ai-coach-memory-header strong {
  color: var(--text-normal);
  font-weight: 600;
  flex: 1;
  word-break: break-word;
}

.ai-coach-memory-date {
  font-size: 0.75em;
  color: var(--text-muted);
  white-space: nowrap;
  flex-shrink: 0;
}

.ai-coach-memory-content {
  margin-bottom: 10px;
  line-height: 1.4;
  color: var(--text-normal);
  font-size: 0.9em;
  word-break: break-word;
}

.ai-coach-memory-actions {
  display: flex;
  gap: 6px;
  justify-content: flex-end;
}

.ai-coach-btn {
  padding: 4px 8px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  background: var(--background-primary);
  color: var(--text-normal);
  cursor: pointer;
  font-size: 0.75em;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.ai-coach-btn:hover {
  background: var(--background-modifier-hover);
  transform: translateY(-1px);
}

.ai-coach-btn-primary {
  background: var(--interactive-accent);
  color: var(--text-on-accent);
  border-color: var(--interactive-accent);
}

.ai-coach-btn-primary:hover {
  background: var(--interactive-accent-hover);
}

.ai-coach-btn-edit {
  color: var(--text-accent);
  border-color: var(--text-accent);
}

.ai-coach-btn-edit:hover {
  background: var(--text-accent);
  color: var(--text-on-accent);
}

.ai-coach-btn-delete {
  color: var(--text-error);
  border-color: var(--text-error);
}

.ai-coach-btn-delete:hover {
  background: var(--text-error);
  color: var(--text-on-accent);
}

.ai-coach-add-memory {
  text-align: center;
  margin-top: 15px;
  padding: 12px;
}

.ai-coach-add-memory button {
  padding: 8px 16px;
  font-size: 0.9em;
}

.ai-coach-no-memories {
  text-align: center;
  color: var(--text-muted);
  font-style: italic;
  padding: 40px 20px;
  background: var(--background-secondary);
  border-radius: 8px;
  border: 1px dashed var(--background-modifier-border);
}

.ai-coach-memory-error {
  color: var(--text-error);
  padding: 12px;
  background: var(--background-modifier-error);
  border: 1px solid var(--text-error);
  border-radius: 6px;
  margin: 10px 0;
  font-size: 0.9em;
}
