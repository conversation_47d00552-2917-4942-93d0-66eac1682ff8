import { Task<PERSON>lanner } from '../../../src/core/orchestration/TaskPlanner';
import { LLMInterface } from '../../../src/core/llm/LLMInterface';
import { PromptManager } from '../../../src/utils/prompts/PromptManager';
import { ToolRegistry } from '../../../src/core/tools/ToolRegistry';
import { LLMMessage, LLMResponse } from '../../../src/types/llm';
import { ToolResult } from '../../../src/types/tools';

// Mock LLM
class MockLLM implements LLMInterface {
  private responses: string[] = [];
  private responseIndex = 0;

  setResponses(responses: string[]) {
    this.responses = responses;
    this.responseIndex = 0;
  }

  async generateText(messages: LLMMessage[]): Promise<LLMResponse> {
    const response = this.responses[this.responseIndex] || 'Default response';
    this.responseIndex = (this.responseIndex + 1) % this.responses.length;
    
    return {
      content: response,
      usage: { promptTokens: 10, completionTokens: 20, totalTokens: 30 },
      model: 'mock-model'
    };
  }

  async generateTextStream(): Promise<AsyncIterable<string>> {
    throw new Error('Not implemented');
  }
}

// Mock PromptManager
class MockPromptManager extends PromptManager {
  generateTaskAnalysisPrompt(): string {
    return 'Analyze this task';
  }

  generateSystemPrompt(): string {
    return 'You are a helpful assistant';
  }

  generateContentSummaryPrompt(): string {
    return 'Summarize this content';
  }
}

// Mock ToolRegistry
class MockToolRegistry extends ToolRegistry {
  private mockTools = [
    { name: 'test_tool', description: 'A test tool' },
    { name: 'search_tool', description: 'Search for information' }
  ];

  list() {
    return this.mockTools as any;
  }

  async execute(toolName: string, args: any): Promise<ToolResult> {
    if (toolName === 'test_tool') {
      return {
        success: true,
        data: { result: 'Test result' }
      };
    }
    
    return {
      success: false,
      error: 'Tool not found'
    };
  }
}

describe('TaskPlanner', () => {
  let taskPlanner: TaskPlanner;
  let mockLLM: MockLLM;
  let mockPromptManager: MockPromptManager;
  let mockToolRegistry: MockToolRegistry;

  beforeEach(() => {
    mockLLM = new MockLLM();
    mockPromptManager = new MockPromptManager();
    mockToolRegistry = new MockToolRegistry();
    
    taskPlanner = new TaskPlanner(
      mockLLM,
      mockPromptManager,
      mockToolRegistry
    );
  });

  describe('planAndExecute', () => {
    it('should execute simple task successfully', async () => {
      mockLLM.setResponses([
        'This task is executable and straightforward.',
        'I need to use the test_tool to complete this task.',
        JSON.stringify({
          type: 'tool_call',
          description: 'Using test tool',
          toolName: 'test_tool',
          parameters: { input: 'test' }
        }),
        JSON.stringify({
          type: 'final_answer',
          description: 'Task completed',
          answer: 'Task completed successfully with result: Test result'
        })
      ]);

      const result = await taskPlanner.planAndExecute('Test task');

      expect(result.success).toBe(true);
      expect(result.result).toContain('Test result');
      expect(result.toolsUsed).toContain('test_tool');
      expect(result.iterations).toBeGreaterThan(0);
    });

    it('should handle non-executable tasks', async () => {
      mockLLM.setResponses([
        'This task cannot be executed because it is invalid.'
      ]);

      const result = await taskPlanner.planAndExecute('Invalid task');

      expect(result.success).toBe(false);
      expect(result.error).toBe('任务无法执行');
    });

    it('should handle tool execution failures', async () => {
      mockLLM.setResponses([
        'This task is executable.',
        'I need to use a non-existent tool.',
        JSON.stringify({
          type: 'tool_call',
          description: 'Using non-existent tool',
          toolName: 'non_existent_tool',
          parameters: {}
        }),
        'The tool failed, but I can provide an alternative solution.',
        JSON.stringify({
          type: 'final_answer',
          description: 'Alternative solution',
          answer: 'Could not use the tool, but here is an alternative approach.'
        })
      ]);

      const result = await taskPlanner.planAndExecute('Task requiring non-existent tool');

      expect(result.success).toBe(true);
      expect(result.result).toContain('alternative approach');
    });

    it('should handle maximum iterations', async () => {
      // Set up responses that would cause infinite loop
      mockLLM.setResponses([
        'This task is executable.',
        'I need to think more about this.',
        JSON.stringify({
          type: 'tool_call',
          description: 'Thinking step',
          toolName: 'test_tool',
          parameters: {}
        })
      ]);

      const result = await taskPlanner.planAndExecute('Complex task');

      expect(result.success).toBe(true);
      expect(result.iterations).toBeGreaterThanOrEqual(10); // Should hit max iterations
    });

    it('should handle LLM errors gracefully', async () => {
      // Mock LLM to throw error
      jest.spyOn(mockLLM, 'generateText').mockRejectedValue(new Error('LLM Error'));

      const result = await taskPlanner.planAndExecute('Test task');

      expect(result.success).toBe(false);
      expect(result.error).toContain('LLM Error');
    });

    it('should handle malformed action plans', async () => {
      mockLLM.setResponses([
        'This task is executable.',
        'I will complete this task.',
        'Invalid JSON response', // This should cause parsing error
        JSON.stringify({
          type: 'final_answer',
          description: 'Fallback answer',
          answer: 'I will complete this task.'
        })
      ]);

      const result = await taskPlanner.planAndExecute('Test task');

      expect(result.success).toBe(true);
      expect(result.result).toContain('I will complete this task');
    });

    it('should provide context to subsequent iterations', async () => {
      mockLLM.setResponses([
        'This task is executable.',
        'I need to search for information first.',
        JSON.stringify({
          type: 'tool_call',
          description: 'Searching for information',
          toolName: 'test_tool',
          parameters: { query: 'test' }
        }),
        'Now I can provide the final answer based on the search results.',
        JSON.stringify({
          type: 'final_answer',
          description: 'Final answer with context',
          answer: 'Based on the search results: Test result, here is the answer.'
        })
      ]);

      const result = await taskPlanner.planAndExecute('Search and answer task');

      expect(result.success).toBe(true);
      expect(result.result).toContain('Test result');
      expect(result.executionLog.length).toBeGreaterThan(3);
    });
  });

  describe('task analysis', () => {
    it('should correctly identify executable tasks', async () => {
      mockLLM.setResponses([
        'This is a clear and executable task that can be completed with available tools.'
      ]);

      const result = await taskPlanner.planAndExecute('Simple executable task');

      expect(result.executionLog[0].type).toBe('analysis');
      expect(result.executionLog[0].content).toContain('executable');
    });

    it('should correctly identify non-executable tasks', async () => {
      mockLLM.setResponses([
        'This task cannot be completed because it requires capabilities that are not available.'
      ]);

      const result = await taskPlanner.planAndExecute('Impossible task');

      expect(result.success).toBe(false);
      expect(result.error).toBe('任务无法执行');
    });
  });

  describe('execution logging', () => {
    it('should log all execution steps', async () => {
      mockLLM.setResponses([
        'This task is executable.',
        'I will use the test tool.',
        JSON.stringify({
          type: 'tool_call',
          description: 'Using test tool',
          toolName: 'test_tool',
          parameters: {}
        }),
        JSON.stringify({
          type: 'final_answer',
          description: 'Task completed',
          answer: 'Task completed successfully.'
        })
      ]);

      const result = await taskPlanner.planAndExecute('Test task');

      expect(result.executionLog).toBeDefined();
      expect(result.executionLog.length).toBeGreaterThan(0);
      
      const stepTypes = result.executionLog.map(step => step.type);
      expect(stepTypes).toContain('analysis');
      expect(stepTypes).toContain('thought');
      expect(stepTypes).toContain('action');
      expect(stepTypes).toContain('tool_result');
      expect(stepTypes).toContain('final_answer');
    });
  });
});
