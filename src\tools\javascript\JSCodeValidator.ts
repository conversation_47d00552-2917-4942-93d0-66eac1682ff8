/**
 * JavaScript代码验证器
 * 负责LLM生成代码的语法检查、安全验证和预处理
 */
export class JSCodeValidator {
  private syntaxChecker: SyntaxChecker;
  private securityAnalyzer: SecurityAnalyzer;
  private codeFormatter: CodeFormatter;

  constructor() {
    this.syntaxChecker = new SyntaxChecker();
    this.securityAnalyzer = new SecurityAnalyzer();
    this.codeFormatter = new CodeFormatter();
  }

  /**
   * 验证代码
   */
  async validateCode(code: string, options: ValidationOptions = {}): Promise<ValidationResult> {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: [],
      suggestions: [],
      processedCode: code
    };

    try {
      // 1. 提取代码块
      const extractedCode = this.extractCodeFromMarkdown(code);
      
      // 2. 语法检查
      const syntaxResult = await this.syntaxChecker.check(extractedCode);
      if (!syntaxResult.valid) {
        result.valid = false;
        result.errors.push(...syntaxResult.errors);
      }
      result.warnings.push(...syntaxResult.warnings);

      // 3. 安全分析
      const securityResult = await this.securityAnalyzer.analyze(extractedCode);
      if (!securityResult.safe) {
        result.valid = false;
        result.errors.push(...securityResult.risks.map(risk => `安全风险: ${risk}`));
      }
      result.warnings.push(...securityResult.warnings);

      // 4. 代码格式化和优化
      if (result.valid && options.format) {
        result.processedCode = await this.codeFormatter.format(extractedCode);
      } else {
        result.processedCode = extractedCode;
      }

      // 5. 生成建议
      result.suggestions = this.generateSuggestions(extractedCode, syntaxResult, securityResult);

    } catch (error) {
      result.valid = false;
      result.errors.push(`验证过程出错: ${error.message}`);
    }

    return result;
  }

  /**
   * 从Markdown中提取代码
   */
  private extractCodeFromMarkdown(text: string): string {
    // 匹配代码块
    const codeBlockRegex = /```(?:javascript|js)?\n?([\s\S]*?)```/gi;
    const matches = text.match(codeBlockRegex);
    
    if (matches && matches.length > 0) {
      // 提取第一个代码块的内容
      const codeBlock = matches[0];
      return codeBlock.replace(/```(?:javascript|js)?\n?/gi, '').replace(/```$/g, '').trim();
    }

    // 匹配内联代码
    const inlineCodeRegex = /`([^`]+)`/g;
    const inlineMatches = text.match(inlineCodeRegex);
    
    if (inlineMatches && inlineMatches.length > 0) {
      return inlineMatches.map(match => match.replace(/`/g, '')).join('\n');
    }

    // 如果没有找到代码块，假设整个文本就是代码
    return text.trim();
  }

  /**
   * 生成改进建议
   */
  private generateSuggestions(
    code: string, 
    syntaxResult: SyntaxCheckResult, 
    securityResult: SecurityAnalysisResult
  ): string[] {
    const suggestions: string[] = [];

    // 语法建议
    if (syntaxResult.suggestions) {
      suggestions.push(...syntaxResult.suggestions);
    }

    // 安全建议
    if (securityResult.suggestions) {
      suggestions.push(...securityResult.suggestions);
    }

    // 代码质量建议
    if (!code.includes('use strict')) {
      suggestions.push('建议添加 "use strict" 以启用严格模式');
    }

    if (code.includes('var ')) {
      suggestions.push('建议使用 let 或 const 替代 var');
    }

    if (code.includes('==') && !code.includes('===')) {
      suggestions.push('建议使用 === 替代 == 进行严格比较');
    }

    return suggestions;
  }
}

/**
 * 语法检查器
 */
class SyntaxChecker {
  async check(code: string): Promise<SyntaxCheckResult> {
    const result: SyntaxCheckResult = {
      valid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    try {
      // 使用Function构造函数进行语法检查
      new Function(code);
    } catch (error) {
      result.valid = false;
      result.errors.push(`语法错误: ${error.message}`);
    }

    // 检查常见问题
    this.checkCommonIssues(code, result);

    return result;
  }

  private checkCommonIssues(code: string, result: SyntaxCheckResult): void {
    // 检查未闭合的括号
    const brackets = { '(': ')', '[': ']', '{': '}' };
    const stack: string[] = [];
    
    for (const char of code) {
      if (char in brackets) {
        stack.push(char);
      } else if (Object.values(brackets).includes(char)) {
        const last = stack.pop();
        if (!last || brackets[last as keyof typeof brackets] !== char) {
          result.warnings.push('可能存在未匹配的括号');
          break;
        }
      }
    }

    if (stack.length > 0) {
      result.warnings.push('可能存在未闭合的括号');
    }

    // 检查未定义的变量
    const variablePattern = /\b([a-zA-Z_$][a-zA-Z0-9_$]*)\s*(?=\s*[^=]|$)/g;
    const definedVars = new Set<string>();
    const usedVars = new Set<string>();

    // 简单的变量定义检查
    const definePattern = /(?:var|let|const)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g;
    let match;
    while ((match = definePattern.exec(code)) !== null) {
      definedVars.add(match[1]);
    }

    // 函数参数
    const functionPattern = /function\s*\w*\s*\(([^)]*)\)/g;
    while ((match = functionPattern.exec(code)) !== null) {
      const params = match[1].split(',').map(p => p.trim().split(/\s+/)[0]);
      params.forEach(param => {
        if (param) definedVars.add(param);
      });
    }

    // 检查使用的变量
    while ((match = variablePattern.exec(code)) !== null) {
      const varName = match[1];
      if (!this.isBuiltinOrGlobal(varName)) {
        usedVars.add(varName);
      }
    }

    // 报告未定义的变量
    for (const usedVar of usedVars) {
      if (!definedVars.has(usedVar)) {
        result.warnings.push(`可能使用了未定义的变量: ${usedVar}`);
      }
    }
  }

  private isBuiltinOrGlobal(name: string): boolean {
    const builtins = [
      'console', 'Math', 'Date', 'Array', 'Object', 'String', 'Number', 'Boolean',
      'JSON', 'RegExp', 'Error', 'parseInt', 'parseFloat', 'isNaN', 'isFinite',
      'undefined', 'null', 'true', 'false', 'Infinity', 'NaN'
    ];
    return builtins.includes(name);
  }
}

/**
 * 安全分析器
 */
class SecurityAnalyzer {
  private dangerousPatterns: Array<{ pattern: RegExp; risk: string; severity: 'high' | 'medium' | 'low' }>;

  constructor() {
    this.dangerousPatterns = [
      { pattern: /eval\s*\(/, risk: '使用eval函数可能导致代码注入', severity: 'high' },
      { pattern: /Function\s*\(/, risk: '使用Function构造函数可能不安全', severity: 'high' },
      { pattern: /setTimeout\s*\(.*string/, risk: '使用字符串作为setTimeout参数不安全', severity: 'medium' },
      { pattern: /setInterval\s*\(.*string/, risk: '使用字符串作为setInterval参数不安全', severity: 'medium' },
      { pattern: /document\.write/, risk: 'document.write可能导致XSS', severity: 'medium' },
      { pattern: /innerHTML\s*=/, risk: '直接设置innerHTML可能导致XSS', severity: 'medium' },
      { pattern: /while\s*\(\s*true\s*\)/, risk: '无限循环可能导致程序卡死', severity: 'high' },
      { pattern: /for\s*\(\s*;\s*;\s*\)/, risk: '无限循环可能导致程序卡死', severity: 'high' },
    ];
  }

  async analyze(code: string): Promise<SecurityAnalysisResult> {
    const result: SecurityAnalysisResult = {
      safe: true,
      risks: [],
      warnings: [],
      suggestions: []
    };

    // 检查危险模式
    for (const { pattern, risk, severity } of this.dangerousPatterns) {
      if (pattern.test(code)) {
        if (severity === 'high') {
          result.safe = false;
          result.risks.push(risk);
        } else {
          result.warnings.push(risk);
        }
      }
    }

    // 检查代码复杂度
    const complexity = this.calculateComplexity(code);
    if (complexity > 20) {
      result.warnings.push('代码复杂度较高，建议简化');
    }

    // 生成安全建议
    if (code.includes('innerHTML')) {
      result.suggestions.push('建议使用textContent或安全的DOM操作方法');
    }

    if (code.includes('eval')) {
      result.suggestions.push('建议避免使用eval，考虑使用JSON.parse或其他安全方法');
    }

    return result;
  }

  private calculateComplexity(code: string): number {
    let complexity = 1; // 基础复杂度

    // 条件语句
    complexity += (code.match(/if\s*\(/g) || []).length;
    complexity += (code.match(/else\s+if/g) || []).length;
    complexity += (code.match(/switch\s*\(/g) || []).length;
    complexity += (code.match(/case\s+/g) || []).length;

    // 循环语句
    complexity += (code.match(/for\s*\(/g) || []).length;
    complexity += (code.match(/while\s*\(/g) || []).length;
    complexity += (code.match(/do\s*{/g) || []).length;

    // 逻辑操作符
    complexity += (code.match(/&&/g) || []).length;
    complexity += (code.match(/\|\|/g) || []).length;

    // 异常处理
    complexity += (code.match(/try\s*{/g) || []).length;
    complexity += (code.match(/catch\s*\(/g) || []).length;

    return complexity;
  }
}

/**
 * 代码格式化器
 */
class CodeFormatter {
  async format(code: string): Promise<string> {
    // 简单的代码格式化
    let formatted = code;

    // 添加适当的缩进
    formatted = this.addIndentation(formatted);

    // 添加分号
    formatted = this.addSemicolons(formatted);

    return formatted;
  }

  private addIndentation(code: string): string {
    const lines = code.split('\n');
    let indentLevel = 0;
    const indentSize = 2;

    return lines.map(line => {
      const trimmed = line.trim();
      if (!trimmed) return '';

      // 减少缩进
      if (trimmed.startsWith('}') || trimmed.startsWith(']') || trimmed.startsWith(')'))) {
        indentLevel = Math.max(0, indentLevel - 1);
      }

      const indented = ' '.repeat(indentLevel * indentSize) + trimmed;

      // 增加缩进
      if (trimmed.endsWith('{') || trimmed.endsWith('[') || trimmed.endsWith('(')) {
        indentLevel++;
      }

      return indented;
    }).join('\n');
  }

  private addSemicolons(code: string): string {
    // 简单的分号添加逻辑
    return code.replace(/([^;{}\s])\s*\n/g, '$1;\n');
  }
}

/**
 * 验证选项
 */
export interface ValidationOptions {
  format?: boolean;
  strictMode?: boolean;
  allowDangerousPatterns?: boolean;
}

/**
 * 验证结果
 */
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
  processedCode: string;
}

/**
 * 语法检查结果
 */
interface SyntaxCheckResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

/**
 * 安全分析结果
 */
interface SecurityAnalysisResult {
  safe: boolean;
  risks: string[];
  warnings: string[];
  suggestions: string[];
}
