{"name": "obsidian-ai-coach-advanced", "version": "0.3.0", "description": "基于LLM的Obsidian智能助手插件，支持自然语言交互、Vault知识库查询和工具调用", "main": "main.js", "scripts": {"dev": "node esbuild.config.mjs", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "build": "node scripts/build.js", "build:prod": "node scripts/build.js --production", "package": "npm run build:prod && node scripts/package.js", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write src/**/*.ts"}, "keywords": ["obsidian", "plugin", "ai", "llm", "assistant", "knowledge-base", "automation"], "author": "AI Coach Team", "license": "MIT", "devDependencies": {"@types/jest": "^29.5.5", "@types/node": "^20.6.0", "archiver": "^6.0.1", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "builtin-modules": "^3.3.0", "esbuild": "^0.19.4", "eslint": "^8.49.0", "jest": "^29.7.0", "obsidian": "latest", "prettier": "^3.0.3", "ts-jest": "^29.1.1", "tslib": "^2.6.2", "typescript": "^5.2.2"}, "dependencies": {"openai": "^4.20.1", "@google/generative-ai": "^0.1.3"}}