/**
 * 测试调试日志输出
 */

// 模拟 Obsidian API
const mockObsidian = {
  Plugin: class Plugin {
    constructor(app, manifest) {
      this.app = app;
      this.manifest = manifest;
      this.savedData = null;
    }
    
    async loadData() {
      return this.savedData;
    }
    
    async saveData(data) {
      this.savedData = data;
    }
    
    addCommand() {}
    addSettingTab() {}
    addStatusBarItem() {
      return {
        createSpan: () => ({ innerHTML: '' }),
        createDiv: () => ({ createDiv: () => ({}) }),
        empty: () => {},
        addClass: () => {},
        addEventListener: () => {},
        removeEventListener: () => {},
        style: {}
      };
    }
    registerEvent() {}
  },
  
  Notice: class Notice {
    constructor(message, timeout) {
      console.log('📢 Obsidian通知:', message);
    }
  },
  
  TFile: class TFile {
    constructor(path) {
      this.path = path;
      this.name = path.split('/').pop();
      this.extension = path.split('.').pop();
    }
  },
  
  Modal: class Modal {
    constructor(app) {
      this.app = app;
      this.contentEl = {
        empty: () => {},
        createEl: () => ({}),
        createDiv: () => ({
          createDiv: () => ({}),
          createEl: () => ({})
        })
      };
    }
    
    open() {
      console.log('📱 模态窗口已打开');
    }
    
    close() {
      console.log('📱 模态窗口已关闭');
    }
    
    onOpen() {}
    onClose() {}
  },
  
  Setting: class Setting {
    constructor(containerEl) {
      this.containerEl = containerEl;
    }
    
    setName() { return this; }
    setDesc() { return this; }
    addText() { return this; }
    addTextArea(callback) { 
      const mockTextArea = {
        inputEl: {
          style: {},
          placeholder: '',
          addEventListener: () => {}
        },
        getValue: () => 'test message',
        setValue: () => {}
      };
      if (callback) callback(mockTextArea);
      return this; 
    }
    addButton(callback) { 
      const mockButton = {
        setButtonText: () => mockButton,
        setCta: () => mockButton,
        onClick: () => mockButton,
        setDisabled: () => mockButton
      };
      if (callback) callback(mockButton);
      return this; 
    }
  },
  
  PluginSettingTab: class PluginSettingTab {
    constructor(app, plugin) {
      this.app = app;
      this.plugin = plugin;
    }
  }
};

// 模拟 App
const mockApp = {
  vault: {
    getMarkdownFiles: () => [
      new mockObsidian.TFile('test.md')
    ],
    read: async (file) => '# Test Note\n\nThis is a test note.',
    adapter: {
      exists: async () => true,
      read: async () => '{}',
      write: async () => {},
      mkdir: async () => {},
      stat: async () => ({ mtime: Date.now() })
    },
    on: () => {}
  },
  
  metadataCache: {
    getFileCache: () => ({
      headings: [],
      links: [],
      tags: []
    })
  },
  
  plugins: {
    plugins: {},
    enabledPlugins: new Set()
  },
  
  commands: {
    commands: {}
  }
};

// 模拟插件清单
const mockManifest = {
  id: 'obsidian-ai-coach-advanced',
  name: 'AI Coach Advanced',
  version: '0.2.3'
};

// 设置全局变量
global.require = () => mockObsidian;

async function testDebugLogs() {
  console.log('🧪 开始测试调试日志输出...\n');
  
  try {
    // 加载构建后的插件代码
    const fs = require('fs');
    const path = require('path');
    
    const pluginCode = fs.readFileSync(path.join(__dirname, 'dist', 'main.js'), 'utf8');
    
    // 创建执行环境
    const vm = require('vm');
    const context = {
      console,
      require: () => mockObsidian,
      module: { exports: {} },
      exports: {},
      global: {},
      Buffer,
      process: { env: { NODE_ENV: 'test' } },
      setTimeout,
      clearTimeout,
      setInterval,
      clearInterval,
      btoa: (str) => Buffer.from(str).toString('base64'),
      atob: (str) => Buffer.from(str, 'base64').toString(),
      fetch: async (url, options) => {
        console.log('🌐 模拟网络请求:', url);
        return {
          ok: true,
          json: async () => ({
            choices: [{
              message: {
                content: 'This is a test response from the LLM.'
              }
            }]
          })
        };
      }
    };
    
    // 执行插件代码
    vm.createContext(context);
    vm.runInContext(pluginCode, context);
    
    const PluginClass = context.module.exports.default || context.module.exports;
    const plugin = new PluginClass(mockApp, mockManifest);
    
    console.log('='.repeat(60));
    console.log('🚀 开始测试插件加载过程...');
    console.log('='.repeat(60));
    
    // 测试插件加载
    await plugin.onload();
    
    console.log('\n' + '='.repeat(60));
    console.log('💬 开始测试聊天功能...');
    console.log('='.repeat(60));
    
    // 等待一下确保初始化完成
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 测试打开聊天窗口
    await plugin.openChatModal();
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 测试结果分析');
    console.log('='.repeat(60));
    
    console.log('✅ 如果你看到了上面的详细日志输出，说明调试功能正常工作');
    console.log('✅ 插件加载过程的每个步骤都有相应的日志');
    console.log('✅ 聊天功能的调用也有详细的跟踪日志');
    
    console.log('\n📋 接下来在实际使用中：');
    console.log('1. 安装最新版本的插件');
    console.log('2. 打开浏览器开发者工具 (Ctrl+Shift+I)');
    console.log('3. 切换到 Console 标签页');
    console.log('4. 打开AI助手对话窗口');
    console.log('5. 发送消息，观察详细的调试输出');
    
    console.log('\n🎉 调试日志测试完成！');
    return true;
    
  } catch (error) {
    console.error('\n❌ 调试日志测试失败:');
    console.error('错误类型:', error.constructor.name);
    console.error('错误消息:', error.message);
    console.error('错误堆栈:', error.stack);
    return false;
  }
}

// 运行测试
if (require.main === module) {
  testDebugLogs().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { testDebugLogs };
