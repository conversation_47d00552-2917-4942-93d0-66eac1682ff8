# Obsidian AI Coach Advanced

基于LLM的Obsidian智能助手插件，支持自然语言交互、Vault知识库查询和工具调用等高级功能。

## 功能特性

- 🤖 **智能对话**: 支持自然语言指令，理解复杂任务并自动拆解执行
- 📚 **知识库查询**: 深度集成Vault内容，支持语义搜索和智能问答
- 🔧 **工具调用**: 集成多种工具，包括插件管理、网络搜索、JS脚本执行等
- 🧠 **记忆系统**: 维护对话上下文和用户偏好，提供个性化服务
- 🔒 **安全可靠**: 沙箱执行环境，严格的权限控制和数据保护

## 项目结构

```
src/
├── core/                 # 核心模块
│   ├── llm/             # LLM接口层
│   ├── orchestration/   # LLM编排核心
│   ├── memory/          # 记忆系统
│   └── config/          # 配置管理
├── tools/               # 工具集
│   ├── vault/           # Vault知识库工具
│   ├── web/             # 网络搜索工具
│   ├── plugin/          # 插件管理工具
│   └── javascript/      # JS执行工具
├── ui/                  # 用户界面
│   ├── modals/          # 模态框
│   ├── settings/        # 设置页面
│   └── components/      # UI组件
├── utils/               # 工具函数
│   ├── prompts/         # 提示工程
│   ├── security/        # 安全相关
│   └── validation/      # 数据验证
├── types/               # 类型定义
└── __tests__/           # 测试文件
```

## 开发指南

### 环境要求

- Node.js 18+
- TypeScript 5.0+
- Obsidian 0.15.0+

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建

```bash
npm run build
```

### 测试

```bash
npm test
```

### 代码规范

```bash
npm run lint
npm run format
```

## 配置说明

插件支持多种LLM提供商：

- OpenAI (GPT-3.5, GPT-4)
- Google Gemini
- DeepSeek
- 其他OpenAI兼容接口

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
