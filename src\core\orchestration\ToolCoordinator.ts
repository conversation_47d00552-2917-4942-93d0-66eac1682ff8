import { ToolRegistry } from '@/core/tools/ToolRegistry';
import { ToolResult, ToolExecutionContext } from '@/types/tools';
import { ExecutionStep } from './TaskPlanner';

/**
 * 工具调用协调器
 * 负责协调多个工具的调用，处理工具间的依赖关系和结果传递
 */
export class ToolCoordinator {
  private toolRegistry: ToolRegistry;
  private executionHistory: ToolExecutionRecord[] = [];
  private maxHistorySize: number = 100;

  constructor(toolRegistry: ToolRegistry) {
    this.toolRegistry = toolRegistry;
  }

  /**
   * 协调执行工具调用
   */
  async coordinateExecution(
    toolCalls: ToolCall[],
    context?: ToolExecutionContext
  ): Promise<CoordinationResult> {
    const startTime = Date.now();
    const executionLog: ExecutionStep[] = [];
    const results: ToolResult[] = [];
    const toolsUsed: string[] = [];

    try {
      // 分析工具依赖关系
      const executionPlan = this.planExecution(toolCalls);
      
      executionLog.push({
        type: 'action',
        content: `规划执行 ${toolCalls.length} 个工具调用`,
        timestamp: new Date()
      });

      // 按计划执行工具
      for (const phase of executionPlan) {
        const phaseResults = await this.executePhase(phase, context, executionLog);
        results.push(...phaseResults);
        
        // 更新上下文，将前一阶段的结果传递给下一阶段
        if (context) {
          context.previousResults = phaseResults;
        }
      }

      // 记录使用的工具
      toolCalls.forEach(call => {
        if (!toolsUsed.includes(call.toolName)) {
          toolsUsed.push(call.toolName);
        }
      });

      // 记录执行历史
      this.recordExecution({
        toolCalls,
        results,
        executionTime: Date.now() - startTime,
        success: results.every(r => r.success),
        timestamp: new Date()
      });

      return {
        success: true,
        results,
        executionLog,
        toolsUsed,
        executionTime: Date.now() - startTime,
        coordination: {
          totalPhases: executionPlan.length,
          parallelExecutions: executionPlan.reduce((sum, phase) => sum + phase.length, 0)
        }
      };

    } catch (error) {
      executionLog.push({
        type: 'error',
        content: `工具协调执行失败: ${error.message}`,
        timestamp: new Date()
      });

      return {
        success: false,
        results,
        executionLog,
        toolsUsed,
        executionTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * 规划执行顺序
   */
  private planExecution(toolCalls: ToolCall[]): ToolCall[][] {
    const phases: ToolCall[][] = [];
    const processed = new Set<string>();
    const remaining = [...toolCalls];

    while (remaining.length > 0) {
      const currentPhase: ToolCall[] = [];
      
      // 找出当前可以执行的工具（没有未满足的依赖）
      for (let i = remaining.length - 1; i >= 0; i--) {
        const toolCall = remaining[i];
        
        if (this.canExecuteNow(toolCall, processed)) {
          currentPhase.push(toolCall);
          remaining.splice(i, 1);
          processed.add(toolCall.id);
        }
      }

      if (currentPhase.length === 0) {
        // 如果没有可执行的工具，说明存在循环依赖或无法解决的依赖
        throw new Error('检测到循环依赖或无法解决的工具依赖关系');
      }

      phases.push(currentPhase);
    }

    return phases;
  }

  /**
   * 检查工具是否可以立即执行
   */
  private canExecuteNow(toolCall: ToolCall, processed: Set<string>): boolean {
    if (!toolCall.dependencies || toolCall.dependencies.length === 0) {
      return true;
    }

    return toolCall.dependencies.every(dep => processed.has(dep));
  }

  /**
   * 执行一个阶段的工具调用
   */
  private async executePhase(
    phase: ToolCall[],
    context?: ToolExecutionContext,
    executionLog?: ExecutionStep[]
  ): Promise<ToolResult[]> {
    const phaseResults: ToolResult[] = [];

    // 并行执行同一阶段的工具
    const promises = phase.map(async (toolCall) => {
      try {
        executionLog?.push({
          type: 'action',
          content: `执行工具: ${toolCall.toolName}`,
          timestamp: new Date(),
          toolName: toolCall.toolName
        });

        // 处理参数中的引用
        const processedArgs = this.processArguments(toolCall.arguments, context);
        
        const result = await this.toolRegistry.execute(
          toolCall.toolName,
          processedArgs,
          context
        );

        executionLog?.push({
          type: 'tool_result',
          content: `工具 ${toolCall.toolName} 执行${result.success ? '成功' : '失败'}`,
          timestamp: new Date(),
          toolName: toolCall.toolName,
          toolResult: result
        });

        return result;
      } catch (error) {
        const errorResult: ToolResult = {
          success: false,
          error: `工具 ${toolCall.toolName} 执行失败: ${error.message}`
        };

        executionLog?.push({
          type: 'error',
          content: errorResult.error!,
          timestamp: new Date(),
          toolName: toolCall.toolName
        });

        return errorResult;
      }
    });

    const results = await Promise.all(promises);
    phaseResults.push(...results);

    return phaseResults;
  }

  /**
   * 处理参数中的引用
   */
  private processArguments(args: any, context?: ToolExecutionContext): any {
    if (!context?.previousResults || !args) {
      return args;
    }

    // 深拷贝参数以避免修改原始对象
    const processedArgs = JSON.parse(JSON.stringify(args));

    // 递归处理参数中的引用
    this.resolveReferences(processedArgs, context.previousResults);

    return processedArgs;
  }

  /**
   * 解析参数中的引用
   */
  private resolveReferences(obj: any, previousResults: ToolResult[]): void {
    if (typeof obj === 'string' && obj.startsWith('$ref:')) {
      // 处理引用格式: $ref:toolName.path.to.value
      const refPath = obj.substring(5);
      const [toolName, ...pathParts] = refPath.split('.');
      
      const result = previousResults.find(r => r.metadata?.toolName === toolName);
      if (result && result.data) {
        let value = result.data;
        for (const part of pathParts) {
          value = value[part];
          if (value === undefined) break;
        }
        return value;
      }
    } else if (typeof obj === 'object' && obj !== null) {
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const resolved = this.resolveReferences(obj[key], previousResults);
          if (resolved !== undefined) {
            obj[key] = resolved;
          }
        }
      }
    }
  }

  /**
   * 记录执行历史
   */
  private recordExecution(record: ToolExecutionRecord): void {
    this.executionHistory.push(record);
    
    // 限制历史记录大小
    if (this.executionHistory.length > this.maxHistorySize) {
      this.executionHistory = this.executionHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * 获取执行统计
   */
  getExecutionStats(): ToolCoordinationStats {
    const total = this.executionHistory.length;
    const successful = this.executionHistory.filter(r => r.success).length;
    const avgExecutionTime = total > 0 
      ? this.executionHistory.reduce((sum, r) => sum + r.executionTime, 0) / total 
      : 0;

    const toolUsage: Record<string, number> = {};
    this.executionHistory.forEach(record => {
      record.toolCalls.forEach(call => {
        toolUsage[call.toolName] = (toolUsage[call.toolName] || 0) + 1;
      });
    });

    return {
      totalExecutions: total,
      successfulExecutions: successful,
      failedExecutions: total - successful,
      averageExecutionTime: Math.round(avgExecutionTime),
      toolUsage,
      mostUsedTool: Object.entries(toolUsage)
        .sort(([, a], [, b]) => b - a)[0]?.[0] || 'none'
    };
  }

  /**
   * 清理执行历史
   */
  clearExecutionHistory(): void {
    this.executionHistory = [];
  }
}

/**
 * 工具调用定义
 */
export interface ToolCall {
  id: string;
  toolName: string;
  arguments: any;
  dependencies?: string[]; // 依赖的其他工具调用ID
  priority?: number; // 优先级，数字越大优先级越高
}

/**
 * 协调结果
 */
export interface CoordinationResult {
  success: boolean;
  results: ToolResult[];
  executionLog: ExecutionStep[];
  toolsUsed: string[];
  executionTime: number;
  error?: string;
  coordination?: {
    totalPhases: number;
    parallelExecutions: number;
  };
}

/**
 * 工具执行记录
 */
export interface ToolExecutionRecord {
  toolCalls: ToolCall[];
  results: ToolResult[];
  executionTime: number;
  success: boolean;
  timestamp: Date;
}

/**
 * 工具协调统计
 */
export interface ToolCoordinationStats {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  toolUsage: Record<string, number>;
  mostUsedTool: string;
}
