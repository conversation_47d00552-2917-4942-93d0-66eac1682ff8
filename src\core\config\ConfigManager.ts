import { Plugin } from 'obsidian';
import { PluginConfig, DEFAULT_CONFIG } from '@/types/config';

export class ConfigManager {
  private plugin: Plugin;
  private config: PluginConfig;

  constructor(plugin: Plugin) {
    this.plugin = plugin;
    this.config = DEFAULT_CONFIG;
  }

  async loadConfig(): Promise<PluginConfig> {
    try {
      const data = await this.plugin.loadData();
      if (data) {
        this.config = { ...DEFAULT_CONFIG, ...data };
      }
    } catch (error) {
      console.error('Failed to load config:', error);
    }
    return this.config;
  }

  async saveConfig(): Promise<void> {
    try {
      await this.plugin.saveData(this.config);
    } catch (error) {
      console.error('Failed to save config:', error);
      throw error;
    }
  }

  getConfig(): PluginConfig {
    return this.config;
  }

  updateConfig(updates: Partial<PluginConfig>): void {
    this.config = { ...this.config, ...updates };
  }
}
