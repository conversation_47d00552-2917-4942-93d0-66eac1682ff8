import { Plugin, Notice } from 'obsidian';
import { PluginConfig, DEFAULT_CONFIG, LLMConfig, ToolsConfig, UIConfig, SecurityConfig } from '@/types/config';
import { LLMFactory } from '@/core/llm/LLMFactory';

export class ConfigManager {
  private plugin: Plugin;
  private config: PluginConfig;
  private configVersion = '1.0.0';
  private encryptionKey: string | null = null;

  constructor(plugin: Plugin) {
    this.plugin = plugin;
    this.config = this.deepClone(DEFAULT_CONFIG);
  }

  async loadConfig(): Promise<PluginConfig> {
    try {
      const data = await this.plugin.loadData();
      if (data) {
        // 检查配置版本兼容性
        if (data.version && data.version !== this.configVersion) {
          await this.migrateConfig(data);
        } else {
          this.config = this.mergeConfig(DEFAULT_CONFIG, data);
        }

        // 解密敏感信息
        if (this.config.security.encryptApiKeys) {
          await this.decryptSensitiveData();
        }

        // 验证配置
        this.validateConfig();
      }
    } catch (error) {
      console.error('Failed to load config:', error);
      new Notice('配置加载失败，使用默认配置');
      this.config = this.deepClone(DEFAULT_CONFIG);
    }
    return this.config;
  }

  async saveConfig(): Promise<void> {
    try {
      // 验证配置
      this.validateConfig();

      // 准备保存的配置
      const configToSave = this.deepClone(this.config);
      configToSave.version = this.configVersion;
      configToSave.lastUpdated = new Date().toISOString();

      // 加密敏感信息
      if (this.config.security.encryptApiKeys) {
        await this.encryptSensitiveData(configToSave);
      }

      await this.plugin.saveData(configToSave);
    } catch (error) {
      console.error('Failed to save config:', error);
      new Notice('配置保存失败: ' + error.message);
      throw error;
    }
  }

  getConfig(): PluginConfig {
    return this.deepClone(this.config);
  }

  updateConfig(updates: Partial<PluginConfig>): void {
    this.config = this.mergeConfig(this.config, updates);
  }

  // LLM配置相关方法
  updateLLMConfig(updates: Partial<LLMConfig>): void {
    this.config.llm = { ...this.config.llm, ...updates };
  }

  getLLMConfig(): LLMConfig {
    return this.deepClone(this.config.llm);
  }

  async validateLLMConfig(): Promise<{ valid: boolean; errors: string[] }> {
    return LLMFactory.validateConfig(this.config.llm);
  }

  // 工具配置相关方法
  updateToolsConfig(updates: Partial<ToolsConfig>): void {
    this.config.tools = this.mergeConfig(this.config.tools, updates);
  }

  getToolsConfig(): ToolsConfig {
    return this.deepClone(this.config.tools);
  }

  isToolEnabled(toolName: keyof ToolsConfig): boolean {
    return this.config.tools[toolName]?.enabled || false;
  }

  // UI配置相关方法
  updateUIConfig(updates: Partial<UIConfig>): void {
    this.config.ui = { ...this.config.ui, ...updates };
  }

  getUIConfig(): UIConfig {
    return this.deepClone(this.config.ui);
  }

  // 安全配置相关方法
  updateSecurityConfig(updates: Partial<SecurityConfig>): void {
    this.config.security = { ...this.config.security, ...updates };
  }

  getSecurityConfig(): SecurityConfig {
    return this.deepClone(this.config.security);
  }

  // 重置配置
  async resetConfig(): Promise<void> {
    this.config = this.deepClone(DEFAULT_CONFIG);
    await this.saveConfig();
    new Notice('配置已重置为默认值');
  }

  // 导出配置
  exportConfig(): string {
    const exportData = this.deepClone(this.config);
    // 移除敏感信息
    exportData.llm.apiKey = '';
    exportData.tools.web.apiKey = '';
    return JSON.stringify(exportData, null, 2);
  }

  // 导入配置
  async importConfig(configJson: string): Promise<void> {
    try {
      const importedConfig = JSON.parse(configJson);

      // 验证导入的配置
      if (!this.isValidConfigStructure(importedConfig)) {
        throw new Error('无效的配置格式');
      }

      // 合并配置（保留当前的敏感信息）
      const currentApiKey = this.config.llm.apiKey;
      const currentWebApiKey = this.config.tools.web.apiKey;

      this.config = this.mergeConfig(DEFAULT_CONFIG, importedConfig);

      // 恢复敏感信息
      if (!this.config.llm.apiKey) {
        this.config.llm.apiKey = currentApiKey;
      }
      if (!this.config.tools.web.apiKey) {
        this.config.tools.web.apiKey = currentWebApiKey;
      }

      await this.saveConfig();
      new Notice('配置导入成功');
    } catch (error) {
      console.error('Failed to import config:', error);
      new Notice('配置导入失败: ' + error.message);
      throw error;
    }
  }

  private validateConfig(): void {
    // 验证LLM配置（仅在有API密钥时进行严格验证）
    if (this.config.llm.apiKey && this.config.llm.apiKey.trim()) {
      const llmValidation = LLMFactory.validateConfig(this.config.llm);
      if (!llmValidation.valid) {
        console.warn('LLM配置验证失败:', llmValidation.errors);
      }
    }

    // 验证其他配置项
    if (this.config.tools.vault.chunkSize < 100) {
      this.config.tools.vault.chunkSize = 1000;
    }

    if (this.config.tools.javascript.timeout < 1000) {
      this.config.tools.javascript.timeout = 5000;
    }

    if (this.config.security.maxRequestsPerMinute < 1) {
      this.config.security.maxRequestsPerMinute = 60;
    }
  }

  private async migrateConfig(oldConfig: any): Promise<void> {
    console.log('Migrating config from version', oldConfig.version, 'to', this.configVersion);

    // 这里可以添加版本迁移逻辑
    // 目前直接合并配置
    this.config = this.mergeConfig(DEFAULT_CONFIG, oldConfig);

    new Notice('配置已升级到新版本');
  }

  private async encryptSensitiveData(config: any): Promise<void> {
    // 这里应该实现真正的加密逻辑
    // 目前只是简单的base64编码作为占位符
    if (config.llm.apiKey) {
      config.llm.apiKey = btoa(config.llm.apiKey);
    }
    if (config.tools.web.apiKey) {
      config.tools.web.apiKey = btoa(config.tools.web.apiKey);
    }
  }

  private async decryptSensitiveData(): Promise<void> {
    // 这里应该实现真正的解密逻辑
    // 目前只是简单的base64解码作为占位符
    try {
      if (this.config.llm.apiKey) {
        this.config.llm.apiKey = atob(this.config.llm.apiKey);
      }
      if (this.config.tools.web.apiKey) {
        this.config.tools.web.apiKey = atob(this.config.tools.web.apiKey);
      }
    } catch (error) {
      console.error('Failed to decrypt sensitive data:', error);
    }
  }

  private isValidConfigStructure(config: any): boolean {
    return (
      config &&
      typeof config === 'object' &&
      config.llm &&
      config.tools &&
      config.ui &&
      config.security
    );
  }

  private deepClone<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj));
  }

  private mergeConfig<T extends Record<string, any>>(target: T, source: Partial<T>): T {
    const result = { ...target };

    for (const key in source) {
      if (source[key] !== undefined) {
        if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
          result[key] = this.mergeConfig(result[key] || {}, source[key]);
        } else {
          result[key] = source[key] as T[Extract<keyof T, string>];
        }
      }
    }

    return result;
  }
}
