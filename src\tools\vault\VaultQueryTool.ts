import { App, TFile } from 'obsidian';
import { BaseTool, ToolConfig } from '@/core/tools/BaseTool';
import { ToolResult, ToolExecutionContext, VaultQueryResult } from '@/types/tools';
import { VaultFile, VaultSearchOptions } from '@/types/vault';
import { VaultIndexer } from './VaultIndexer';

/**
 * Vault查询工具
 * 提供Vault内容的搜索和查询功能
 */
export class VaultQueryTool extends BaseTool {
  private app: App;
  private indexer: VaultIndexer;
  private initialized: boolean = false;

  constructor(app: App) {
    const config: ToolConfig = {
      name: 'vault_query',
      description: '搜索和查询Vault中的笔记内容',
      category: 'vault',
      version: '1.0.0',
      parameters: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: '搜索查询字符串'
          },
          maxResults: {
            type: 'number',
            description: '最大返回结果数',
            default: 10,
            minimum: 1,
            maximum: 50
          },
          fileTypes: {
            type: 'array',
            description: '文件类型过滤',
            items: { type: 'string' },
            default: ['md']
          },
          tags: {
            type: 'array',
            description: '标签过滤',
            items: { type: 'string' }
          },
          includeContent: {
            type: 'boolean',
            description: '是否包含文件内容',
            default: true
          },
          threshold: {
            type: 'number',
            description: '相似度阈值',
            default: 0.3,
            minimum: 0,
            maximum: 1
          }
        },
        required: ['query']
      },
      permissions: {
        required: ['vault_read'],
        optional: [],
        dangerous: false,
        requiresConfirmation: false
      },
      metadata: {
        author: 'AI Coach Team',
        tags: ['vault', 'search', 'query'],
        documentation: '在Obsidian Vault中搜索和查询笔记内容',
        examples: [
          {
            name: '基础搜索',
            description: '搜索包含特定关键词的笔记',
            input: { query: 'machine learning' },
            expectedOutput: { results: [] }
          },
          {
            name: '标签过滤搜索',
            description: '搜索特定标签的笔记',
            input: { query: 'neural networks', tags: ['ai', 'research'] },
            expectedOutput: { results: [] }
          }
        ]
      }
    };

    super(config);
    this.app = app;
    this.indexer = new VaultIndexer(app);
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // 初始化索引
      await this.buildInitialIndex();
      this.initialized = true;
      console.log('VaultQueryTool initialized successfully');
    } catch (error) {
      console.error('Failed to initialize VaultQueryTool:', error);
      throw error;
    }
  }

  protected async executeInternal(
    args: Record<string, any>,
    context?: ToolExecutionContext
  ): Promise<ToolResult> {
    try {
      // 确保工具已初始化
      if (!this.initialized) {
        await this.initialize();
      }

      const {
        query,
        maxResults = 10,
        fileTypes = ['md'],
        tags,
        includeContent = true,
        threshold = 0.3
      } = args;

      const searchOptions: VaultSearchOptions = {
        query,
        maxResults,
        threshold,
        includeContent,
        fileTypes,
        tags
      };

      const results = await this.searchVault(searchOptions);

      return {
        success: true,
        data: {
          query,
          results,
          totalResults: results.length,
          searchOptions
        },
        metadata: {
          source: 'vault_indexer',
          timestamp: new Date().toISOString(),
          indexStats: await this.indexer.getStats()
        }
      };

    } catch (error) {
      return {
        success: false,
        error: `Vault查询失败: ${error.message}`,
        metadata: {
          errorType: 'vault_query_error'
        }
      };
    }
  }

  /**
   * 搜索Vault内容
   */
  private async searchVault(options: VaultSearchOptions): Promise<VaultQueryResult[]> {
    // 首先进行基础搜索
    const basicResults = await this.indexer.search(options);

    // 如果启用了语义搜索，进行语义搜索并合并结果
    let semanticResults: any[] = [];
    if (options.useSemanticSearch !== false) {
      try {
        semanticResults = await this.performSemanticSearch(options);
      } catch (error) {
        console.warn('Semantic search failed, falling back to basic search:', error);
      }
    }

    // 合并和去重结果
    const combinedResults = this.mergeSearchResults(basicResults, semanticResults);

    return combinedResults.map(result => ({
      content: result.content,
      file: result.file?.path || result.filePath,
      score: result.score,
      metadata: {
        highlights: result.highlights || this.generateHighlights(result.content, options.query),
        searchType: result.searchType || 'basic',
        ...result.metadata
      }
    }));
  }

  /**
   * 执行语义搜索
   */
  private async performSemanticSearch(options: VaultSearchOptions): Promise<any[]> {
    if (!this.embeddingService) {
      return [];
    }

    try {
      // 生成查询向量
      const queryEmbedding = await this.embeddingService.generateEmbedding(options.query);

      // 搜索相似文档块
      const searchResults = await this.vectorStore.search(
        queryEmbedding,
        options.maxResults * 2,
        options.threshold
      );

      return searchResults.map(result => ({
        ...result,
        searchType: 'semantic',
        filePath: result.metadata?.file || result.file
      }));
    } catch (error) {
      console.error('Semantic search error:', error);
      return [];
    }
  }

  /**
   * 合并搜索结果
   */
  private mergeSearchResults(basicResults: any[], semanticResults: any[]): any[] {
    const resultMap = new Map<string, any>();

    // 添加基础搜索结果
    for (const result of basicResults) {
      const key = `${result.file?.path || result.filePath}_${result.chunkIndex || 0}`;
      resultMap.set(key, {
        ...result,
        searchType: 'basic'
      });
    }

    // 添加语义搜索结果，如果已存在则合并分数
    for (const result of semanticResults) {
      const key = `${result.filePath}_${result.metadata?.chunkIndex || 0}`;
      const existing = resultMap.get(key);

      if (existing) {
        // 合并分数，语义搜索权重稍高
        existing.score = Math.max(existing.score, result.score * 1.1);
        existing.searchType = 'hybrid';
      } else {
        resultMap.set(key, result);
      }
    }

    // 按分数排序并返回
    return Array.from(resultMap.values())
      .sort((a, b) => b.score - a.score)
      .slice(0, 20); // 限制结果数量
  }

  /**
   * 生成高亮片段
   */
  private generateHighlights(content: string, query: string): string[] {
    if (!content || !query) return [];

    const highlights: string[] = [];
    const queryTerms = query.toLowerCase().split(/\s+/);
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 10);

    for (const sentence of sentences) {
      const lowerSentence = sentence.toLowerCase();
      let relevanceScore = 0;

      for (const term of queryTerms) {
        if (lowerSentence.includes(term)) {
          relevanceScore += term.length;
        }
      }

      if (relevanceScore > 0) {
        highlights.push(sentence.trim());
      }

      if (highlights.length >= 3) {
        break;
      }
    }

    return highlights;
  }

  /**
   * 构建初始索引
   */
  private async buildInitialIndex(): Promise<void> {
    const files = this.app.vault.getMarkdownFiles();
    const vaultFiles: VaultFile[] = [];

    for (const file of files) {
      try {
        const content = await this.app.vault.read(file);
        const vaultFile: VaultFile = {
          path: file.path,
          name: file.name,
          extension: file.extension,
          size: file.stat.size,
          mtime: file.stat.mtime,
          content
        };
        vaultFiles.push(vaultFile);
      } catch (error) {
        console.warn(`Failed to read file ${file.path}:`, error);
      }
    }

    await this.indexer.indexFiles(vaultFiles);
  }

  /**
   * 重建索引
   */
  async rebuildIndex(): Promise<void> {
    this.indexer.clearAllIndices();
    await this.buildInitialIndex();
  }

  /**
   * 更新文件索引
   */
  async updateFileIndex(filePath: string): Promise<void> {
    await this.indexer.updateIndex(filePath);
  }

  /**
   * 删除文件索引
   */
  async deleteFileIndex(filePath: string): Promise<void> {
    await this.indexer.deleteIndex(filePath);
  }

  /**
   * 获取相关文件
   */
  async getRelatedFiles(filePath: string, maxResults: number = 5): Promise<VaultQueryResult[]> {
    try {
      const file = this.app.vault.getAbstractFileByPath(filePath);
      if (!(file instanceof TFile)) {
        throw new Error(`File not found: ${filePath}`);
      }

      const content = await this.app.vault.read(file);
      
      // 提取关键词作为查询
      const keywords = this.extractKeywords(content);
      const query = keywords.slice(0, 5).join(' ');

      const searchOptions: VaultSearchOptions = {
        query,
        maxResults: maxResults + 1, // +1 因为可能包含原文件
        threshold: 0.2,
        includeContent: true,
        fileTypes: ['md']
      };

      const results = await this.searchVault(searchOptions);
      
      // 过滤掉原文件
      return results.filter(result => result.file !== filePath).slice(0, maxResults);

    } catch (error) {
      console.error(`Failed to get related files for ${filePath}:`, error);
      return [];
    }
  }

  /**
   * 提取关键词
   */
  private extractKeywords(content: string): string[] {
    // 简单的关键词提取
    const words = content
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3);

    // 统计词频
    const wordCount: Record<string, number> = {};
    words.forEach(word => {
      wordCount[word] = (wordCount[word] || 0) + 1;
    });

    // 按频率排序并返回前N个
    return Object.entries(wordCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);
  }

  /**
   * 搜索标签
   */
  async searchByTags(tags: string[], maxResults: number = 10): Promise<VaultQueryResult[]> {
    const searchOptions: VaultSearchOptions = {
      query: tags.join(' '),
      maxResults,
      threshold: 0.1,
      includeContent: true,
      tags,
      fileTypes: ['md']
    };

    return await this.searchVault(searchOptions);
  }

  /**
   * 搜索链接
   */
  async searchByLinks(linkText: string, maxResults: number = 10): Promise<VaultQueryResult[]> {
    const searchOptions: VaultSearchOptions = {
      query: `[[${linkText}]]`,
      maxResults,
      threshold: 0.5,
      includeContent: true,
      fileTypes: ['md']
    };

    return await this.searchVault(searchOptions);
  }

  /**
   * 获取索引统计信息
   */
  async getIndexStats(): Promise<any> {
    return await this.indexer.getStats();
  }

  /**
   * 智能搜索建议
   */
  async getSearchSuggestions(partialQuery: string): Promise<string[]> {
    // 基于现有内容生成搜索建议
    const allIndices = this.indexer.getAllIndices();
    const suggestions: Set<string> = new Set();

    for (const fileIndices of allIndices.values()) {
      for (const index of fileIndices) {
        // 提取包含部分查询的短语
        const content = index.content.toLowerCase();
        const queryLower = partialQuery.toLowerCase();
        
        if (content.includes(queryLower)) {
          const words = content.split(/\s+/);
          for (let i = 0; i < words.length - 1; i++) {
            const phrase = words.slice(i, i + 3).join(' ');
            if (phrase.includes(queryLower) && phrase.length > partialQuery.length) {
              suggestions.add(phrase);
            }
          }
        }
      }
    }

    return Array.from(suggestions).slice(0, 10);
  }
}
